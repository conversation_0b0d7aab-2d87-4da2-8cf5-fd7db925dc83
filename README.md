# 兼职工作管理微信小程序

## 项目概述

这是一个专为兼职工作者设计的工作记录与时间管理微信小程序，帮助用户统一管理多份兼职工作记录，自动化薪资计算，可视化工作安排，数据化收入分析。

## 功能特性

### 核心功能
- **工作记录管理**：单日/多日工作记录，支持多种工作类型
- **日历视图**：月度日历展示，工作类型色彩区分
- **智能薪资计算**：支持时薪、日薪、项目薪资多种计算方式
- **统计分析**：收入统计、工时分析、趋势图表

### 目标用户
- 大学生兼职群体
- 自由职业者
- 灵活就业人员

## 技术架构

### 技术栈
- **框架**：微信小程序原生开发框架
- **样式**：WXSS + 原子化CSS设计
- **数据存储**：微信小程序本地存储API
- **架构模式**：分层设计（页面层、组件层、工具层、数据层）

### 项目结构

```
PartTimeJob/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序全局配置
├── app.wxss               # 小程序全局样式
├── project.config.json    # 项目配置文件
├── sitemap.json          # 站点地图配置
├── pages/                # 页面目录
│   ├── home/             # 首页（日历视图）
│   ├── add-work/         # 添加工作记录
│   ├── work-detail/      # 工作详情
│   ├── statistics/       # 统计分析
│   ├── profile/          # 个人设置
│   └── work-types/       # 工作类型管理
├── components/           # 自定义组件
│   ├── calendar/         # 日历组件
│   ├── work-type-selector/ # 工作类型选择器
│   ├── time-picker/      # 时间选择器
│   └── stat-card/        # 统计卡片
├── utils/                # 工具类
│   ├── storage.js        # 本地存储工具
│   ├── date.js           # 日期时间工具
│   ├── salary.js         # 薪资计算工具
│   └── validator.js      # 数据验证工具
├── data/                 # 数据层
│   ├── work-record.js    # 工作记录数据模型
│   ├── work-type.js      # 工作类型数据模型
│   └── constants.js      # 常量定义
└── images/               # 图片资源
    ├── tab-home.png      # 首页图标
    ├── tab-stats.png     # 统计图标
    └── tab-profile.png   # 个人图标
```

## 开发规范

### 命名约定
- **文件命名**：使用kebab-case（短横线分隔）
- **变量命名**：使用camelCase（驼峰命名）
- **常量命名**：使用UPPER_SNAKE_CASE（大写下划线）
- **组件命名**：使用PascalCase（帕斯卡命名）

### 代码规范
- 使用ES6+语法
- 统一使用2个空格缩进
- 函数和变量必须有清晰的注释
- 组件必须有完整的属性说明

### Git提交规范
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 开发计划

### 阶段1：项目基础搭建 ✅
- [x] 项目初始化
- [x] 基础配置文件
- [x] 目录结构设计

### 阶段2：数据层和工具层
- [ ] 数据模型设计
- [ ] 本地存储工具类
- [ ] 日期时间工具类
- [ ] 薪资计算工具类

### 阶段3：公共组件开发
- [ ] 日历组件开发
- [ ] 工作类型选择组件
- [ ] 时间选择器组件
- [ ] 统计卡片组件

### 阶段4：MVP核心页面
- [ ] 首页日历视图
- [ ] 添加工作记录页面
- [ ] 工作详情页面
- [ ] 工作类型管理页面

### 阶段5：扩展功能开发
- [ ] 统计分析页面
- [ ] 个人设置页面
- [ ] 数据导入导出功能
- [ ] 提醒功能

### 阶段6：优化和完善
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 兼容性测试
- [ ] 功能测试和调试

## 许可证

MIT License
