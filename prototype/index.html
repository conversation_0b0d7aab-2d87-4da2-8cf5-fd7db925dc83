<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职工作管理小程序 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 尺寸配置 */
        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 39px;
            overflow: hidden;
            position: relative;
        }
        
        /* 状态栏样式 */
        .status-bar {
            height: 47px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* 动态岛 */
        .dynamic-island {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }
        
        /* 页面容器 */
        .page-container {
            height: calc(100% - 47px);
            overflow: hidden;
        }
        
        /* 自定义滚动条 */
        .custom-scroll::-webkit-scrollbar {
            width: 4px;
        }
        
        .custom-scroll::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .custom-scroll::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        /* 页面切换动画 */
        .page-transition {
            transition: all 0.3s ease-in-out;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .prototype-container {
                flex-direction: column;
                gap: 20px;
            }
            
            .iphone-frame {
                transform: scale(0.8);
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 原型展示容器 -->
    <div class="container mx-auto py-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">兼职工作管理小程序</h1>
            <p class="text-gray-600">HTML原型设计 - iPhone 15 Pro界面效果</p>
        </div>
        
        <!-- 页面导航 -->
        <div class="flex justify-center mb-6">
            <div class="bg-white rounded-lg shadow-md p-2 flex gap-2">
                <button onclick="showPage('home')" class="px-4 py-2 rounded-md bg-blue-500 text-white transition-colors" id="btn-home">
                    <i class="fas fa-calendar-alt mr-2"></i>首页
                </button>
                <button onclick="showPage('add-work')" class="px-4 py-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors" id="btn-add-work">
                    <i class="fas fa-plus mr-2"></i>添加工作
                </button>
                <button onclick="showPage('statistics')" class="px-4 py-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors" id="btn-statistics">
                    <i class="fas fa-chart-bar mr-2"></i>统计
                </button>
                <button onclick="showPage('profile')" class="px-4 py-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors" id="btn-profile">
                    <i class="fas fa-user mr-2"></i>设置
                </button>
            </div>
        </div>
        
        <!-- iPhone 模拟器 -->
        <div class="flex justify-center">
            <div class="iphone-frame">
                <!-- 动态岛 -->
                <div class="dynamic-island"></div>
                
                <!-- 屏幕内容 -->
                <div class="screen">
                    <!-- 状态栏 -->
                    <div class="status-bar">
                        <span>9:41</span>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-signal text-xs"></i>
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>
                    
                    <!-- 页面内容容器 -->
                    <div class="page-container">
                        <iframe id="current-page" src="pages/home.html" class="w-full h-full border-0" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="max-w-4xl mx-auto mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold mb-4">原型功能说明</h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-blue-600 mb-2">📅 首页日历视图</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 月度工作安排一目了然</li>
                        <li>• 不同工作类型色彩区分</li>
                        <li>• 点击日期快速添加工作</li>
                        <li>• 支持月份切换查看</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-green-600 mb-2">➕ 添加工作记录</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 支持单日和多日记录</li>
                        <li>• 智能薪资计算</li>
                        <li>• 工作类型快速选择</li>
                        <li>• 表单验证和提示</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-purple-600 mb-2">📊 统计分析</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 收入和工时统计</li>
                        <li>• 工作类型分布分析</li>
                        <li>• 月度趋势图表</li>
                        <li>• 数据导出功能</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-orange-600 mb-2">⚙️ 个人设置</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 工作类型管理</li>
                        <li>• 提醒设置</li>
                        <li>• 数据备份</li>
                        <li>• 个人信息管理</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面切换功能
        function showPage(pageName) {
            const iframe = document.getElementById('current-page');
            iframe.src = `pages/${pageName}.html`;
            
            // 更新按钮状态
            document.querySelectorAll('[id^="btn-"]').forEach(btn => {
                btn.className = 'px-4 py-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors';
            });
            
            document.getElementById(`btn-${pageName}`).className = 'px-4 py-2 rounded-md bg-blue-500 text-white transition-colors';
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('兼职工作管理小程序原型已加载');
        });
    </script>
</body>
</html>
