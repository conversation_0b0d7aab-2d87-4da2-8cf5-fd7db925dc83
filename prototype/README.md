# 兼职工作管理小程序 - HTML原型设计

## 📱 项目概述

这是一个专为兼职工作者设计的工作记录与时间管理小程序的HTML原型，模拟iPhone 15 Pro界面效果，提供完整的用户体验展示。

## 🎯 核心功能

### 1. 工作记录管理
- **单日工作记录**：支持记录具体日期的工作内容、时间段和薪资
- **多日连续记录**：批量创建连续多天的工作安排
- **工作类型分类**：支持家教辅导、兼职直播、外卖配送、发传单等多种工作类型
- **智能薪资计算**：自动根据工时和薪资标准计算总收入

### 2. 日历视图
- **月度日历展示**：直观显示整月工作安排
- **工作类型色彩区分**：不同工作类型用不同颜色标识
- **快速添加功能**：点击日期即可快速添加工作记录
- **工作密度可视化**：清晰展示工作分布情况

### 3. 统计分析
- **收入统计**：按周/月/年统计总收入和平均收入
- **工时分析**：统计工作时长和工作效率
- **工作类型分布**：分析不同工作类型的收入占比
- **趋势图表**：可视化展示收入和工时变化趋势

### 4. 个人设置
- **工作类型管理**：自定义工作类型、图标和薪资标准
- **提醒设置**：工作提醒和记录提醒功能
- **数据导出**：支持Excel和PDF格式导出
- **数据备份**：云端备份工作数据

## 🏗️ 文件结构

```
prototype/
├── index.html                 # 主入口页面（iPhone模拟器展示）
├── pages/                     # 页面文件夹
│   ├── home.html             # 首页日历视图
│   ├── add-work.html         # 添加工作记录页面
│   ├── work-detail.html      # 工作详情页面
│   ├── statistics.html       # 统计分析页面
│   ├── profile.html          # 个人设置页面
│   └── work-types.html       # 工作类型管理页面
└── README.md                 # 项目说明文档
```

## 🎨 设计规范

### 色彩体系
- **主色调**：#007AFF（微信蓝）
- **成功色**：#34C759（绿色）
- **警告色**：#FF9500（橙色）
- **危险色**：#FF3B30（红色）

### 工作类型色彩编码
- 🔴 兼职直播：#FF3B30
- 🟡 超市发传单：#FF9500
- 🟢 家教辅导：#34C759
- 🔵 外卖配送：#007AFF
- 🟣 其他工作：#AF52DE

### 设备规格
- **屏幕尺寸**：393px × 852px（iPhone 15 Pro）
- **状态栏高度**：47px
- **底部导航栏**：83px
- **最小点击区域**：44px

## 🛠️ 技术栈

- **HTML5**：语义化标签和现代化结构
- **Tailwind CSS**：原子化CSS框架，快速样式开发
- **FontAwesome**：丰富的图标库
- **Chart.js**：数据可视化图表
- **原生JavaScript**：交互逻辑和动态效果

## 📱 页面功能详解

### 1. 首页日历视图 (home.html)
- 月度日历网格布局
- 工作类型色彩标识
- 当前日期高亮显示
- 快速添加工作按钮
- 工作类型图例说明

### 2. 添加工作记录 (add-work.html)
- 单日/多日模式切换
- 工作类型卡片选择
- 时间段选择器
- 智能薪资计算器
- 表单验证和提交

### 3. 统计分析 (statistics.html)
- 核心数据卡片展示
- 收入趋势图表
- 工作类型分布分析
- 工作效率指标
- 数据导出功能

### 4. 个人设置 (profile.html)
- 个人信息展示
- 功能设置列表
- 开关控件交互
- 渐变背景设计
- 统计数据概览

### 5. 工作详情 (work-detail.html)
- 详细信息展示
- 操作记录时间线
- 编辑/删除功能
- 复制记录功能
- 备注信息显示

### 6. 工作类型管理 (work-types.html)
- 工作类型卡片列表
- 统计数据展示
- 添加/编辑模态框
- 颜色和图标选择器
- 删除确认对话框

## 🚀 使用说明

1. **打开原型**：在浏览器中打开 `index.html` 文件
2. **页面切换**：使用顶部导航按钮切换不同页面
3. **交互体验**：点击各种按钮和元素体验交互效果
4. **移动端适配**：在移动设备或开发者工具中查看移动端效果

## 💡 设计亮点

### 用户体验
- **直观的日历视图**：一目了然的工作安排展示
- **智能的薪资计算**：自动计算避免人工错误
- **丰富的统计分析**：多维度数据分析支持决策
- **便捷的操作流程**：最少步骤完成核心功能

### 视觉设计
- **现代化界面**：遵循iOS设计规范
- **一致的色彩体系**：清晰的视觉层级
- **精美的图标设计**：FontAwesome图标库
- **流畅的动画效果**：提升用户体验

### 技术实现
- **响应式布局**：适配不同屏幕尺寸
- **模块化结构**：清晰的文件组织
- **可扩展架构**：便于功能扩展
- **性能优化**：快速加载和流畅交互

## 🔧 开发建议

### 后续开发重点
1. **数据持久化**：集成本地存储或云端数据库
2. **用户认证**：添加登录注册功能
3. **推送通知**：工作提醒和记录提醒
4. **数据同步**：多设备数据同步
5. **社交功能**：工作经验分享

### 技术优化
1. **性能优化**：图片懒加载、代码分割
2. **PWA支持**：离线使用和安装到桌面
3. **无障碍访问**：屏幕阅读器支持
4. **国际化**：多语言支持
5. **数据安全**：加密存储和传输

## 📞 联系信息

如有任何问题或建议，欢迎联系开发团队。

---

**版本**：v1.0.0  
**更新时间**：2025-08-27  
**兼容性**：现代浏览器（Chrome 80+, Safari 13+, Firefox 75+）
