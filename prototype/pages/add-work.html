<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>添加工作记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* 自定义样式 */
      .form-group {
        margin-bottom: 20px;
      }

      .form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
      }

      .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 16px;
        transition: all 0.2s ease;
      }

      .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .work-type-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }

      .work-type-card {
        padding: 16px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .work-type-card:hover {
        border-color: #3b82f6;
        background: #f0f9ff;
      }

      .work-type-card.selected {
        border-color: #3b82f6;
        background: #dbeafe;
      }

      .work-type-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 0 auto 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      .mode-switch {
        display: flex;
        background: #f3f4f6;
        border-radius: 8px;
        padding: 4px;
      }

      .mode-button {
        flex: 1;
        padding: 8px 16px;
        text-align: center;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .mode-button.active {
        background: white;
        color: #3b82f6;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .salary-calculator {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
      }

      .time-picker {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      .time-input {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        text-align: center;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- 主容器 -->
    <div class="min-h-screen">
      <!-- 顶部导航栏 -->
      <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex items-center justify-between">
          <button
            class="p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i class="fas fa-arrow-left text-gray-600"></i>
          </button>
          <h1 class="text-lg font-semibold text-gray-900">添加工作记录</h1>
          <button
            class="text-blue-500 font-medium hover:text-blue-600 transition-colors"
          >
            保存
          </button>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="p-4">
        <!-- 记录模式切换 -->
        <div class="form-group">
          <label class="form-label">记录模式</label>
          <div class="mode-switch">
            <div class="mode-button active" onclick="switchMode('single')">
              单日记录
            </div>
            <div class="mode-button" onclick="switchMode('multiple')">
              多日记录
            </div>
          </div>
        </div>

        <!-- 日期选择 -->
        <div class="form-group" id="single-date">
          <label class="form-label">工作日期</label>
          <input type="date" class="form-input" value="2025-08-27" />
        </div>

        <!-- 多日期选择（隐藏） -->
        <div class="form-group hidden" id="multiple-date">
          <label class="form-label">日期范围</label>
          <div class="flex gap-3">
            <input type="date" class="form-input" placeholder="开始日期" />
            <span class="flex items-center text-gray-500">至</span>
            <input type="date" class="form-input" placeholder="结束日期" />
          </div>
        </div>

        <!-- 工作时间 -->
        <div class="form-group">
          <label class="form-label">工作时间</label>
          <div class="time-picker">
            <input type="time" class="time-input" value="09:00" />
            <span class="text-gray-500">-</span>
            <input type="time" class="time-input" value="17:00" />
          </div>
          <div class="mt-2 text-sm text-gray-500">工作时长：8小时</div>
        </div>

        <!-- 工作类型选择 -->
        <div class="form-group">
          <label class="form-label">工作类型</label>
          <div class="work-type-grid">
            <div class="work-type-card selected" data-type="tutor">
              <div class="work-type-icon" style="background: #10b981">
                <i class="fas fa-graduation-cap"></i>
              </div>
              <div class="text-sm font-medium">家教辅导</div>
              <div class="text-xs text-gray-500">¥50/小时</div>
            </div>
            <div class="work-type-card" data-type="delivery">
              <div class="work-type-icon" style="background: #3b82f6">
                <i class="fas fa-motorcycle"></i>
              </div>
              <div class="text-sm font-medium">外卖配送</div>
              <div class="text-xs text-gray-500">¥6/单</div>
            </div>
            <div class="work-type-card" data-type="live">
              <div class="work-type-icon" style="background: #ef4444">
                <i class="fas fa-video"></i>
              </div>
              <div class="text-sm font-medium">兼职直播</div>
              <div class="text-xs text-gray-500">¥200/天</div>
            </div>
            <div class="work-type-card" data-type="flyer">
              <div class="work-type-icon" style="background: #f59e0b">
                <i class="fas fa-bullhorn"></i>
              </div>
              <div class="text-sm font-medium">发传单</div>
              <div class="text-xs text-gray-500">¥15/小时</div>
            </div>
          </div>
        </div>

        <!-- 内容 -->
        <div class="form-group">
          <label class="form-label">内容</label>
          <textarea
            class="form-input"
            rows="2"
            placeholder="其他需要记录的信息..."
          ></textarea>
        </div>

        <!-- 薪资计算 -->
        <div class="form-group">
          <label class="form-label">薪资计算</label>
          <div class="salary-calculator">
            <div class="flex justify-between items-center mb-3">
              <span class="text-sm text-gray-600">计算方式</span>
              <select class="text-sm border border-gray-300 rounded px-2 py-1">
                <option>按小时计算</option>
                <option>按天计算</option>
                <option>按项目计算</option>
              </select>
            </div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-600">时薪</span>
              <input
                type="number"
                class="text-sm border border-gray-300 rounded px-2 py-1 w-20"
                value="50"
              />
            </div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-600">工作时长</span>
              <span class="text-sm font-medium">8小时</span>
            </div>
            <div class="border-t pt-2 mt-2">
              <div class="flex justify-between items-center">
                <span class="font-medium">总薪资</span>
                <span class="text-lg font-bold text-green-600">¥400</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="mt-8 pb-6">
          <button
            class="w-full bg-blue-500 text-white py-4 rounded-lg font-medium text-lg hover:bg-blue-600 transition-colors"
          >
            <i class="fas fa-save mr-2"></i>保存工作记录
          </button>
        </div>
      </div>
    </div>

    <script>
      // 模式切换功能
      function switchMode(mode) {
        // 更新按钮状态
        document.querySelectorAll(".mode-button").forEach((btn) => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");

        // 切换日期选择器
        if (mode === "single") {
          document.getElementById("single-date").classList.remove("hidden");
          document.getElementById("multiple-date").classList.add("hidden");
        } else {
          document.getElementById("single-date").classList.add("hidden");
          document.getElementById("multiple-date").classList.remove("hidden");
        }
      }

      // 工作类型选择
      document.querySelectorAll(".work-type-card").forEach((card) => {
        card.addEventListener("click", function () {
          // 移除其他选中状态
          document.querySelectorAll(".work-type-card").forEach((c) => {
            c.classList.remove("selected");
          });
          // 添加当前选中状态
          this.classList.add("selected");

          // 更新薪资计算
          updateSalaryCalculation();
        });
      });

      // 时间变化监听
      document.querySelectorAll(".time-input").forEach((input) => {
        input.addEventListener("change", function () {
          calculateWorkHours();
          updateSalaryCalculation();
        });
      });

      // 计算工作时长
      function calculateWorkHours() {
        const startTime = document.querySelectorAll(".time-input")[0].value;
        const endTime = document.querySelectorAll(".time-input")[1].value;

        if (startTime && endTime) {
          const start = new Date(`2000-01-01 ${startTime}`);
          const end = new Date(`2000-01-01 ${endTime}`);
          const hours = (end - start) / (1000 * 60 * 60);

          document.querySelector(
            ".text-gray-500"
          ).textContent = `工作时长：${hours}小时`;
        }
      }

      // 更新薪资计算
      function updateSalaryCalculation() {
        // 这里可以添加实际的薪资计算逻辑
        console.log("更新薪资计算");
      }

      // 表单验证和提交
      document
        .querySelector(".bg-blue-500")
        .addEventListener("click", function () {
          // 这里可以添加表单验证逻辑
          alert("工作记录保存成功！");
        });

      // 页面初始化
      document.addEventListener("DOMContentLoaded", function () {
        calculateWorkHours();
      });
    </script>
  </body>
</html>
