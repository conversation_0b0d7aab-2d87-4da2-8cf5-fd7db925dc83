<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作类型管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .work-type-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .work-type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .work-type-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .work-type-info {
            display: flex;
            align-items: center;
        }
        
        .work-type-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 12px;
        }
        
        .work-type-details {
            flex: 1;
        }
        
        .work-type-name {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 4px;
        }
        
        .work-type-rate {
            font-size: 14px;
            color: #6b7280;
        }
        
        .work-type-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .action-btn.edit {
            background: #dbeafe;
            color: #3b82f6;
        }
        
        .action-btn.edit:hover {
            background: #bfdbfe;
        }
        
        .action-btn.delete {
            background: #fee2e2;
            color: #ef4444;
        }
        
        .action-btn.delete:hover {
            background: #fecaca;
        }
        
        .work-type-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #f3f4f6;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #111827;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 2px;
        }
        
        .add-button {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #3b82f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .add-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .color-picker {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin-top: 8px;
        }
        
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.2s ease;
        }
        
        .color-option.selected {
            border-color: #374151;
            transform: scale(1.1);
        }
        
        .icon-picker {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            margin-top: 8px;
        }
        
        .icon-option {
            width: 40px;
            height: 40px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .icon-option.selected {
            border-color: #3b82f6;
            background: #dbeafe;
            color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
        <!-- 顶部导航栏 -->
        <div class="bg-white border-b border-gray-200 px-4 py-3">
            <div class="flex items-center justify-between">
                <button class="p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-900">工作类型管理</h1>
                <button class="text-blue-500 font-medium hover:text-blue-600 transition-colors">
                    完成
                </button>
            </div>
        </div>
        
        <!-- 工作类型列表 -->
        <div class="p-4">
            <!-- 家教辅导 -->
            <div class="work-type-card">
                <div class="work-type-header">
                    <div class="work-type-info">
                        <div class="work-type-icon" style="background: #10b981;">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="work-type-details">
                            <div class="work-type-name">家教辅导</div>
                            <div class="work-type-rate">¥50/小时</div>
                        </div>
                    </div>
                    <div class="work-type-actions">
                        <div class="action-btn edit" onclick="editWorkType('tutor')">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-btn delete" onclick="deleteWorkType('tutor')">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                    </div>
                </div>
                <div class="work-type-stats">
                    <div class="stat-item">
                        <div class="stat-number">32</div>
                        <div class="stat-label">本月小时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">¥1,600</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">工作天数</div>
                    </div>
                </div>
            </div>
            
            <!-- 兼职直播 -->
            <div class="work-type-card">
                <div class="work-type-header">
                    <div class="work-type-info">
                        <div class="work-type-icon" style="background: #ef4444;">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="work-type-details">
                            <div class="work-type-name">兼职直播</div>
                            <div class="work-type-rate">¥200/天</div>
                        </div>
                    </div>
                    <div class="work-type-actions">
                        <div class="action-btn edit" onclick="editWorkType('live')">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-btn delete" onclick="deleteWorkType('live')">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                    </div>
                </div>
                <div class="work-type-stats">
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">本月天数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">¥1,200</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">48</div>
                        <div class="stat-label">总小时</div>
                    </div>
                </div>
            </div>
            
            <!-- 外卖配送 -->
            <div class="work-type-card">
                <div class="work-type-header">
                    <div class="work-type-info">
                        <div class="work-type-icon" style="background: #3b82f6;">
                            <i class="fas fa-motorcycle"></i>
                        </div>
                        <div class="work-type-details">
                            <div class="work-type-name">外卖配送</div>
                            <div class="work-type-rate">¥6/单</div>
                        </div>
                    </div>
                    <div class="work-type-actions">
                        <div class="action-btn edit" onclick="editWorkType('delivery')">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-btn delete" onclick="deleteWorkType('delivery')">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                    </div>
                </div>
                <div class="work-type-stats">
                    <div class="stat-item">
                        <div class="stat-number">53</div>
                        <div class="stat-label">本月订单</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">¥318</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">18</div>
                        <div class="stat-label">工作小时</div>
                    </div>
                </div>
            </div>
            
            <!-- 发传单 -->
            <div class="work-type-card">
                <div class="work-type-header">
                    <div class="work-type-info">
                        <div class="work-type-icon" style="background: #f59e0b;">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="work-type-details">
                            <div class="work-type-name">发传单</div>
                            <div class="work-type-rate">¥15/小时</div>
                        </div>
                    </div>
                    <div class="work-type-actions">
                        <div class="action-btn edit" onclick="editWorkType('flyer')">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-btn delete" onclick="deleteWorkType('flyer')">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                    </div>
                </div>
                <div class="work-type-stats">
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">本月小时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">¥120</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2</div>
                        <div class="stat-label">工作天数</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加按钮 -->
    <div class="add-button" onclick="showAddModal()">
        <i class="fas fa-plus"></i>
    </div>
    
    <!-- 添加/编辑工作类型模态框 -->
    <div class="modal" id="workTypeModal">
        <div class="modal-content">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">添加工作类型</h2>
                <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form>
                <div class="form-group">
                    <label class="form-label">工作类型名称</label>
                    <input type="text" class="form-input" placeholder="请输入工作类型名称">
                </div>
                
                <div class="form-group">
                    <label class="form-label">薪资标准</label>
                    <div class="flex gap-2">
                        <input type="number" class="form-input" placeholder="金额">
                        <select class="form-input" style="flex: 0 0 80px;">
                            <option>/小时</option>
                            <option>/天</option>
                            <option>/单</option>
                            <option>/项目</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">选择颜色</label>
                    <div class="color-picker">
                        <div class="color-option selected" style="background: #ef4444;" data-color="#ef4444"></div>
                        <div class="color-option" style="background: #f59e0b;" data-color="#f59e0b"></div>
                        <div class="color-option" style="background: #10b981;" data-color="#10b981"></div>
                        <div class="color-option" style="background: #3b82f6;" data-color="#3b82f6"></div>
                        <div class="color-option" style="background: #8b5cf6;" data-color="#8b5cf6"></div>
                        <div class="color-option" style="background: #ec4899;" data-color="#ec4899"></div>
                        <div class="color-option" style="background: #06b6d4;" data-color="#06b6d4"></div>
                        <div class="color-option" style="background: #84cc16;" data-color="#84cc16"></div>
                        <div class="color-option" style="background: #f97316;" data-color="#f97316"></div>
                        <div class="color-option" style="background: #6b7280;" data-color="#6b7280"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">选择图标</label>
                    <div class="icon-picker">
                        <div class="icon-option selected" data-icon="fas fa-briefcase">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-graduation-cap">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-video">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-motorcycle">
                            <i class="fas fa-motorcycle"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-bullhorn">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-code">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-paint-brush">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-camera">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-music">
                            <i class="fas fa-music"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-utensils">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-car">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="icon-option" data-icon="fas fa-shopping-cart">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-3 mt-6">
                    <button type="button" onclick="hideModal()" class="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium">
                        取消
                    </button>
                    <button type="submit" class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 显示添加模态框
        function showAddModal() {
            document.getElementById('workTypeModal').style.display = 'flex';
        }
        
        // 隐藏模态框
        function hideModal() {
            document.getElementById('workTypeModal').style.display = 'none';
        }
        
        // 编辑工作类型
        function editWorkType(type) {
            alert(`编辑${type}工作类型`);
            showAddModal();
        }
        
        // 删除工作类型
        function deleteWorkType(type) {
            if (confirm('确定要删除这个工作类型吗？删除后相关的工作记录将无法正常显示。')) {
                alert(`${type}工作类型已删除`);
            }
        }
        
        // 颜色选择
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.color-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 图标选择
        document.querySelectorAll('.icon-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.icon-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 表单提交
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('工作类型保存成功！');
            hideModal();
        });
        
        // 返回按钮
        document.querySelector('.fa-arrow-left').parentElement.addEventListener('click', function() {
            alert('返回设置页面');
        });
    </script>
</body>
</html>
