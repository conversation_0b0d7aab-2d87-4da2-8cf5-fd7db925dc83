<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .detail-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .work-type-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            color: white;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #6b7280;
            font-size: 14px;
        }
        
        .info-value {
            font-weight: 500;
            color: #111827;
        }
        
        .action-button {
            flex: 1;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .action-button.primary {
            background: #3b82f6;
            color: white;
        }
        
        .action-button.primary:hover {
            background: #2563eb;
        }
        
        .action-button.secondary {
            background: #f3f4f6;
            color: #374151;
        }
        
        .action-button.secondary:hover {
            background: #e5e7eb;
        }
        
        .action-button.danger {
            background: #ef4444;
            color: white;
        }
        
        .action-button.danger:hover {
            background: #dc2626;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 32px;
            padding-bottom: 16px;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            left: 8px;
            top: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #3b82f6;
        }
        
        .timeline-item:after {
            content: '';
            position: absolute;
            left: 11px;
            top: 16px;
            width: 2px;
            height: calc(100% - 8px);
            background: #e5e7eb;
        }
        
        .timeline-item:last-child:after {
            display: none;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        
        .timeline-content {
            font-size: 14px;
            color: #374151;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 主容器 -->
    <div class="min-h-screen">
        <!-- 顶部导航栏 -->
        <div class="bg-white border-b border-gray-200 px-4 py-3">
            <div class="flex items-center justify-between">
                <button class="p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-900">工作详情</h1>
                <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fas fa-edit text-gray-600"></i>
                </button>
            </div>
        </div>
        
        <!-- 工作详情内容 -->
        <div class="p-4">
            <!-- 基本信息卡片 -->
            <div class="detail-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="work-type-badge" style="background: #10b981;">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        家教辅导
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-green-600">¥400</div>
                        <div class="text-sm text-gray-500">总薪资</div>
                    </div>
                </div>
                
                <h2 class="text-xl font-bold text-gray-900 mb-2">高中数学辅导</h2>
                <p class="text-gray-600 mb-4">主要内容包括函数、导数、积分等知识点的讲解和练习</p>
                
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    <span>2025年8月27日</span>
                    <i class="fas fa-clock ml-4 mr-2"></i>
                    <span>09:00 - 17:00</span>
                    <i class="fas fa-map-marker-alt ml-4 mr-2"></i>
                    <span>学生家中</span>
                </div>
            </div>
            
            <!-- 详细信息卡片 -->
            <div class="detail-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">详细信息</h3>
                
                <div class="info-row">
                    <span class="info-label">工作日期</span>
                    <span class="info-value">2025年8月27日</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">工作时间</span>
                    <span class="info-value">09:00 - 17:00</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">工作时长</span>
                    <span class="info-value">8小时</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">时薪标准</span>
                    <span class="info-value">¥50/小时</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">计算方式</span>
                    <span class="info-value">按小时计算</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">工作地点</span>
                    <span class="info-value">学生家中</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">创建时间</span>
                    <span class="info-value">2025-08-26 20:30</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">最后修改</span>
                    <span class="info-value">2025-08-27 08:15</span>
                </div>
            </div>
            
            <!-- 工作内容卡片 -->
            <div class="detail-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">工作内容</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-gray-700 leading-relaxed">
                        今天主要辅导高二学生的数学课程，重点讲解了函数的性质和图像变换。学生对于复合函数的理解还需要加强，下次课程需要重点练习相关题型。
                        <br><br>
                        具体内容包括：
                        <br>• 函数的单调性和奇偶性
                        <br>• 反函数的概念和求法
                        <br>• 复合函数的运算
                        <br>• 函数图像的平移和对称变换
                        <br><br>
                        学生表现良好，课堂参与度高，作业完成质量不错。
                    </p>
                </div>
            </div>
            
            <!-- 备注信息 -->
            <div class="detail-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">备注信息</h3>
                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-blue-700">
                        学生家长很满意，已预约下周同一时间继续辅导。需要准备更多练习题。
                    </p>
                </div>
            </div>
            
            <!-- 操作记录 -->
            <div class="detail-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">操作记录</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-time">2025-08-27 08:15</div>
                        <div class="timeline-content">修改了工作内容和备注信息</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">2025-08-26 20:30</div>
                        <div class="timeline-content">创建了这条工作记录</div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex gap-3 mt-6 pb-6">
                <button class="action-button secondary">
                    <i class="fas fa-copy mr-2"></i>复制记录
                </button>
                <button class="action-button primary">
                    <i class="fas fa-edit mr-2"></i>编辑记录
                </button>
                <button class="action-button danger">
                    <i class="fas fa-trash-alt mr-2"></i>删除
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 编辑按钮功能
        document.querySelector('.fa-edit').parentElement.addEventListener('click', function() {
            alert('跳转到编辑页面');
        });
        
        // 操作按钮功能
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', function() {
                const text = this.textContent.trim();
                
                if (text.includes('复制')) {
                    alert('记录已复制，可以基于此记录创建新的工作记录');
                } else if (text.includes('编辑')) {
                    alert('跳转到编辑页面');
                } else if (text.includes('删除')) {
                    if (confirm('确定要删除这条工作记录吗？删除后无法恢复。')) {
                        alert('记录已删除');
                    }
                }
            });
        });
        
        // 返回按钮
        document.querySelector('.fa-arrow-left').parentElement.addEventListener('click', function() {
            alert('返回上一页');
        });
    </script>
</body>
</html>
