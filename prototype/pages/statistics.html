<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 自定义样式 */
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 16px;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            line-height: 1.2;
        }
        
        .stat-label {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }
        
        .stat-change {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            margin-top: 8px;
            display: inline-block;
        }
        
        .stat-change.positive {
            background: #dcfce7;
            color: #166534;
        }
        
        .stat-change.negative {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .time-filter {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 20px;
        }
        
        .time-filter-btn {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .time-filter-btn.active {
            background: white;
            color: #3b82f6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .chart-container {
            position: relative;
            height: 200px;
            margin: 16px 0;
        }
        
        .work-type-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .work-type-item:last-child {
            border-bottom: none;
        }
        
        .work-type-info {
            display: flex;
            align-items: center;
        }
        
        .work-type-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
        }
        
        .progress-bar {
            width: 60px;
            height: 6px;
            background: #f3f4f6;
            border-radius: 3px;
            overflow: hidden;
            margin-left: 12px;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
        <!-- 顶部导航栏 -->
        <div class="bg-white border-b border-gray-200 px-4 py-3">
            <div class="flex items-center justify-between">
                <h1 class="text-lg font-semibold text-gray-900">统计分析</h1>
                <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fas fa-download text-gray-600"></i>
                </button>
            </div>
        </div>
        
        <!-- 时间筛选 -->
        <div class="p-4">
            <div class="time-filter">
                <div class="time-filter-btn" onclick="switchTimeFilter('week')">本周</div>
                <div class="time-filter-btn active" onclick="switchTimeFilter('month')">本月</div>
                <div class="time-filter-btn" onclick="switchTimeFilter('year')">本年</div>
            </div>
        </div>
        
        <!-- 核心统计卡片 -->
        <div class="px-4">
            <div class="grid grid-cols-2 gap-4 mb-6">
                <!-- 总收入 -->
                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <i class="fas fa-coins text-green-500 text-xl"></i>
                        <i class="fas fa-arrow-up text-green-500 text-sm"></i>
                    </div>
                    <div class="stat-number text-green-600">¥3,240</div>
                    <div class="stat-label">总收入</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up mr-1"></i>+12.5%
                    </div>
                </div>
                
                <!-- 工作时长 -->
                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <i class="fas fa-clock text-blue-500 text-xl"></i>
                        <i class="fas fa-arrow-up text-blue-500 text-sm"></i>
                    </div>
                    <div class="stat-number text-blue-600">68.5</div>
                    <div class="stat-label">工作小时</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up mr-1"></i>+8.2%
                    </div>
                </div>
                
                <!-- 工作天数 -->
                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <i class="fas fa-calendar-check text-purple-500 text-xl"></i>
                        <i class="fas fa-arrow-down text-red-500 text-sm"></i>
                    </div>
                    <div class="stat-number text-purple-600">12</div>
                    <div class="stat-label">工作天数</div>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down mr-1"></i>-5.0%
                    </div>
                </div>
                
                <!-- 平均时薪 -->
                <div class="stat-card">
                    <div class="flex items-center justify-between mb-2">
                        <i class="fas fa-chart-line text-orange-500 text-xl"></i>
                        <i class="fas fa-arrow-up text-green-500 text-sm"></i>
                    </div>
                    <div class="stat-number text-orange-600">¥47.3</div>
                    <div class="stat-label">平均时薪</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up mr-1"></i>+4.1%
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 收入趋势图表 -->
        <div class="px-4 mb-6">
            <div class="stat-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">收入趋势</h3>
                    <select class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option>按周统计</option>
                        <option>按月统计</option>
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="incomeChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 工作类型分布 -->
        <div class="px-4 mb-6">
            <div class="stat-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">工作类型分布</h3>
                
                <div class="work-type-item">
                    <div class="work-type-info">
                        <div class="work-type-color" style="background: #10b981;"></div>
                        <div>
                            <div class="font-medium">家教辅导</div>
                            <div class="text-sm text-gray-500">32小时</div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-900">¥1,600</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 80%; background: #10b981;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="work-type-item">
                    <div class="work-type-info">
                        <div class="work-type-color" style="background: #ef4444;"></div>
                        <div>
                            <div class="font-medium">兼职直播</div>
                            <div class="text-sm text-gray-500">6天</div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-900">¥1,200</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%; background: #ef4444;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="work-type-item">
                    <div class="work-type-info">
                        <div class="work-type-color" style="background: #3b82f6;"></div>
                        <div>
                            <div class="font-medium">外卖配送</div>
                            <div class="text-sm text-gray-500">18小时</div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-900">¥320</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%; background: #3b82f6;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="work-type-item">
                    <div class="work-type-info">
                        <div class="work-type-color" style="background: #f59e0b;"></div>
                        <div>
                            <div class="font-medium">发传单</div>
                            <div class="text-sm text-gray-500">8小时</div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-900">¥120</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 15%; background: #f59e0b;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工作效率分析 -->
        <div class="px-4 mb-6">
            <div class="stat-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">工作效率分析</h3>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">47.3</div>
                        <div class="text-sm text-gray-500">平均时薪(¥)</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">5.7</div>
                        <div class="text-sm text-gray-500">日均工时</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">270</div>
                        <div class="text-sm text-gray-500">日均收入(¥)</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">2.4</div>
                        <div class="text-sm text-gray-500">工作类型数</div>
                    </div>
                </div>
                
                <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-lightbulb text-blue-500 mr-2"></i>
                        <span class="text-sm text-blue-700">
                            建议：家教辅导收入最高，可以适当增加此类工作时间
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 导出功能 -->
        <div class="px-4 pb-6">
            <div class="stat-card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">数据导出</h3>
                <div class="flex gap-3">
                    <button class="flex-1 bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>导出Excel
                    </button>
                    <button class="flex-1 bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-colors">
                        <i class="fas fa-file-pdf mr-2"></i>导出PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-0 py-2">
        <div class="flex">
            <div class="flex-1 flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-calendar-alt text-xl mb-1"></i>
                <span class="text-xs">日历</span>
            </div>
            <div class="flex-1 flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-plus-circle text-xl mb-1"></i>
                <span class="text-xs">记录</span>
            </div>
            <div class="flex-1 flex flex-col items-center py-2 text-blue-500">
                <i class="fas fa-chart-bar text-xl mb-1"></i>
                <span class="text-xs">统计</span>
            </div>
            <div class="flex-1 flex flex-col items-center py-2 text-gray-400">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">我的</span>
            </div>
        </div>
    </div>
    
    <script>
        // 时间筛选切换
        function switchTimeFilter(period) {
            document.querySelectorAll('.time-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 这里可以添加数据更新逻辑
            updateStatistics(period);
        }
        
        // 更新统计数据
        function updateStatistics(period) {
            console.log(`更新${period}统计数据`);
            // 这里可以添加实际的数据更新逻辑
        }
        
        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('incomeChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['第1周', '第2周', '第3周', '第4周'],
                    datasets: [{
                        label: '收入',
                        data: [680, 920, 750, 890],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f3f4f6'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });
    </script>
</body>
</html>
