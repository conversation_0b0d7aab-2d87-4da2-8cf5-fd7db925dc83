<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俏皮风格图标预览 - 兼职工作管理</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007AFF;
            text-align: center;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 16px;
        }
        .icon-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .icon-card {
            border: 2px solid #f0f0f0;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #007AFF;
        }
        .icon-card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .icon-pair {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        .icon-item h4 {
            margin: 15px 0 10px 0;
            font-size: 14px;
            color: #555;
        }
        .download-btn {
            background: linear-gradient(45deg, #007AFF, #0056CC);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 122, 255, 0.3);
        }
        .features {
            background: linear-gradient(45deg, #f8f9ff, #e8f2ff);
            border-radius: 16px;
            padding: 30px;
            margin-top: 40px;
        }
        .features h3 {
            color: #007AFF;
            margin-bottom: 20px;
            text-align: center;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #007AFF;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 俏皮风格图标</h1>
        <p class="subtitle">兼职工作管理小程序 - 更多圆角元素，更加可爱俏皮</p>
        
        <div class="icon-showcase">
            <!-- 首页图标 -->
            <div class="icon-card">
                <h3>🏠 首页图标</h3>
                <div class="icon-pair">
                    <div class="icon-item">
                        <svg width="60" height="60" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
                            <rect x="22" y="32" width="37" height="32" fill="none" stroke="#7A7E83" stroke-width="3" rx="8"/>
                            <path d="M18 35C18 35 40.5 18 40.5 18S63 35 63 35" stroke="#7A7E83" stroke-width="3" stroke-linecap="round" fill="none"/>
                            <rect x="35" y="48" width="11" height="16" fill="none" stroke="#7A7E83" stroke-width="2.5" rx="5.5"/>
                            <circle cx="28" cy="42" r="3" fill="none" stroke="#7A7E83" stroke-width="2"/>
                            <circle cx="53" cy="42" r="3" fill="none" stroke="#7A7E83" stroke-width="2"/>
                            <rect x="50" y="22" width="6" height="12" fill="none" stroke="#7A7E83" stroke-width="2" rx="3"/>
                        </svg>
                        <h4>未选中</h4>
                        <button class="download-btn" onclick="downloadIcon('tab-home')">下载</button>
                    </div>
                    <div class="icon-item">
                        <svg width="60" height="60" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
                            <rect x="22" y="32" width="37" height="32" fill="#007AFF" stroke="#007AFF" stroke-width="3" rx="8"/>
                            <path d="M18 35C18 35 40.5 18 40.5 18S63 35 63 35" stroke="#007AFF" stroke-width="3" stroke-linecap="round" fill="none"/>
                            <rect x="35" y="48" width="11" height="16" fill="#ffffff" stroke="#007AFF" stroke-width="2.5" rx="5.5"/>
                            <circle cx="28" cy="42" r="3" fill="#ffffff"/>
                            <circle cx="53" cy="42" r="3" fill="#ffffff"/>
                            <rect x="50" y="22" width="6" height="12" fill="#007AFF" stroke="#007AFF" stroke-width="2" rx="3"/>
                        </svg>
                        <h4>选中</h4>
                        <button class="download-btn" onclick="downloadIcon('tab-home-active')">下载</button>
                    </div>
                </div>
            </div>

            <!-- 统计图标 -->
            <div class="icon-card">
                <h3>📊 统计图标</h3>
                <div class="icon-pair">
                    <div class="icon-item">
                        <svg width="60" height="60" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
                            <rect x="18" y="45" width="10" height="20" fill="#7A7E83" rx="5"/>
                            <rect x="30" y="35" width="10" height="30" fill="#7A7E83" rx="5"/>
                            <rect x="42" y="40" width="10" height="25" fill="#7A7E83" rx="5"/>
                            <rect x="54" y="25" width="10" height="40" fill="#7A7E83" rx="5"/>
                            <path d="M12 67C12 67 69 67 69 67" stroke="#7A7E83" stroke-width="3" stroke-linecap="round"/>
                            <circle cx="23" cy="20" r="2" fill="#7A7E83"/>
                            <circle cx="35" cy="15" r="2" fill="#7A7E83"/>
                            <circle cx="47" cy="18" r="2" fill="#7A7E83"/>
                            <circle cx="59" cy="12" r="2" fill="#7A7E83"/>
                            <path d="M23 20L35 15L47 18L59 12" stroke="#7A7E83" stroke-width="2" stroke-linecap="round" fill="none"/>
                        </svg>
                        <h4>未选中</h4>
                        <button class="download-btn" onclick="downloadIcon('tab-stats')">下载</button>
                    </div>
                    <div class="icon-item">
                        <svg width="60" height="60" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
                            <rect x="18" y="45" width="10" height="20" fill="#007AFF" rx="5"/>
                            <rect x="30" y="35" width="10" height="30" fill="#007AFF" rx="5"/>
                            <rect x="42" y="40" width="10" height="25" fill="#007AFF" rx="5"/>
                            <rect x="54" y="25" width="10" height="40" fill="#007AFF" rx="5"/>
                            <path d="M12 67C12 67 69 67 69 67" stroke="#007AFF" stroke-width="3" stroke-linecap="round"/>
                            <circle cx="23" cy="20" r="2" fill="#007AFF"/>
                            <circle cx="35" cy="15" r="2" fill="#007AFF"/>
                            <circle cx="47" cy="18" r="2" fill="#007AFF"/>
                            <circle cx="59" cy="12" r="2" fill="#007AFF"/>
                            <path d="M23 20L35 15L47 18L59 12" stroke="#007AFF" stroke-width="2" stroke-linecap="round" fill="none"/>
                        </svg>
                        <h4>选中</h4>
                        <button class="download-btn" onclick="downloadIcon('tab-stats-active')">下载</button>
                    </div>
                </div>
            </div>

            <!-- 个人中心图标 -->
            <div class="icon-card">
                <h3>👤 个人中心</h3>
                <div class="icon-pair">
                    <div class="icon-item">
                        <svg width="60" height="60" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="40.5" cy="28" r="12" fill="none" stroke="#7A7E83" stroke-width="3"/>
                            <path d="M22 62C22 52 30 47 40.5 47C51 47 59 52 59 62" fill="none" stroke="#7A7E83" stroke-width="3" stroke-linecap="round"/>
                            <circle cx="35" cy="25" r="1.5" fill="#7A7E83"/>
                            <circle cx="46" cy="25" r="1.5" fill="#7A7E83"/>
                            <path d="M35 32C35 32 38 35 40.5 35C43 35 46 32 46 32" stroke="#7A7E83" stroke-width="2" stroke-linecap="round" fill="none"/>
                            <circle cx="30" cy="30" r="2" fill="#7A7E83" opacity="0.3"/>
                            <circle cx="51" cy="30" r="2" fill="#7A7E83" opacity="0.3"/>
                        </svg>
                        <h4>未选中</h4>
                        <button class="download-btn" onclick="downloadIcon('tab-profile')">下载</button>
                    </div>
                    <div class="icon-item">
                        <svg width="60" height="60" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="40.5" cy="28" r="12" fill="#007AFF"/>
                            <path d="M22 62C22 52 30 47 40.5 47C51 47 59 52 59 62" fill="#007AFF"/>
                            <circle cx="35" cy="25" r="1.5" fill="#ffffff"/>
                            <circle cx="46" cy="25" r="1.5" fill="#ffffff"/>
                            <path d="M35 32C35 32 38 35 40.5 35C43 35 46 32 46 32" stroke="#ffffff" stroke-width="2" stroke-linecap="round" fill="none"/>
                            <circle cx="30" cy="30" r="2" fill="#ffffff" opacity="0.6"/>
                            <circle cx="51" cy="30" r="2" fill="#ffffff" opacity="0.6"/>
                        </svg>
                        <h4>选中</h4>
                        <button class="download-btn" onclick="downloadIcon('tab-profile-active')">下载</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="features">
            <h3>✨ 俏皮设计特色</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <span>更多圆角元素</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">😊</span>
                    <span>可爱表情细节</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🏠</span>
                    <span>温馨房屋设计</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📈</span>
                    <span>动态趋势线</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">💙</span>
                    <span>品牌色彩一致</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <span>小程序规范</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadIcon(iconName) {
            // 创建一个临时链接来下载对应的SVG文件
            const link = document.createElement('a');
            link.href = iconName + '.svg';
            link.download = iconName + '.svg';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 显示提示信息
            alert(`已下载 ${iconName}.svg 文件！\n\n请使用在线工具将SVG转换为81x81像素的PNG格式：\n1. 访问 https://convertio.co/svg-png/\n2. 上传SVG文件\n3. 设置尺寸为81x81像素\n4. 下载PNG文件`);
        }
    </script>
</body>
</html>
