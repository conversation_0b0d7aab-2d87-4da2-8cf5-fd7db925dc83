/* pages/reminder-settings/reminder-settings.wxss */
/* 提醒设置页面样式 */

.reminder-settings-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 权限状态 */
.permission-status {
  margin: 32rpx;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-card.granted {
  border-left: 8rpx solid #34C759;
}

.status-card.denied {
  border-left: 8rpx solid #FF3B30;
}

.status-icon {
  font-size: 32rpx;
}

.status-content {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.status-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.permission-btn {
  padding: 16rpx 24rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.permission-btn:active {
  background: #0056CC;
}

/* 设置区域 */
.settings-section {
  margin: 0 32rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.item-content {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.item-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.item-right {
  display: flex;
  align-items: center;
}

.time-btn,
.edit-btn {
  padding: 12rpx 20rpx;
  background: #f0f9ff;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.time-btn:active,
.edit-btn:active {
  background: #007AFF;
  color: #ffffff;
}

/* 子区域 */
.sub-section {
  margin-left: 32rpx;
  margin-top: 16rpx;
  padding-left: 24rpx;
  border-left: 4rpx solid #e9ecef;
}

.sub-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 16rpx;
}

/* 通知列表 */
.notifications-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.notification-content {
  flex: 1;
}

.notification-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.notification-time {
  display: block;
  font-size: 20rpx;
  color: #666;
}

.cancel-btn {
  padding: 8rpx 16rpx;
  background: #fff5f5;
  color: #FF3B30;
  border: 2rpx solid #FF3B30;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.cancel-btn:active {
  background: #FF3B30;
  color: #ffffff;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx;
  background: #ffffff;
  border: none;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.action-btn.test {
  border: 2rpx solid #34C759;
}

.action-btn.refresh {
  border: 2rpx solid #007AFF;
}

.action-btn.clear {
  border: 2rpx solid #FF3B30;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 22rpx;
  font-weight: 500;
}

.action-btn.test .btn-text {
  color: #34C759;
}

.action-btn.refresh .btn-text {
  color: #007AFF;
}

.action-btn.clear .btn-text {
  color: #FF3B30;
}

/* 弹窗样式 */
.advance-time-modal,
.weekly-time-modal,
.monthly-time-modal,
.message-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.advance-time-modal.show,
.weekly-time-modal.show,
.monthly-time-modal.show,
.message-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
}

.time-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.time-option {
  padding: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  transition: all 0.2s ease;
}

.time-option.selected {
  background: #007AFF;
  border-color: #007AFF;
  color: #ffffff;
}

.time-option:active {
  transform: scale(0.95);
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.weekday-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.weekday-option {
  padding: 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  font-size: 22rpx;
  color: #333;
}

.weekday-option.selected {
  background: #007AFF;
  border-color: #007AFF;
  color: #ffffff;
}

.time-picker,
.day-picker {
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.message-input {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  resize: none;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 20rpx;
  color: #adb5bd;
  margin-top: 8rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #007AFF;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .time-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .weekday-options {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .reminder-settings-container {
    background: #1a1a1a;
  }
  
  .status-card,
  .setting-item,
  .notifications-list,
  .action-btn {
    background: #2a2a2a;
  }
  
  .section-title,
  .status-title,
  .item-title,
  .notification-title {
    color: #ffffff;
  }
  
  .notification-item {
    border-bottom-color: #404040;
  }
  
  .notification-icon,
  .item-icon {
    background: #1a3a5c;
  }
  
  .modal-content {
    background: #2a2a2a;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #404040;
  }
  
  .time-option,
  .weekday-option,
  .time-picker,
  .day-picker,
  .message-input {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .time-option.selected,
  .weekday-option.selected {
    background: #007AFF;
    border-color: #007AFF;
  }
}
