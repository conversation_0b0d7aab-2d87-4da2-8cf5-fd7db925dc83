<!-- pages/reminder-settings/reminder-settings.wxml -->
<!-- 提醒设置页面 -->

<view class="reminder-settings-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">提醒设置</text>
    <text class="page-subtitle">管理你的工作提醒和通知</text>
  </view>

  <!-- 通知权限状态 -->
  <view class="permission-status">
    <view class="status-card {{permissionGranted ? 'granted' : 'denied'}}">
      <view class="status-icon">{{permissionGranted ? '✅' : '❌'}}</view>
      <view class="status-content">
        <text class="status-title">{{permissionGranted ? '通知权限已开启' : '通知权限未开启'}}</text>
        <text class="status-desc">{{permissionGranted ? '可以正常接收提醒通知' : '开启权限以接收提醒通知'}}</text>
      </view>
      <button 
        class="permission-btn" 
        wx:if="{{!permissionGranted}}"
        bindtap="onRequestPermission"
      >
        开启权限
      </button>
    </view>
  </view>

  <!-- 总开关 -->
  <view class="settings-section">
    <view class="section-title">通知总开关</view>
    <view class="setting-item">
      <view class="item-left">
        <text class="item-icon">🔔</text>
        <view class="item-content">
          <text class="item-title">启用通知</text>
          <text class="item-desc">开启后可以接收各种提醒通知</text>
        </view>
      </view>
      <view class="item-right">
        <switch 
          checked="{{settings.enabled}}"
          bindchange="onMainSwitchChange"
          color="#007AFF"
        />
      </view>
    </view>
  </view>

  <!-- 工作提醒 -->
  <view class="settings-section">
    <view class="section-title">工作提醒</view>
    
    <view class="setting-item">
      <view class="item-left">
        <text class="item-icon">⏰</text>
        <view class="item-content">
          <text class="item-title">工作开始提醒</text>
          <text class="item-desc">在工作开始前提醒你</text>
        </view>
      </view>
      <view class="item-right">
        <switch 
          checked="{{settings.workReminder.enabled}}"
          bindchange="onWorkReminderChange"
          color="#007AFF"
          disabled="{{!settings.enabled}}"
        />
      </view>
    </view>
    
    <view class="setting-item" wx:if="{{settings.workReminder.enabled}}">
      <view class="item-left">
        <text class="item-icon">⏱️</text>
        <view class="item-content">
          <text class="item-title">提前提醒时间</text>
          <text class="item-desc">工作开始前{{settings.workReminder.advanceMinutes}}分钟提醒</text>
        </view>
      </view>
      <view class="item-right">
        <button class="time-btn" bindtap="onAdvanceTimeSelect">
          {{settings.workReminder.advanceMinutes}}分钟
        </button>
      </view>
    </view>
    
    <view class="setting-item" wx:if="{{settings.workReminder.enabled}}">
      <view class="item-left">
        <text class="item-icon">🌙</text>
        <view class="item-content">
          <text class="item-title">明日工作提醒</text>
          <text class="item-desc">每天{{settings.workReminder.tomorrowReminderTime}}提醒明日工作</text>
        </view>
      </view>
      <view class="item-right">
        <picker 
          mode="time" 
          value="{{settings.workReminder.tomorrowReminderTime}}" 
          bindchange="onTomorrowTimeChange"
        >
          <button class="time-btn">{{settings.workReminder.tomorrowReminderTime}}</button>
        </picker>
      </view>
    </view>
    
  </view>

  <!-- 统计提醒 -->
  <view class="settings-section">
    <view class="section-title">统计提醒</view>
    
    <view class="setting-item">
      <view class="item-left">
        <text class="item-icon">📊</text>
        <view class="item-content">
          <text class="item-title">启用统计提醒</text>
          <text class="item-desc">定期提醒查看工作统计</text>
        </view>
      </view>
      <view class="item-right">
        <switch 
          checked="{{settings.statsReminder.enabled}}"
          bindchange="onStatsReminderChange"
          color="#007AFF"
          disabled="{{!settings.enabled}}"
        />
      </view>
    </view>
    
    <!-- 周统计提醒 -->
    <view class="sub-section" wx:if="{{settings.statsReminder.enabled}}">
      <view class="sub-title">周统计提醒</view>
      
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📅</text>
          <view class="item-content">
            <text class="item-title">启用周提醒</text>
            <text class="item-desc">每周提醒查看工作统计</text>
          </view>
        </view>
        <view class="item-right">
          <switch 
            checked="{{settings.statsReminder.weekly.enabled}}"
            bindchange="onWeeklyReminderChange"
            color="#007AFF"
          />
        </view>
      </view>
      
      <view class="setting-item" wx:if="{{settings.statsReminder.weekly.enabled}}">
        <view class="item-left">
          <text class="item-icon">📆</text>
          <view class="item-content">
            <text class="item-title">提醒时间</text>
            <text class="item-desc">{{getWeekdayText(settings.statsReminder.weekly.dayOfWeek)}} {{settings.statsReminder.weekly.time}}</text>
          </view>
        </view>
        <view class="item-right">
          <button class="time-btn" bindtap="onWeeklyTimeSelect">设置</button>
        </view>
      </view>
    </view>
    
    <!-- 月统计提醒 -->
    <view class="sub-section" wx:if="{{settings.statsReminder.enabled}}">
      <view class="sub-title">月统计提醒</view>
      
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🗓️</text>
          <view class="item-content">
            <text class="item-title">启用月提醒</text>
            <text class="item-desc">每月提醒查看工作统计</text>
          </view>
        </view>
        <view class="item-right">
          <switch 
            checked="{{settings.statsReminder.monthly.enabled}}"
            bindchange="onMonthlyReminderChange"
            color="#007AFF"
          />
        </view>
      </view>
      
      <view class="setting-item" wx:if="{{settings.statsReminder.monthly.enabled}}">
        <view class="item-left">
          <text class="item-icon">📅</text>
          <view class="item-content">
            <text class="item-title">提醒时间</text>
            <text class="item-desc">每月{{settings.statsReminder.monthly.dayOfMonth}}号 {{settings.statsReminder.monthly.time}}</text>
          </view>
        </view>
        <view class="item-right">
          <button class="time-btn" bindtap="onMonthlyTimeSelect">设置</button>
        </view>
      </view>
    </view>
    
  </view>

  <!-- 每日提醒 -->
  <view class="settings-section">
    <view class="section-title">每日提醒</view>
    
    <view class="setting-item">
      <view class="item-left">
        <text class="item-icon">🌅</text>
        <view class="item-content">
          <text class="item-title">启用每日提醒</text>
          <text class="item-desc">每天定时提醒记录工作</text>
        </view>
      </view>
      <view class="item-right">
        <switch 
          checked="{{settings.dailyReminder.enabled}}"
          bindchange="onDailyReminderChange"
          color="#007AFF"
          disabled="{{!settings.enabled}}"
        />
      </view>
    </view>
    
    <view class="setting-item" wx:if="{{settings.dailyReminder.enabled}}">
      <view class="item-left">
        <text class="item-icon">⏰</text>
        <view class="item-content">
          <text class="item-title">提醒时间</text>
          <text class="item-desc">每天{{settings.dailyReminder.time}}提醒</text>
        </view>
      </view>
      <view class="item-right">
        <picker 
          mode="time" 
          value="{{settings.dailyReminder.time}}" 
          bindchange="onDailyTimeChange"
        >
          <button class="time-btn">{{settings.dailyReminder.time}}</button>
        </picker>
      </view>
    </view>
    
    <view class="setting-item" wx:if="{{settings.dailyReminder.enabled}}">
      <view class="item-left">
        <text class="item-icon">💬</text>
        <view class="item-content">
          <text class="item-title">提醒内容</text>
          <text class="item-desc">自定义提醒消息</text>
        </view>
      </view>
      <view class="item-right">
        <button class="edit-btn" bindtap="onEditMessage">编辑</button>
      </view>
    </view>
    
  </view>

  <!-- 已安排的提醒 -->
  <view class="settings-section" wx:if="{{scheduledNotifications.length > 0}}">
    <view class="section-title">已安排的提醒</view>
    <view class="notifications-list">
      <view 
        class="notification-item"
        wx:for="{{scheduledNotifications}}" 
        wx:key="id"
      >
        <view class="notification-icon">{{item.icon || '🔔'}}</view>
        <view class="notification-content">
          <text class="notification-title">{{item.title}}</text>
          <text class="notification-time">{{item.timeText}}</text>
        </view>
        <button 
          class="cancel-btn" 
          bindtap="onCancelNotification"
          data-id="{{item.id}}"
        >
          取消
        </button>
      </view>
    </view>
  </view>

  <!-- 测试和操作 -->
  <view class="settings-section">
    <view class="section-title">测试和管理</view>
    
    <view class="action-buttons">
      <button class="action-btn test" bindtap="onTestNotification">
        <text class="btn-icon">🧪</text>
        <text class="btn-text">测试通知</text>
      </button>
      
      <button class="action-btn refresh" bindtap="onRefreshNotifications">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">刷新提醒</text>
      </button>
      
      <button class="action-btn clear" bindtap="onClearAllNotifications">
        <text class="btn-icon">🗑️</text>
        <text class="btn-text">清除所有</text>
      </button>
    </view>
  </view>
</view>

<!-- 提前时间选择弹窗 -->
<view class="advance-time-modal {{showAdvanceTimeModal ? 'show' : ''}}" bindtap="onCloseAdvanceTimeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择提前时间</text>
      <button class="modal-close" bindtap="onCloseAdvanceTimeModal">×</button>
    </view>
    <view class="modal-body">
      <view class="time-options">
        <button 
          class="time-option {{advanceMinutes === item ? 'selected' : ''}}"
          wx:for="{{advanceTimeOptions}}" 
          wx:key="*this"
          bindtap="onAdvanceTimeConfirm"
          data-minutes="{{item}}"
        >
          {{item}}分钟
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 周提醒时间设置弹窗 -->
<view class="weekly-time-modal {{showWeeklyTimeModal ? 'show' : ''}}" bindtap="onCloseWeeklyTimeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">设置周提醒时间</text>
      <button class="modal-close" bindtap="onCloseWeeklyTimeModal">×</button>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <label class="form-label">星期几</label>
        <view class="weekday-options">
          <button 
            class="weekday-option {{weeklyDayOfWeek === index ? 'selected' : ''}}"
            wx:for="{{weekdayOptions}}" 
            wx:key="*this"
            bindtap="onWeekdaySelect"
            data-day="{{index}}"
          >
            {{item}}
          </button>
        </view>
      </view>
      <view class="form-group">
        <label class="form-label">时间</label>
        <picker 
          mode="time" 
          value="{{weeklyTime}}" 
          bindchange="onWeeklyTimePickerChange"
        >
          <view class="time-picker">{{weeklyTime}}</view>
        </picker>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseWeeklyTimeModal">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmWeeklyTime">确定</button>
    </view>
  </view>
</view>

<!-- 月提醒时间设置弹窗 -->
<view class="monthly-time-modal {{showMonthlyTimeModal ? 'show' : ''}}" bindtap="onCloseMonthlyTimeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">设置月提醒时间</text>
      <button class="modal-close" bindtap="onCloseMonthlyTimeModal">×</button>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <label class="form-label">每月几号</label>
        <picker 
          range="{{monthDayOptions}}" 
          value="{{monthlyDayIndex}}" 
          bindchange="onMonthlyDayChange"
        >
          <view class="day-picker">{{monthlyDayOfMonth}}号</view>
        </picker>
      </view>
      <view class="form-group">
        <label class="form-label">时间</label>
        <picker 
          mode="time" 
          value="{{monthlyTime}}" 
          bindchange="onMonthlyTimePickerChange"
        >
          <view class="time-picker">{{monthlyTime}}</view>
        </picker>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseMonthlyTimeModal">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmMonthlyTime">确定</button>
    </view>
  </view>
</view>

<!-- 消息编辑弹窗 -->
<view class="message-modal {{showMessageModal ? 'show' : ''}}" bindtap="onCloseMessageModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑提醒消息</text>
      <button class="modal-close" bindtap="onCloseMessageModal">×</button>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <label class="form-label">提醒内容</label>
        <textarea 
          class="message-input"
          placeholder="请输入提醒消息"
          value="{{dailyMessage}}"
          bindinput="onMessageInput"
          maxlength="50"
          auto-height
        />
        <text class="char-count">{{dailyMessage.length}}/50</text>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseMessageModal">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmMessage">确定</button>
    </view>
  </view>
</view>
