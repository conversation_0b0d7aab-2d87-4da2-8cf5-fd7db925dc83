// pages/reminder-settings/reminder-settings.js
// 提醒设置页面

import NotificationManager from "../../utils/notification.js";
import DateUtils from "../../utils/date.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    permissionGranted: false,
    settings: {},
    scheduledNotifications: [],

    // 弹窗状态
    showAdvanceTimeModal: false,
    showWeeklyTimeModal: false,
    showMonthlyTimeModal: false,
    showMessageModal: false,

    // 表单数据
    advanceMinutes: 15,
    weeklyDayOfWeek: 0,
    weeklyTime: "19:00",
    monthlyDayOfMonth: 1,
    monthlyDayIndex: 0,
    monthlyTime: "10:00",
    dailyMessage: "",

    // 选项数据
    advanceTimeOptions: [5, 10, 15, 30, 60, 120],
    weekdayOptions: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
    monthDayOptions: Array.from({ length: 31 }, (_, i) => `${i + 1}号`),
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadSettings();
    this.loadScheduledNotifications();
    this.checkPermission();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadScheduledNotifications();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理可能的定时器和监听器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  },

  /**
   * 检查权限状态
   */
  checkPermission() {
    // 在小程序中，通知权限检查比较复杂，这里简化处理
    this.setData({ permissionGranted: true });
  },

  /**
   * 加载设置
   */
  loadSettings() {
    const settings = NotificationManager.getNotificationSettings();
    this.setData({
      settings,
      advanceMinutes: settings.workReminder.advanceMinutes,
      weeklyDayOfWeek: settings.statsReminder.weekly.dayOfWeek,
      weeklyTime: settings.statsReminder.weekly.time,
      monthlyDayOfMonth: settings.statsReminder.monthly.dayOfMonth,
      monthlyDayIndex: settings.statsReminder.monthly.dayOfMonth - 1,
      monthlyTime: settings.statsReminder.monthly.time,
      dailyMessage: settings.dailyReminder.message || "记得记录今天的工作哦！",
    });
  },

  /**
   * 加载已安排的通知
   */
  loadScheduledNotifications() {
    const notifications = NotificationManager.getScheduledNotifications();
    const processedNotifications = notifications.map((notification) => ({
      ...notification,
      timeText: DateUtils.formatDateTime(new Date(notification.time)),
      icon: this.getNotificationIcon(notification.data?.type),
    }));

    this.setData({ scheduledNotifications: processedNotifications });
  },

  /**
   * 获取通知图标
   */
  getNotificationIcon(type) {
    const iconMap = {
      work: "⏰",
      stats: "📊",
      daily: "🌅",
      test: "🧪",
    };
    return iconMap[type] || "🔔";
  },

  /**
   * 获取星期几文本
   */
  getWeekdayText(dayOfWeek) {
    return this.data.weekdayOptions[dayOfWeek] || "周日";
  },

  /**
   * 请求权限
   */
  async onRequestPermission() {
    const granted = await NotificationManager.requestPermission();
    this.setData({ permissionGranted: granted });

    if (granted) {
      wx.showToast({
        title: "权限已开启",
        icon: "success",
      });
    }
  },

  /**
   * 主开关变化
   */
  onMainSwitchChange(e) {
    const settings = { ...this.data.settings };
    settings.enabled = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 工作提醒开关变化
   */
  onWorkReminderChange(e) {
    const settings = { ...this.data.settings };
    settings.workReminder.enabled = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 统计提醒开关变化
   */
  onStatsReminderChange(e) {
    const settings = { ...this.data.settings };
    settings.statsReminder.enabled = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 周提醒开关变化
   */
  onWeeklyReminderChange(e) {
    const settings = { ...this.data.settings };
    settings.statsReminder.weekly.enabled = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 月提醒开关变化
   */
  onMonthlyReminderChange(e) {
    const settings = { ...this.data.settings };
    settings.statsReminder.monthly.enabled = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 每日提醒开关变化
   */
  onDailyReminderChange(e) {
    const settings = { ...this.data.settings };
    settings.dailyReminder.enabled = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 明日提醒时间变化
   */
  onTomorrowTimeChange(e) {
    const settings = { ...this.data.settings };
    settings.workReminder.tomorrowReminderTime = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 每日提醒时间变化
   */
  onDailyTimeChange(e) {
    const settings = { ...this.data.settings };
    settings.dailyReminder.time = e.detail.value;

    this.setData({ settings });
    this.saveSettings(settings);
  },

  /**
   * 提前时间选择
   */
  onAdvanceTimeSelect() {
    this.setData({ showAdvanceTimeModal: true });
  },

  /**
   * 确认提前时间
   */
  onAdvanceTimeConfirm(e) {
    const { minutes } = e.currentTarget.dataset;
    const settings = { ...this.data.settings };
    settings.workReminder.advanceMinutes = minutes;

    this.setData({
      settings,
      advanceMinutes: minutes,
      showAdvanceTimeModal: false,
    });
    this.saveSettings(settings);
  },

  /**
   * 周提醒时间设置
   */
  onWeeklyTimeSelect() {
    this.setData({ showWeeklyTimeModal: true });
  },

  /**
   * 星期几选择
   */
  onWeekdaySelect(e) {
    const { day } = e.currentTarget.dataset;
    this.setData({ weeklyDayOfWeek: day });
  },

  /**
   * 周提醒时间选择器变化
   */
  onWeeklyTimePickerChange(e) {
    this.setData({ weeklyTime: e.detail.value });
  },

  /**
   * 确认周提醒时间
   */
  onConfirmWeeklyTime() {
    const settings = { ...this.data.settings };
    settings.statsReminder.weekly.dayOfWeek = this.data.weeklyDayOfWeek;
    settings.statsReminder.weekly.time = this.data.weeklyTime;

    this.setData({
      settings,
      showWeeklyTimeModal: false,
    });
    this.saveSettings(settings);
  },

  /**
   * 月提醒时间设置
   */
  onMonthlyTimeSelect() {
    this.setData({ showMonthlyTimeModal: true });
  },

  /**
   * 月份日期变化
   */
  onMonthlyDayChange(e) {
    const dayIndex = e.detail.value;
    const dayOfMonth = dayIndex + 1;

    this.setData({
      monthlyDayIndex: dayIndex,
      monthlyDayOfMonth: dayOfMonth,
    });
  },

  /**
   * 月提醒时间选择器变化
   */
  onMonthlyTimePickerChange(e) {
    this.setData({ monthlyTime: e.detail.value });
  },

  /**
   * 确认月提醒时间
   */
  onConfirmMonthlyTime() {
    const settings = { ...this.data.settings };
    settings.statsReminder.monthly.dayOfMonth = this.data.monthlyDayOfMonth;
    settings.statsReminder.monthly.time = this.data.monthlyTime;

    this.setData({
      settings,
      showMonthlyTimeModal: false,
    });
    this.saveSettings(settings);
  },

  /**
   * 编辑消息
   */
  onEditMessage() {
    this.setData({ showMessageModal: true });
  },

  /**
   * 消息输入
   */
  onMessageInput(e) {
    this.setData({ dailyMessage: e.detail.value });
  },

  /**
   * 确认消息
   */
  onConfirmMessage() {
    const settings = { ...this.data.settings };
    settings.dailyReminder.message = this.data.dailyMessage;

    this.setData({
      settings,
      showMessageModal: false,
    });
    this.saveSettings(settings);
  },

  /**
   * 取消通知
   */
  onCancelNotification(e) {
    const { id } = e.currentTarget.dataset;

    wx.showModal({
      title: "确认取消",
      content: "确定要取消这个提醒吗？",
      success: (res) => {
        if (res.confirm) {
          NotificationManager.clearNotification(id);
          this.loadScheduledNotifications();

          wx.showToast({
            title: "已取消提醒",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 测试通知
   */
  onTestNotification() {
    NotificationManager.testNotification();

    // 延迟刷新通知列表
    setTimeout(() => {
      this.loadScheduledNotifications();
    }, 1000);
  },

  /**
   * 刷新提醒
   */
  onRefreshNotifications() {
    wx.showLoading({ title: "刷新中..." });

    NotificationManager.scheduleNotifications()
      .then(() => {
        this.loadScheduledNotifications();

        wx.hideLoading();
        wx.showToast({
          title: "刷新完成",
          icon: "success",
        });
      })
      .catch((error) => {
        wx.hideLoading();
        console.error("刷新提醒失败:", error);
        wx.showToast({
          title: "刷新失败",
          icon: "none",
        });
      });
  },

  /**
   * 清除所有通知
   */
  onClearAllNotifications() {
    wx.showModal({
      title: "确认清除",
      content: "确定要清除所有已安排的提醒吗？",
      success: (res) => {
        if (res.confirm) {
          NotificationManager.clearAllNotifications();
          this.loadScheduledNotifications();

          wx.showToast({
            title: "已清除所有提醒",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 保存设置
   */
  saveSettings(settings) {
    NotificationManager.saveNotificationSettings(settings);

    wx.showToast({
      title: "设置已保存",
      icon: "success",
      duration: 1000,
    });
  },

  /**
   * 关闭提前时间弹窗
   */
  onCloseAdvanceTimeModal() {
    this.setData({ showAdvanceTimeModal: false });
  },

  /**
   * 关闭周提醒时间弹窗
   */
  onCloseWeeklyTimeModal() {
    this.setData({ showWeeklyTimeModal: false });
  },

  /**
   * 关闭月提醒时间弹窗
   */
  onCloseMonthlyTimeModal() {
    this.setData({ showMonthlyTimeModal: false });
  },

  /**
   * 关闭消息弹窗
   */
  onCloseMessageModal() {
    this.setData({ showMessageModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: "提醒设置 - 兼职工作管理",
      path: "/pages/reminder-settings/reminder-settings",
    };
  },
});
