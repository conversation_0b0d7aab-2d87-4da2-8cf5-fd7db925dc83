<!-- pages/work-detail/work-detail.wxml -->
<!-- 工作详情页面 -->

<view class="work-detail-container">
  <!-- 页面头部 -->
  <view class="detail-header" style="background: linear-gradient(135deg, {{workRecord.workTypeColor || '#007AFF'}} 0%, {{workRecord.workTypeColor || '#5AC8FA'}} 100%)">
    <view class="header-content">
      <view class="work-type-info">
        <view class="type-icon" style="background-color: rgba(255,255,255,0.2)">
          <text class="icon">{{workRecord.workTypeIcon || '📋'}}</text>
        </view>
        <view class="type-details">
          <text class="type-name">{{workRecord.workTypeName || '工作记录'}}</text>
          <text class="work-title" wx:if="{{workRecord.title}}">{{workRecord.title}}</text>
        </view>
      </view>
      <view class="header-actions">
        <button class="action-btn" bindtap="onEdit">
          <text class="btn-icon">✏️</text>
        </button>
        <button class="action-btn" bindtap="onDelete">
          <text class="btn-icon">🗑️</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 工作状态 -->
  <view class="status-section">
    <view class="status-card">
      <view class="status-indicator {{workRecord.status}}">
        <text class="status-icon">{{statusConfig[workRecord.status].icon}}</text>
        <text class="status-text">{{statusConfig[workRecord.status].text}}</text>
      </view>
      <button 
        class="status-btn" 
        wx:if="{{canChangeStatus}}"
        bindtap="onChangeStatus"
      >
        {{getNextStatusText()}}
      </button>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="info-section">
    <view class="section-title">基本信息</view>
    <view class="info-grid">
      <view class="info-item">
        <text class="info-label">工作日期</text>
        <text class="info-value">
          {{workRecord.date}}
          <text class="weekday">{{workRecord.weekday}}</text>
        </text>
      </view>
      
      <view class="info-item" wx:if="{{workRecord.isMultiDay}}">
        <text class="info-label">结束日期</text>
        <text class="info-value">{{workRecord.endDate}}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">工作时间</text>
        <text class="info-value">{{workRecord.startTime}} - {{workRecord.endTime}}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">工作时长</text>
        <text class="info-value">{{workRecord.formattedDuration}}</text>
      </view>
      
      <view class="info-item" wx:if="{{workRecord.location}}">
        <text class="info-label">工作地点</text>
        <text class="info-value">{{workRecord.location}}</text>
      </view>
    </view>
  </view>

  <!-- 薪资信息 -->
  <view class="salary-section">
    <view class="section-title">薪资信息</view>
    <view class="salary-card">
      <view class="salary-main">
        <view class="salary-amount">
          <text class="amount-value">¥{{workRecord.totalSalary}}</text>
          <text class="amount-label">{{getSalaryTypeLabel()}}</text>
        </view>
        <view class="salary-rate">
          <text class="rate-text">{{workRecord.hourlyRate}}</text>
          <text class="rate-unit">{{getRateUnit()}}</text>
        </view>
      </view>
      
      <!-- 实际收入 -->
      <view class="actual-salary" wx:if="{{workRecord.actualSalary !== null}}">
        <view class="actual-info">
          <text class="actual-label">实际收入</text>
          <text class="actual-value">¥{{workRecord.actualSalary}}</text>
        </view>
        <view class="salary-diff {{getSalaryDiffClass()}}">
          <text class="diff-text">{{getSalaryDiffText()}}</text>
        </view>
      </view>
      
      <!-- 设置实际收入 -->
      <button 
        class="set-actual-btn" 
        wx:if="{{workRecord.actualSalary === null && workRecord.status === 'completed'}}"
        bindtap="onSetActualSalary"
      >
        设置实际收入
      </button>
    </view>
  </view>

  <!-- 工作描述 -->
  <view class="description-section" wx:if="{{workRecord.description}}">
    <view class="section-title">工作描述</view>
    <view class="description-content">
      <text class="description-text">{{workRecord.description}}</text>
    </view>
  </view>

  <!-- 备注信息 -->
  <view class="notes-section" wx:if="{{workRecord.notes}}">
    <view class="section-title">备注</view>
    <view class="notes-content">
      <text class="notes-text">{{workRecord.notes}}</text>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="section-title">统计信息</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{workRecord.durationHours}}h</text>
        <text class="stat-label">工作时长</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">¥{{workRecord.averageHourlyRate}}</text>
        <text class="stat-label">平均时薪</text>
      </view>
      <view class="stat-item" wx:if="{{workRecord.isMultiDay}}">
        <text class="stat-value">{{workRecord.workDays}}</text>
        <text class="stat-label">工作天数</text>
      </view>
    </view>
  </view>

  <!-- 操作记录 -->
  <view class="history-section" wx:if="{{showHistory}}">
    <view class="section-title">操作记录</view>
    <view class="history-list">
      <view class="history-item">
        <text class="history-time">{{workRecord.createdAt}}</text>
        <text class="history-action">创建记录</text>
      </view>
      <view class="history-item" wx:if="{{workRecord.updatedAt !== workRecord.createdAt}}">
        <text class="history-time">{{workRecord.updatedAt}}</text>
        <text class="history-action">最后修改</text>
      </view>
    </view>
  </view>
</view>

<!-- 实际收入设置弹窗 -->
<view class="actual-salary-modal {{showActualSalaryModal ? 'show' : ''}}" bindtap="onCloseActualSalaryModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">设置实际收入</text>
      <button class="modal-close" bindtap="onCloseActualSalaryModal">×</button>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <label class="form-label">实际收入金额</label>
        <input 
          class="form-input"
          type="digit"
          placeholder="请输入实际收到的金额"
          value="{{actualSalaryInput}}"
          bindinput="onActualSalaryInput"
        />
      </view>
      <view class="expected-info">
        <text class="expected-text">预期收入：¥{{workRecord.totalSalary}}</text>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseActualSalaryModal">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmActualSalary">确定</button>
    </view>
  </view>
</view>

<!-- 删除确认弹窗 -->
<view class="delete-modal {{showDeleteModal ? 'show' : ''}}" bindtap="onCloseDeleteModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">确认删除</text>
    </view>
    <view class="modal-body">
      <text class="delete-warning">确定要删除这条工作记录吗？删除后无法恢复。</text>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseDeleteModal">取消</button>
      <button class="modal-btn danger" bindtap="onConfirmDelete">删除</button>
    </view>
  </view>
</view>
