/* pages/work-detail/work-detail.wxss */
/* 工作详情页面样式 */

.work-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.detail-header {
  padding: 32rpx;
  color: #ffffff;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.work-type-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.type-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  font-size: 32rpx;
}

.type-details {
  flex: 1;
}

.type-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.work-title {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 状态区域 */
.status-section {
  margin: 24rpx 32rpx;
}

.status-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-icon {
  font-size: 32rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
}

.status-indicator.planned {
  color: #007AFF;
}

.status-indicator.in_progress {
  color: #FF9500;
}

.status-indicator.completed {
  color: #34C759;
}

.status-indicator.cancelled {
  color: #8E8E93;
}

.status-btn {
  padding: 12rpx 24rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-btn:active {
  background: #0056CC;
}

/* 信息区域 */
.info-section,
.salary-section,
.description-section,
.notes-section,
.stats-section,
.history-section {
  margin: 24rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

/* 基本信息 */
.info-grid {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
}

.weekday {
  font-size: 22rpx;
  color: #999;
  margin-left: 8rpx;
}

/* 薪资信息 */
.salary-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.salary-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.salary-amount {
  text-align: left;
}

.amount-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #34C759;
  margin-bottom: 4rpx;
}

.amount-label {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.salary-rate {
  text-align: right;
}

.rate-text {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.rate-unit {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 实际收入 */
.actual-salary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.actual-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.actual-label {
  font-size: 26rpx;
  color: #666;
}

.actual-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.salary-diff {
  font-size: 24rpx;
  font-weight: 500;
}

.salary-diff.positive {
  color: #34C759;
}

.salary-diff.negative {
  color: #FF3B30;
}

.salary-diff.equal {
  color: #8E8E93;
}

.set-actual-btn {
  width: 100%;
  padding: 20rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.set-actual-btn:active {
  background: #0056CC;
}

/* 描述和备注 */
.description-content,
.notes-content {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.description-text,
.notes-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 统计信息 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 操作记录 */
.history-list {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  font-size: 24rpx;
  color: #666;
}

.history-action {
  font-size: 26rpx;
  color: #333;
}

/* 弹窗样式 */
.actual-salary-modal,
.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.actual-salary-modal.show,
.delete-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.expected-info {
  text-align: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.expected-text {
  font-size: 24rpx;
  color: #666;
}

.delete-warning {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-align: center;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #007AFF;
  color: #ffffff;
}

.modal-btn.danger {
  background: #FF3B30;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .salary-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .salary-rate {
    text-align: left;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .work-detail-container {
    background: #1a1a1a;
  }
  
  .status-card,
  .info-grid,
  .salary-card,
  .description-content,
  .notes-content,
  .stat-item,
  .history-list {
    background: #2a2a2a;
  }
  
  .section-title,
  .info-value,
  .rate-text,
  .actual-value,
  .description-text,
  .notes-text,
  .history-action {
    color: #ffffff;
  }
  
  .info-label,
  .amount-label,
  .rate-unit,
  .actual-label,
  .history-time {
    color: #adb5bd;
  }
  
  .info-item,
  .history-item {
    border-bottom-color: #404040;
  }
  
  .actual-salary {
    background: #404040;
  }
  
  .modal-content {
    background: #2a2a2a;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #404040;
  }
  
  .form-input {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .expected-info {
    background: #404040;
  }
}
