// pages/work-detail/work-detail.js
// 工作详情页面

import DateUtils from '../../utils/date.js'
import SalaryCalculator from '../../utils/salary.js'
import { workRecordStorage, workTypeStorage } from '../../utils/storage.js'
import { WorkRecord } from '../../data/work-record.js'
import { WORK_RECORD_STATUS, SALARY_TYPES } from '../../data/constants.js'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    recordId: '',
    workRecord: {},
    canChangeStatus: false,
    showHistory: false,
    showActualSalaryModal: false,
    showDeleteModal: false,
    actualSalaryInput: '',
    
    // 状态配置
    statusConfig: {
      planned: { icon: '📅', text: '计划中' },
      in_progress: { icon: '⏳', text: '进行中' },
      completed: { icon: '✅', text: '已完成' },
      cancelled: { icon: '❌', text: '已取消' }
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id, date, ids } = options
    
    if (id) {
      // 单个记录详情
      this.setData({ recordId: id })
      this.loadWorkRecord()
    } else if (date && ids) {
      // 多个记录（暂时显示第一个）
      const recordIds = ids.split(',')
      this.setData({ recordId: recordIds[0] })
      this.loadWorkRecord()
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从编辑页面返回时刷新数据
    if (this.data.recordId) {
      this.loadWorkRecord()
    }
  },

  /**
   * 加载工作记录
   */
  async loadWorkRecord() {
    try {
      const result = await workRecordStorage.getById(this.data.recordId)
      
      if (result.success) {
        const record = result.data
        await this.processWorkRecord(record)
      } else {
        wx.showToast({
          title: '记录不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载工作记录失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 处理工作记录数据
   */
  async processWorkRecord(record) {
    try {
      // 加载工作类型信息
      const workTypeResult = await workTypeStorage.getById(record.workTypeId)
      const workType = workTypeResult.success ? workTypeResult.data : null

      // 计算相关数据
      const workRecord = new WorkRecord(record)
      const formattedDuration = DateUtils.formatDuration(workRecord.duration)
      const durationHours = DateUtils.minutesToHours(workRecord.duration)
      const averageHourlyRate = SalaryCalculator.calculateAverageHourlyRate(
        workRecord.totalSalary, 
        workRecord.duration
      )

      // 计算工作天数（多日工作）
      let workDays = 1
      if (workRecord.isMultiDay && workRecord.endDate) {
        workDays = DateUtils.getDaysDiff(workRecord.date, workRecord.endDate) + 1
      }

      // 格式化时间
      const createdAt = DateUtils.formatDateTime(new Date(workRecord.createdAt))
      const updatedAt = DateUtils.formatDateTime(new Date(workRecord.updatedAt))
      const weekday = DateUtils.getWeekday(workRecord.date)

      const processedRecord = {
        ...workRecord.toStorage(),
        workTypeName: workType?.name || '未知类型',
        workTypeColor: workType?.color || '#007AFF',
        workTypeIcon: workType?.icon || '📋',
        formattedDuration,
        durationHours: durationHours.toFixed(1),
        averageHourlyRate: averageHourlyRate.toFixed(0),
        workDays,
        weekday,
        createdAt,
        updatedAt
      }

      this.setData({
        workRecord: processedRecord,
        canChangeStatus: this.canChangeStatus(workRecord.status),
        showHistory: true
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: workType?.name || '工作详情'
      })

    } catch (error) {
      console.error('处理工作记录数据失败:', error)
    }
  },

  /**
   * 判断是否可以改变状态
   */
  canChangeStatus(status) {
    return status === 'planned' || status === 'in_progress'
  },

  /**
   * 获取下一个状态文本
   */
  getNextStatusText() {
    const { status } = this.data.workRecord
    
    switch (status) {
      case 'planned':
        return '开始工作'
      case 'in_progress':
        return '完成工作'
      default:
        return ''
    }
  },

  /**
   * 获取薪资类型标签
   */
  getSalaryTypeLabel() {
    const { salaryType } = this.data.workRecord
    
    switch (salaryType) {
      case SALARY_TYPES.HOURLY:
        return '时薪收入'
      case SALARY_TYPES.DAILY:
        return '日薪收入'
      case SALARY_TYPES.PROJECT:
        return '项目收入'
      case SALARY_TYPES.PIECE:
        return '计件收入'
      default:
        return '总收入'
    }
  },

  /**
   * 获取薪资单位
   */
  getRateUnit() {
    const { salaryType } = this.data.workRecord
    
    switch (salaryType) {
      case SALARY_TYPES.HOURLY:
        return '元/小时'
      case SALARY_TYPES.DAILY:
        return '元/天'
      case SALARY_TYPES.PROJECT:
        return '元/项目'
      case SALARY_TYPES.PIECE:
        return '元/件'
      default:
        return '元'
    }
  },

  /**
   * 获取薪资差异样式类
   */
  getSalaryDiffClass() {
    const { totalSalary, actualSalary } = this.data.workRecord
    
    if (actualSalary === null) return ''
    
    if (actualSalary > totalSalary) return 'positive'
    if (actualSalary < totalSalary) return 'negative'
    return 'equal'
  },

  /**
   * 获取薪资差异文本
   */
  getSalaryDiffText() {
    const { totalSalary, actualSalary } = this.data.workRecord
    
    if (actualSalary === null) return ''
    
    const diff = actualSalary - totalSalary
    
    if (diff > 0) return `+¥${diff.toFixed(2)}`
    if (diff < 0) return `-¥${Math.abs(diff).toFixed(2)}`
    return '与预期一致'
  },

  /**
   * 编辑记录
   */
  onEdit() {
    wx.navigateTo({
      url: `/pages/add-work/add-work?id=${this.data.recordId}`
    })
  },

  /**
   * 删除记录
   */
  onDelete() {
    this.setData({ showDeleteModal: true })
  },

  /**
   * 改变状态
   */
  async onChangeStatus() {
    const { status } = this.data.workRecord
    let newStatus = ''
    
    switch (status) {
      case 'planned':
        newStatus = 'in_progress'
        break
      case 'in_progress':
        newStatus = 'completed'
        break
      default:
        return
    }

    try {
      const workRecord = new WorkRecord(this.data.workRecord)
      workRecord.status = newStatus
      workRecord.updatedAt = Date.now()

      const result = await workRecordStorage.save(workRecord.toStorage())
      
      if (result.success) {
        wx.showToast({
          title: '状态更新成功',
          icon: 'success'
        })
        this.loadWorkRecord()
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    }
  },

  /**
   * 设置实际收入
   */
  onSetActualSalary() {
    this.setData({
      showActualSalaryModal: true,
      actualSalaryInput: this.data.workRecord.totalSalary.toString()
    })
  },

  /**
   * 实际收入输入
   */
  onActualSalaryInput(e) {
    this.setData({ actualSalaryInput: e.detail.value })
  },

  /**
   * 确认设置实际收入
   */
  async onConfirmActualSalary() {
    const actualSalary = parseFloat(this.data.actualSalaryInput)
    
    if (isNaN(actualSalary) || actualSalary < 0) {
      wx.showToast({
        title: '请输入正确的金额',
        icon: 'none'
      })
      return
    }

    try {
      const workRecord = new WorkRecord(this.data.workRecord)
      workRecord.actualSalary = actualSalary
      workRecord.updatedAt = Date.now()

      const result = await workRecordStorage.save(workRecord.toStorage())
      
      if (result.success) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        })
        this.setData({ showActualSalaryModal: false })
        this.loadWorkRecord()
      }
    } catch (error) {
      console.error('设置实际收入失败:', error)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    }
  },

  /**
   * 确认删除
   */
  async onConfirmDelete() {
    try {
      const result = await workRecordStorage.delete(this.data.recordId)
      
      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('删除失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
    
    this.setData({ showDeleteModal: false })
  },

  /**
   * 关闭实际收入弹窗
   */
  onCloseActualSalaryModal() {
    this.setData({ showActualSalaryModal: false })
  },

  /**
   * 关闭删除弹窗
   */
  onCloseDeleteModal() {
    this.setData({ showDeleteModal: false })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '我的工作记录',
      path: `/pages/work-detail/work-detail?id=${this.data.recordId}`
    }
  }
})
