/* pages/feedback/feedback.wxss */
/* 意见反馈页面样式 */

.feedback-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 反馈类型选择 */
.feedback-types {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.type-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.type-option.selected {
  border-color: #007AFF;
  background: rgba(0, 122, 255, 0.05);
}

.type-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.type-name {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.check-mark {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
}

/* 反馈表单 */
.feedback-form {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 32rpx;
  position: relative;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;
  box-sizing: border-box;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #007AFF;
}

.char-count {
  position: absolute;
  right: 16rpx;
  bottom: -32rpx;
  font-size: 24rpx;
  color: #999999;
}

/* 图片上传 */
.image-upload {
  margin-top: 16rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e5;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background: #FF3B30;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.add-image-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.add-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #999999;
}

.add-text {
  font-size: 24rpx;
  color: #999999;
}

.image-tip {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  gap: 24rpx;
}

.submit-btn {
  flex: 2;
  height: 88rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.loading {
  background: #cccccc;
}

.submit-btn:active {
  background: #0056CC;
}

.history-btn {
  flex: 1;
  height: 88rpx;
  background: #f8f9fa;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 44rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-btn:active {
  background: #e5e5e5;
}
