<!-- pages/feedback/feedback.wxml -->
<!-- 意见反馈页面 -->

<view class="feedback-container">
  <!-- 反馈类型选择 -->
  <view class="feedback-types">
    <view class="section-title">反馈类型</view>
    <view class="type-options">
      <view 
        class="type-option {{feedbackType === item.value ? 'selected' : ''}}"
        wx:for="{{feedbackTypes}}" 
        wx:key="value"
        bindtap="onTypeSelect"
        data-type="{{item.value}}"
      >
        <view class="type-icon" style="color: {{item.color}}">{{item.icon}}</view>
        <text class="type-name">{{item.name}}</text>
        <view class="check-mark" wx:if="{{feedbackType === item.value}}">✓</view>
      </view>
    </view>
  </view>

  <!-- 反馈表单 -->
  <view class="feedback-form">
    <view class="form-group">
      <label class="form-label">反馈标题 *</label>
      <input 
        class="form-input"
        type="text"
        placeholder="请简要描述您的问题或建议"
        value="{{formData.title}}"
        bindinput="onFormInput"
        data-field="title"
        maxlength="50"
      />
      <text class="char-count">{{formData.title.length}}/50</text>
    </view>

    <view class="form-group">
      <label class="form-label">详细描述 *</label>
      <textarea 
        class="form-textarea"
        placeholder="请详细描述您遇到的问题或建议，我们会认真处理"
        value="{{formData.content}}"
        bindinput="onFormInput"
        data-field="content"
        maxlength="500"
        auto-height
        show-confirm-bar="{{false}}"
      />
      <text class="char-count">{{formData.content.length}}/500</text>
    </view>

    <view class="form-group">
      <label class="form-label">联系方式（可选）</label>
      <input 
        class="form-input"
        type="text"
        placeholder="微信号、邮箱或手机号，便于我们联系您"
        value="{{formData.contact}}"
        bindinput="onFormInput"
        data-field="contact"
        maxlength="50"
      />
    </view>

    <!-- 图片上传 -->
    <view class="form-group">
      <label class="form-label">相关截图（可选）</label>
      <view class="image-upload">
        <view class="image-list">
          <view 
            class="image-item"
            wx:for="{{images}}" 
            wx:key="index"
          >
            <image 
              class="uploaded-image" 
              src="{{item.path}}" 
              mode="aspectFill"
              bindtap="onPreviewImage"
              data-index="{{index}}"
            />
            <view 
              class="delete-btn"
              bindtap="onDeleteImage"
              data-index="{{index}}"
            >×</view>
          </view>
          
          <view 
            class="add-image-btn {{images.length >= maxImages ? 'disabled' : ''}}"
            bindtap="onAddImage"
            wx:if="{{images.length < maxImages}}"
          >
            <text class="add-icon">📷</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <text class="image-tip">最多上传{{maxImages}}张图片，支持截图说明问题</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="submit-btn {{submitting ? 'loading' : ''}}"
      bindtap="onSubmitFeedback"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交反馈'}}
    </button>
    
    <button 
      class="history-btn"
      bindtap="onViewHistory"
    >
      查看历史反馈
    </button>
  </view>
</view>
