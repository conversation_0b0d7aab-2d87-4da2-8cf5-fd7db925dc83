// pages/feedback/feedback.js
// 意见反馈页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    feedbackType: 'bug',
    feedbackTypes: [
      { value: 'bug', name: '问题反馈', icon: '🐛', color: '#FF3B30' },
      { value: 'feature', name: '功能建议', icon: '💡', color: '#007AFF' },
      { value: 'improvement', name: '体验优化', icon: '⚡', color: '#FF9500' },
      { value: 'other', name: '其他意见', icon: '💬', color: '#34C759' }
    ],
    formData: {
      title: '',
      content: '',
      contact: ''
    },
    images: [],
    maxImages: 3,
    submitting: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 获取设备信息用于反馈
    this.getDeviceInfo()
  },

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.deviceInfo = {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight
      }
    } catch (error) {
      console.warn('获取设备信息失败:', error)
      this.deviceInfo = {}
    }
  },

  /**
   * 反馈类型选择
   */
  onTypeSelect(e) {
    const { type } = e.currentTarget.dataset
    this.setData({ feedbackType: type })
  },

  /**
   * 表单输入
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    const formData = { ...this.data.formData }
    formData[field] = value
    
    this.setData({ formData })
  },

  /**
   * 添加图片
   */
  onAddImage() {
    const { images, maxImages } = this.data
    
    if (images.length >= maxImages) {
      wx.showToast({
        title: `最多只能上传${maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: maxImages - images.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = res.tempFiles.map(file => ({
          path: file.tempFilePath,
          size: file.size
        }))
        
        this.setData({
          images: [...images, ...newImages]
        })
      }
    })
  },

  /**
   * 删除图片
   */
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset
    const images = [...this.data.images]
    images.splice(index, 1)
    
    this.setData({ images })
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { index } = e.currentTarget.dataset
    const { images } = this.data
    
    wx.previewImage({
      current: images[index].path,
      urls: images.map(img => img.path)
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    
    if (!formData.title.trim()) {
      wx.showToast({
        title: '请输入反馈标题',
        icon: 'none'
      })
      return false
    }
    
    if (formData.title.length > 50) {
      wx.showToast({
        title: '标题不能超过50个字符',
        icon: 'none'
      })
      return false
    }
    
    if (!formData.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return false
    }
    
    if (formData.content.length > 500) {
      wx.showToast({
        title: '内容不能超过500个字符',
        icon: 'none'
      })
      return false
    }
    
    return true
  },

  /**
   * 提交反馈
   */
  async onSubmitFeedback() {
    if (!this.validateForm()) {
      return
    }

    if (this.data.submitting) {
      return
    }

    this.setData({ submitting: true })
    wx.showLoading({ title: '提交中...' })

    try {
      // 构建反馈数据
      const feedbackData = {
        type: this.data.feedbackType,
        title: this.data.formData.title.trim(),
        content: this.data.formData.content.trim(),
        contact: this.data.formData.contact.trim(),
        images: this.data.images,
        deviceInfo: this.deviceInfo,
        timestamp: Date.now(),
        appVersion: '1.0.0'
      }

      // 保存到本地存储（实际项目中应该发送到服务器）
      const feedbacks = wx.getStorageSync('feedbacks') || []
      feedbacks.unshift(feedbackData)
      
      // 只保留最近50条反馈
      if (feedbacks.length > 50) {
        feedbacks.splice(50)
      }
      
      wx.setStorageSync('feedbacks', feedbacks)

      wx.hideLoading()
      
      wx.showModal({
        title: '提交成功',
        content: '感谢您的反馈！我们会认真处理您的意见和建议。',
        showCancel: false,
        success: () => {
          // 重置表单
          this.setData({
            formData: {
              title: '',
              content: '',
              contact: ''
            },
            images: [],
            feedbackType: 'bug'
          })
        }
      })
    } catch (error) {
      wx.hideLoading()
      console.error('提交反馈失败:', error)
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 查看历史反馈
   */
  onViewHistory() {
    const feedbacks = wx.getStorageSync('feedbacks') || []
    
    if (feedbacks.length === 0) {
      wx.showToast({
        title: '暂无历史反馈',
        icon: 'none'
      })
      return
    }

    // 显示历史反馈列表
    const itemList = feedbacks.slice(0, 10).map((feedback, index) => {
      const typeMap = {
        bug: '问题反馈',
        feature: '功能建议', 
        improvement: '体验优化',
        other: '其他意见'
      }
      const date = new Date(feedback.timestamp).toLocaleDateString()
      return `${typeMap[feedback.type]} - ${feedback.title} (${date})`
    })

    wx.showActionSheet({
      itemList,
      success: (res) => {
        const selectedFeedback = feedbacks[res.tapIndex]
        this.showFeedbackDetail(selectedFeedback)
      }
    })
  },

  /**
   * 显示反馈详情
   */
  showFeedbackDetail(feedback) {
    const typeMap = {
      bug: '问题反馈',
      feature: '功能建议',
      improvement: '体验优化', 
      other: '其他意见'
    }
    
    const date = new Date(feedback.timestamp).toLocaleString()
    const content = `类型：${typeMap[feedback.type]}\n时间：${date}\n\n${feedback.content}`
    
    wx.showModal({
      title: feedback.title,
      content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '兼职工作管理 - 意见反馈',
      path: '/pages/feedback/feedback'
    }
  }
})
