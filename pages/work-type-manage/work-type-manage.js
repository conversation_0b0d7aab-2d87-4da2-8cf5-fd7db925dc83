// pages/work-type-manage/work-type-manage.js
// 工作类型管理页面

import { workTypeStorage, workRecordStorage } from '../../utils/storage.js'
import { WorkType } from '../../data/work-type.js'
import Validator from '../../utils/validator.js'
import SalaryCalculator from '../../utils/salary.js'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    workTypes: [],
    workTypeStats: {
      total: 0,
      active: 0,
      withRecords: 0
    },
    loading: false,
    
    // 弹窗状态
    showModal: false,
    showDeleteModal: false,
    isEdit: false,
    editId: null,
    deleteTarget: {},
    
    // 表单数据
    formData: {
      name: '',
      icon: '📋',
      color: '#007AFF',
      defaultHourlyRate: 0,
      description: ''
    },
    canSave: false,
    
    // 选项数据
    iconOptions: [
      '📋', '💼', '🏢', '🏪', '🍔', '☕',
      '📚', '🎓', '💻', '🎨', '📷', '🎵',
      '🚗', '🚚', '🏃', '💪', '🧹', '🔧',
      '👥', '📞', '💰', '📊', '🎯', '⭐'
    ],
    colorOptions: [
      '#007AFF', '#34C759', '#FF9500', '#FF3B30',
      '#AF52DE', '#FF2D92', '#5AC8FA', '#FFCC02',
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
      '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadData()
  },

  /**
   * 加载数据
   */
  async loadData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadWorkTypes(),
        this.loadWorkTypeStats()
      ])
    } catch (error) {
      console.error('加载数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载工作类型
   */
  async loadWorkTypes() {
    try {
      const [workTypesResult, recordsResult] = await Promise.all([
        workTypeStorage.getAll(),
        workRecordStorage.getAll()
      ])
      
      if (workTypesResult.success) {
        const workTypes = workTypesResult.data || []
        const records = recordsResult.success ? recordsResult.data : []
        
        // 计算每个工作类型的统计数据
        const processedWorkTypes = workTypes.map(workType => {
          const typeRecords = records.filter(record => record.workTypeId === workType.id)
          const stats = SalaryCalculator.calculateIncomeStats(typeRecords)
          
          return {
            ...workType,
            totalRecords: stats.recordCount,
            totalHours: stats.totalHours.toFixed(1),
            totalEarnings: stats.totalIncome.toFixed(0),
            canDelete: !workType.isDefault && stats.recordCount === 0
          }
        })
        
        // 按默认类型和创建时间排序
        processedWorkTypes.sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1
          if (!a.isDefault && b.isDefault) return 1
          return a.createdAt - b.createdAt
        })
        
        this.setData({ workTypes: processedWorkTypes })
      }
    } catch (error) {
      console.error('加载工作类型失败:', error)
    }
  },

  /**
   * 加载工作类型统计
   */
  async loadWorkTypeStats() {
    try {
      const result = await workTypeStorage.getAll()
      
      if (result.success) {
        const workTypes = result.data || []
        const total = workTypes.length
        const active = workTypes.filter(wt => wt.isActive).length
        const withRecords = workTypes.filter(wt => wt.totalRecords > 0).length
        
        this.setData({
          workTypeStats: { total, active, withRecords }
        })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  /**
   * 添加工作类型
   */
  onAddWorkType() {
    this.setData({
      showModal: true,
      isEdit: false,
      editId: null,
      formData: {
        name: '',
        icon: '📋',
        color: '#007AFF',
        defaultHourlyRate: 0,
        description: ''
      }
    })
    this.validateForm()
  },

  /**
   * 编辑工作类型
   */
  onWorkTypeEdit(e) {
    const { id } = e.currentTarget.dataset
    const workType = this.data.workTypes.find(wt => wt.id === id)
    
    if (workType) {
      this.setData({
        showModal: true,
        isEdit: true,
        editId: id,
        formData: {
          name: workType.name,
          icon: workType.icon,
          color: workType.color,
          defaultHourlyRate: workType.defaultHourlyRate,
          description: workType.description || ''
        }
      })
      this.validateForm()
    }
  },

  /**
   * 切换启用状态
   */
  async onToggleActive(e) {
    const { id, active } = e.currentTarget.dataset
    const isActive = active === 'true'
    
    try {
      const workType = this.data.workTypes.find(wt => wt.id === id)
      if (workType) {
        const updatedWorkType = new WorkType({
          ...workType,
          isActive: !isActive,
          updatedAt: Date.now()
        })
        
        const result = await workTypeStorage.save(updatedWorkType.toStorage())
        
        if (result.success) {
          wx.showToast({
            title: !isActive ? '已启用' : '已禁用',
            icon: 'success'
          })
          this.loadData()
        }
      }
    } catch (error) {
      console.error('切换状态失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  /**
   * 删除工作类型
   */
  onDeleteWorkType(e) {
    const { id } = e.currentTarget.dataset
    const workType = this.data.workTypes.find(wt => wt.id === id)
    
    if (workType) {
      this.setData({
        showDeleteModal: true,
        deleteTarget: workType
      })
    }
  },

  /**
   * 表单输入
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    const formData = { ...this.data.formData }
    
    if (field === 'defaultHourlyRate') {
      formData[field] = parseFloat(value) || 0
    } else {
      formData[field] = value
    }
    
    this.setData({ formData })
    this.validateForm()
  },

  /**
   * 图标选择
   */
  onIconSelect(e) {
    const { icon } = e.currentTarget.dataset
    const formData = { ...this.data.formData }
    formData.icon = icon
    
    this.setData({ formData })
  },

  /**
   * 颜色选择
   */
  onColorSelect(e) {
    const { color } = e.currentTarget.dataset
    const formData = { ...this.data.formData }
    formData.color = color
    
    this.setData({ formData })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data
    let canSave = true
    
    // 必填字段验证
    if (!formData.name || formData.name.trim().length === 0) canSave = false
    if (!formData.defaultHourlyRate || formData.defaultHourlyRate <= 0) canSave = false
    
    // 名称长度验证
    if (formData.name.length > 20) canSave = false
    
    this.setData({ canSave })
  },

  /**
   * 确认保存
   */
  async onConfirmSave() {
    if (!this.data.canSave) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      })
      return
    }

    try {
      const { formData, isEdit, editId } = this.data
      
      // 验证名称重复
      const existingWorkType = this.data.workTypes.find(wt => 
        wt.name === formData.name.trim() && (!isEdit || wt.id !== editId)
      )
      
      if (existingWorkType) {
        wx.showToast({
          title: '工作类型名称已存在',
          icon: 'none'
        })
        return
      }

      let workType
      if (isEdit) {
        const originalWorkType = this.data.workTypes.find(wt => wt.id === editId)
        workType = new WorkType({
          ...originalWorkType,
          ...formData,
          name: formData.name.trim(),
          updatedAt: Date.now()
        })
      } else {
        workType = new WorkType({
          ...formData,
          name: formData.name.trim(),
          isActive: true,
          isDefault: false
        })
      }

      const validation = workType.validate()
      if (!validation.isValid) {
        wx.showToast({
          title: validation.errors[0],
          icon: 'none'
        })
        return
      }

      const result = await workTypeStorage.save(workType.toStorage())
      
      if (result.success) {
        wx.showToast({
          title: isEdit ? '修改成功' : '添加成功',
          icon: 'success'
        })
        
        this.setData({ showModal: false })
        this.loadData()
      }
    } catch (error) {
      console.error('保存失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 确认删除
   */
  async onConfirmDelete() {
    try {
      const { deleteTarget } = this.data
      const result = await workTypeStorage.delete(deleteTarget.id)
      
      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        this.setData({ showDeleteModal: false })
        this.loadData()
      }
    } catch (error) {
      console.error('删除失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  /**
   * 关闭弹窗
   */
  onCloseModal() {
    this.setData({ showModal: false })
  },

  /**
   * 关闭删除弹窗
   */
  onCloseDeleteModal() {
    this.setData({ showDeleteModal: false })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '工作类型管理',
      path: '/pages/work-type-manage/work-type-manage'
    }
  }
})
