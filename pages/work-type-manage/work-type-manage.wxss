/* pages/work-type-manage/work-type-manage.wxss */
/* 工作类型管理页面样式 */

.work-type-manage-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 统计概览 */
.stats-overview {
  margin: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 工作类型列表 */
.work-type-list {
  margin: 0 32rpx;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  font-size: 28rpx;
}

.add-btn:active {
  background: #0056CC;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 类型卡片 */
.type-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.type-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.2s ease;
}

.type-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.type-card.disabled {
  opacity: 0.6;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.type-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007AFF;
}

.icon {
  color: #ffffff;
  font-size: 32rpx;
}

.type-details {
  flex: 1;
}

.type-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.type-rate {
  display: block;
  font-size: 24rpx;
  color: #34C759;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.2s ease;
}

.action-btn.toggle {
  background: #f8f9fa;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.action-btn.toggle:active {
  background: #007AFF;
  color: #ffffff;
}

.action-btn.delete {
  background: #fff5f5;
  color: #FF3B30;
  border: 2rpx solid #FF3B30;
}

.action-btn.delete:active {
  background: #FF3B30;
  color: #ffffff;
}

/* 卡片统计 */
.card-stats {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-top: 2rpx solid #f0f0f0;
}

.stat-row {
  text-align: center;
  flex: 1;
}

.stat-row .stat-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.stat-row .stat-value {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

/* 默认标识 */
.default-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #FF9500;
  color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.badge-text {
  font-weight: 500;
}

/* 禁用遮罩 */
.disabled-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.disabled-text {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.empty-action:active {
  background: #0056CC;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #adb5bd;
}

/* 弹窗样式 */
.work-type-modal,
.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.work-type-modal.show,
.delete-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  max-width: 700rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.form-label.required::after {
  content: '*';
  color: #dc3545;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.input-unit {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 22rpx;
  color: #adb5bd;
  margin-top: 8rpx;
}

/* 图标选择 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
}

.icon-item {
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.2s ease;
}

.icon-item.selected {
  border-color: #007AFF;
  background: #f0f9ff;
}

.icon-item:active {
  transform: scale(0.95);
}

/* 颜色选择 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
}

.color-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid transparent;
  transition: all 0.2s ease;
}

.color-item.selected {
  border-color: #333;
}

.color-item:active {
  transform: scale(0.95);
}

.check-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
}

/* 预览区域 */
.preview-section {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.preview-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.preview-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
}

.preview-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007AFF;
}

.preview-info {
  flex: 1;
}

.preview-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.preview-rate {
  display: block;
  font-size: 22rpx;
  color: #34C759;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #007AFF;
  color: #ffffff;
}

.modal-btn.danger {
  background: #FF3B30;
  color: #ffffff;
}

.modal-btn[disabled] {
  background: #e9ecef;
  color: #adb5bd;
  opacity: 0.6;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 删除警告 */
.delete-warning {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-align: center;
}

.warning-note {
  display: block;
  color: #FF3B30;
  font-weight: 500;
  margin-top: 16rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: left;
  }
  
  .icon-grid,
  .color-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .card-stats {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .work-type-manage-container {
    background: #1a1a1a;
  }
  
  .stats-overview,
  .type-card {
    background: #2a2a2a;
  }
  
  .list-title,
  .type-name {
    color: #ffffff;
  }
  
  .stat-row .stat-value {
    color: #ffffff;
  }
  
  .card-stats {
    border-top-color: #404040;
  }
  
  .modal-content {
    background: #2a2a2a;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #404040;
  }
  
  .form-input,
  .form-textarea {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .preview-section {
    background: #404040;
  }
  
  .preview-card {
    background: #555555;
  }
  
  .preview-name {
    color: #ffffff;
  }
  
  .icon-item {
    border-color: #555555;
  }
  
  .icon-item.selected {
    background: #1a3a5c;
  }
}
