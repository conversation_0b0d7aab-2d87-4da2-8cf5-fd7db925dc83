<!-- pages/work-type-manage/work-type-manage.wxml -->
<!-- 工作类型管理页面 -->

<view class="work-type-manage-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">工作类型管理</text>
    <text class="page-subtitle">管理你的兼职工作类型</text>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{workTypeStats.total}}</text>
        <text class="stat-label">总类型</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{workTypeStats.active}}</text>
        <text class="stat-label">启用中</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{workTypeStats.withRecords}}</text>
        <text class="stat-label">有记录</text>
      </view>
    </view>
  </view>

  <!-- 工作类型列表 -->
  <view class="work-type-list">
    <view class="list-header">
      <text class="list-title">工作类型列表</text>
      <button class="add-btn" bindtap="onAddWorkType">
        <text class="add-icon">+</text>
        <text class="add-text">添加</text>
      </button>
    </view>

    <view class="type-cards">
      <view 
        class="type-card {{!item.isActive ? 'disabled' : ''}}"
        wx:for="{{workTypes}}" 
        wx:key="id"
        bindtap="onWorkTypeEdit"
        data-id="{{item.id}}"
      >
        <!-- 工作类型头部 -->
        <view class="card-header">
          <view class="type-info">
            <view class="type-icon" style="background-color: {{item.color}}">
              <text class="icon">{{item.icon}}</text>
            </view>
            <view class="type-details">
              <text class="type-name">{{item.name}}</text>
              <text class="type-rate">¥{{item.defaultHourlyRate}}/小时</text>
            </view>
          </view>
          <view class="card-actions">
            <button 
              class="action-btn toggle"
              bindtap="onToggleActive"
              data-id="{{item.id}}"
              data-active="{{item.isActive}}"
              catchtap="stopPropagation"
            >
              {{item.isActive ? '禁用' : '启用'}}
            </button>
            <button 
              class="action-btn delete"
              wx:if="{{item.canDelete}}"
              bindtap="onDeleteWorkType"
              data-id="{{item.id}}"
              catchtap="stopPropagation"
            >
              删除
            </button>
          </view>
        </view>

        <!-- 工作类型统计 -->
        <view class="card-stats">
          <view class="stat-row">
            <text class="stat-label">工作记录</text>
            <text class="stat-value">{{item.totalRecords}}次</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">总工时</text>
            <text class="stat-value">{{item.totalHours}}小时</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">总收入</text>
            <text class="stat-value">¥{{item.totalEarnings}}</text>
          </view>
        </view>

        <!-- 默认标识 -->
        <view class="default-badge" wx:if="{{item.isDefault}}">
          <text class="badge-text">默认</text>
        </view>

        <!-- 禁用遮罩 -->
        <view class="disabled-mask" wx:if="{{!item.isActive}}">
          <text class="disabled-text">已禁用</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{workTypes.length === 0 && !loading}}">
    <view class="empty-icon">📋</view>
    <text class="empty-title">还没有工作类型</text>
    <text class="empty-desc">添加你的第一个工作类型开始管理</text>
    <button class="empty-action" bindtap="onAddWorkType">
      添加工作类型
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 添加/编辑工作类型弹窗 -->
<view class="work-type-modal {{showModal ? 'show' : ''}}" bindtap="onCloseModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{isEdit ? '编辑工作类型' : '添加工作类型'}}</text>
      <button class="modal-close" bindtap="onCloseModal">×</button>
    </view>
    
    <view class="modal-body">
      <!-- 工作类型名称 -->
      <view class="form-group">
        <label class="form-label required">类型名称</label>
        <input 
          class="form-input"
          type="text"
          placeholder="请输入工作类型名称"
          value="{{formData.name}}"
          bindinput="onFormInput"
          data-field="name"
          maxlength="20"
        />
      </view>

      <!-- 图标选择 -->
      <view class="form-group">
        <label class="form-label">选择图标</label>
        <view class="icon-grid">
          <view 
            class="icon-item {{formData.icon === item ? 'selected' : ''}}"
            wx:for="{{iconOptions}}" 
            wx:key="*this"
            bindtap="onIconSelect"
            data-icon="{{item}}"
          >
            <text class="icon-text">{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 颜色选择 -->
      <view class="form-group">
        <label class="form-label">选择颜色</label>
        <view class="color-grid">
          <view 
            class="color-item {{formData.color === item ? 'selected' : ''}}"
            wx:for="{{colorOptions}}" 
            wx:key="*this"
            style="background-color: {{item}}"
            bindtap="onColorSelect"
            data-color="{{item}}"
          >
            <text class="check-icon" wx:if="{{formData.color === item}}">✓</text>
          </view>
        </view>
      </view>

      <!-- 默认时薪 -->
      <view class="form-group">
        <label class="form-label required">默认时薪</label>
        <input 
          class="form-input"
          type="digit"
          placeholder="请输入默认时薪"
          value="{{formData.defaultHourlyRate}}"
          bindinput="onFormInput"
          data-field="defaultHourlyRate"
        />
        <text class="input-unit">元/小时</text>
      </view>

      <!-- 工作描述 -->
      <view class="form-group">
        <label class="form-label">工作描述</label>
        <textarea 
          class="form-textarea"
          placeholder="请输入工作类型描述（可选）"
          value="{{formData.description}}"
          bindinput="onFormInput"
          data-field="description"
          maxlength="100"
          auto-height
        />
        <text class="char-count">{{formData.description.length}}/100</text>
      </view>

      <!-- 预览 -->
      <view class="preview-section">
        <text class="preview-label">预览效果</text>
        <view class="preview-card">
          <view class="preview-icon" style="background-color: {{formData.color}}">
            <text class="icon">{{formData.icon}}</text>
          </view>
          <view class="preview-info">
            <text class="preview-name">{{formData.name || '工作类型名称'}}</text>
            <text class="preview-rate">¥{{formData.defaultHourlyRate || 0}}/小时</text>
          </view>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseModal">取消</button>
      <button 
        class="modal-btn confirm" 
        bindtap="onConfirmSave"
        disabled="{{!canSave}}"
      >
        {{isEdit ? '保存修改' : '添加类型'}}
      </button>
    </view>
  </view>
</view>

<!-- 删除确认弹窗 -->
<view class="delete-modal {{showDeleteModal ? 'show' : ''}}" bindtap="onCloseDeleteModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">确认删除</text>
    </view>
    <view class="modal-body">
      <text class="delete-warning">
        确定要删除工作类型"{{deleteTarget.name}}"吗？
        <text wx:if="{{deleteTarget.totalRecords > 0}}" class="warning-note">
          注意：该类型下有{{deleteTarget.totalRecords}}条工作记录，删除后这些记录将无法正常显示。
        </text>
      </text>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseDeleteModal">取消</button>
      <button class="modal-btn danger" bindtap="onConfirmDelete">删除</button>
    </view>
  </view>
</view>
