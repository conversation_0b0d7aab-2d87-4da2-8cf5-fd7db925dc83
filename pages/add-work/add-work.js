// pages/add-work/add-work.js
// 添加工作记录页面

import DateUtils from "../../utils/date.js";
import SalaryCalculator from "../../utils/salary.js";
import { workRecordStorage, workTypeStorage } from "../../utils/storage.js";
import { WorkRecord } from "../../data/work-record.js";
import { SALARY_TYPES } from "../../data/constants.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isEdit: false,
    isMultiDay: false,
    recordId: "",

    // 表单数据
    formData: {
      workTypeId: null,
      title: "",
      date: "",
      endDate: "",
      startTime: "09:00",
      endTime: "17:00",
      hourlyRate: 0,
      salaryType: SALARY_TYPES.HOURLY,
      location: "",
      description: "",
      notes: "",
    },

    // 选择状态
    selectedWorkTypeIds: [],
    selectedWorkType: null,
    salaryTypeIndex: 0,

    // 计算数据
    estimatedIncome: 0,
    dailyIncome: 0,
    workDays: 1,
    duration: 0,

    // UI状态
    canSubmit: false,
    showError: false,
    showSuccess: false,
    errorMessage: "",
    successMessage: "",

    // 配置数据
    salaryTypes: [
      {
        value: SALARY_TYPES.HOURLY,
        label: "时薪",
        rateLabel: "时薪（元/小时）",
      },
      { value: SALARY_TYPES.DAILY, label: "日薪", rateLabel: "日薪（元/天）" },
      {
        value: SALARY_TYPES.PROJECT,
        label: "项目薪资",
        rateLabel: "项目薪资（元）",
      },
      {
        value: SALARY_TYPES.PIECE,
        label: "计件薪资",
        rateLabel: "计件单价（元/件）",
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPage(options);
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    const { id, date, startTime, endTime, workTypeId, isMultiDay } = options;

    // 设置基本状态
    this.setData({
      isEdit: !!id,
      recordId: id || "",
      isMultiDay: isMultiDay === "true",
    });

    // 设置初始表单数据
    const formData = { ...this.data.formData };

    if (date) {
      formData.date = date;
    } else {
      formData.date = DateUtils.getToday();
    }

    if (startTime) formData.startTime = startTime;
    if (endTime) formData.endTime = endTime;
    if (workTypeId) {
      formData.workTypeId = parseInt(workTypeId);
      this.setData({ selectedWorkTypeIds: [parseInt(workTypeId)] });
    }

    this.setData({ formData });

    // 如果是编辑模式，加载记录数据
    if (this.data.isEdit) {
      this.loadRecord();
    } else {
      this.loadDefaultWorkType();
    }

    this.calculateIncome();
    this.validateForm();
  },

  /**
   * 加载记录数据（编辑模式）
   */
  loadRecord() {
    try {
      const result = workRecordStorage.getById(this.data.recordId);

      if (result.success) {
        const record = result.data;
        const formData = {
          workTypeId: record.workTypeId,
          title: record.title || "",
          date: record.date,
          endDate: record.endDate || "",
          startTime: record.startTime,
          endTime: record.endTime,
          hourlyRate: record.hourlyRate,
          salaryType: record.salaryType,
          location: record.location || "",
          description: record.description || "",
          notes: record.notes || "",
        };

        // 找到薪资类型索引
        const salaryTypeIndex = this.data.salaryTypes.findIndex(
          (st) => st.value === record.salaryType
        );

        this.setData({
          formData,
          selectedWorkTypeIds: [record.workTypeId],
          salaryTypeIndex: salaryTypeIndex >= 0 ? salaryTypeIndex : 0,
          isMultiDay: record.isMultiDay || false,
        });

        this.loadWorkType(record.workTypeId);
        this.calculateIncome();
        this.validateForm();
      }
    } catch (error) {
      console.error("加载记录失败:", error);
      this.showError("加载记录失败");
    }
  },

  /**
   * 加载默认工作类型
   */
  loadDefaultWorkType() {
    try {
      const result = workTypeStorage.getAll();

      if (result.success && result.data.length > 0) {
        const workTypes = result.data.filter((wt) => wt.isActive);

        if (workTypes.length > 0 && !this.data.formData.workTypeId) {
          const defaultWorkType = workTypes[0];
          const formData = { ...this.data.formData };
          formData.workTypeId = defaultWorkType.id;
          formData.hourlyRate = defaultWorkType.defaultHourlyRate;

          this.setData({
            formData,
            selectedWorkTypeIds: [defaultWorkType.id],
            selectedWorkType: defaultWorkType,
          });

          this.calculateIncome();
          this.validateForm();
        }
      }
    } catch (error) {
      console.error("加载默认工作类型失败:", error);
    }
  },

  /**
   * 加载工作类型信息
   */
  loadWorkType(workTypeId) {
    try {
      const result = workTypeStorage.getById(workTypeId);

      if (result.success) {
        this.setData({ selectedWorkType: result.data });
      }
    } catch (error) {
      console.error("加载工作类型失败:", error);
    }
  },

  /**
   * 模式切换
   */
  onModeSwitch(e) {
    const { mode } = e.currentTarget.dataset;
    const isMultiDay = mode === "multi";

    this.setData({ isMultiDay });
    this.calculateIncome();
    this.validateForm();
  },

  /**
   * 工作类型选择变化
   */
  onWorkTypeChange(e) {
    const { selectedWorkTypes } = e.detail;

    if (selectedWorkTypes.length > 0) {
      const workType = selectedWorkTypes[0];
      const formData = { ...this.data.formData };
      formData.workTypeId = workType.id;
      formData.hourlyRate = workType.defaultHourlyRate;

      this.setData({
        formData,
        selectedWorkType: workType,
      });

      this.calculateIncome();
      this.validateForm();
    }
  },

  /**
   * 输入变化事件
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const formData = { ...this.data.formData };

    if (field === "hourlyRate") {
      formData[field] = parseFloat(value) || 0;
    } else {
      formData[field] = value;
    }

    this.setData({ formData });

    if (field === "hourlyRate") {
      this.calculateIncome();
    }

    this.validateForm();
  },

  /**
   * 日期变化事件
   */
  onDateChange(e) {
    const formData = { ...this.data.formData };
    formData.date = e.detail.value;
    this.setData({ formData });

    this.calculateIncome();
    this.validateForm();
  },

  /**
   * 结束日期变化事件
   */
  onEndDateChange(e) {
    const formData = { ...this.data.formData };
    formData.endDate = e.detail.value;
    this.setData({ formData });

    this.calculateIncome();
    this.validateForm();
  },

  /**
   * 时间变化事件
   */
  onTimeChange(e) {
    const { startTime, endTime, duration } = e.detail;
    const formData = { ...this.data.formData };
    formData.startTime = startTime;
    formData.endTime = endTime;

    this.setData({
      formData,
      duration,
    });

    this.calculateIncome();
    this.validateForm();
  },

  /**
   * 薪资类型变化
   */
  onSalaryTypeChange(e) {
    const index = parseInt(e.detail.value);
    const salaryType = this.data.salaryTypes[index];
    const formData = { ...this.data.formData };
    formData.salaryType = salaryType.value;

    this.setData({
      formData,
      salaryTypeIndex: index,
    });

    this.calculateIncome();
    this.validateForm();
  },

  /**
   * 计算收入
   */
  calculateIncome() {
    const { formData, duration, isMultiDay } = this.data;
    const { hourlyRate, salaryType, date, endDate } = formData;

    if (!hourlyRate || hourlyRate <= 0) {
      this.setData({
        estimatedIncome: 0,
        dailyIncome: 0,
        workDays: 1,
      });
      return;
    }

    let dailyIncome = 0;
    let workDays = 1;
    let totalIncome = 0;

    // 计算单日收入
    dailyIncome = SalaryCalculator.calculateSalary(
      salaryType,
      hourlyRate,
      duration
    );

    // 计算工作天数
    if (isMultiDay && date && endDate) {
      workDays = DateUtils.getDaysDiff(date, endDate) + 1;
      if (workDays < 1) workDays = 1;
    }

    // 计算总收入
    totalIncome = dailyIncome * workDays;

    this.setData({
      estimatedIncome: Math.round(totalIncome * 100) / 100,
      dailyIncome: Math.round(dailyIncome * 100) / 100,
      workDays,
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData, isMultiDay } = this.data;
    let canSubmit = true;

    // 必填字段验证
    if (!formData.workTypeId) canSubmit = false;
    if (!formData.date) canSubmit = false;
    if (!formData.startTime) canSubmit = false;
    if (!formData.endTime) canSubmit = false;
    if (!formData.hourlyRate || formData.hourlyRate <= 0) canSubmit = false;

    // 多日模式验证
    if (isMultiDay && !formData.endDate) canSubmit = false;
    if (
      isMultiDay &&
      formData.date &&
      formData.endDate &&
      formData.date >= formData.endDate
    ) {
      canSubmit = false;
    }

    this.setData({ canSubmit });
  },

  /**
   * 表单提交
   */
  async onFormSubmit() {
    if (!this.data.canSubmit) {
      this.showError("请完善必填信息");
      return;
    }

    try {
      const workRecord = this.createWorkRecord();
      const validation = workRecord.validate();

      if (!validation.isValid) {
        this.showError(validation.errors[0]);
        return;
      }

      if (this.data.isEdit) {
        await this.updateRecord(workRecord);
      } else {
        await this.saveRecord(workRecord);
      }

      this.showSuccess(this.data.isEdit ? "修改成功" : "保存成功");

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error("保存失败:", error);
      this.showError("保存失败，请重试");
    }
  },

  /**
   * 创建工作记录对象
   */
  createWorkRecord() {
    const { formData, isMultiDay } = this.data;

    const recordData = {
      ...formData,
      isMultiDay,
      status: "planned",
    };

    if (this.data.isEdit) {
      recordData.id = this.data.recordId;
    }

    return new WorkRecord(recordData);
  },

  /**
   * 保存新记录
   */
  async saveRecord(workRecord) {
    const { isMultiDay } = this.data;

    if (isMultiDay) {
      // 多日工作记录
      workRecordStorage.save(workRecord.toStorage());
    } else {
      // 单日工作记录
      workRecordStorage.save(workRecord.toStorage());
    }
  },

  /**
   * 更新记录
   */
  async updateRecord(workRecord) {
    await workRecordStorage.save(workRecord.toStorage());
  },

  /**
   * 表单重置
   */
  onFormReset() {
    this.setData({
      formData: {
        workTypeId: null,
        title: "",
        date: DateUtils.getToday(),
        endDate: "",
        startTime: "09:00",
        endTime: "17:00",
        hourlyRate: 0,
        salaryType: SALARY_TYPES.HOURLY,
        location: "",
        description: "",
        notes: "",
      },
      selectedWorkTypeIds: [],
      selectedWorkType: null,
      salaryTypeIndex: 0,
      estimatedIncome: 0,
      dailyIncome: 0,
      workDays: 1,
    });

    this.validateForm();
  },

  /**
   * 取消操作
   */
  onCancel() {
    wx.showModal({
      title: "确认取消",
      content: "确定要取消吗？未保存的内容将丢失",
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      },
    });
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    this.setData({
      errorMessage: message,
      showError: true,
    });

    setTimeout(() => {
      this.setData({ showError: false });
    }, 3000);
  },

  /**
   * 显示成功信息
   */
  showSuccess(message) {
    this.setData({
      successMessage: message,
      showSuccess: true,
    });

    setTimeout(() => {
      this.setData({ showSuccess: false });
    }, 3000);
  },

  /**
   * 隐藏错误信息
   */
  onHideError() {
    this.setData({ showError: false });
  },

  /**
   * 隐藏成功信息
   */
  onHideSuccess() {
    this.setData({ showSuccess: false });
  },
});
