<!-- pages/add-work/add-work.wxml -->
<!-- 添加工作记录页面 -->

<view class="add-work-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">{{isEdit ? '编辑工作记录' : '添加工作记录'}}</text>
    <text class="page-subtitle">{{isMultiDay ? '多日工作记录' : '单日工作记录'}}</text>
  </view>

  <!-- 工作模式切换 -->
  <view class="mode-switch">
    <view class="switch-container">
      <button 
        class="switch-btn {{!isMultiDay ? 'active' : ''}}"
        bindtap="onModeSwitch"
        data-mode="single"
      >
        单日工作
      </button>
      <button 
        class="switch-btn {{isMultiDay ? 'active' : ''}}"
        bindtap="onModeSwitch"
        data-mode="multi"
      >
        多日工作
      </button>
    </view>
  </view>

  <!-- 表单内容 -->
  <form bindsubmit="onFormSubmit" bindreset="onFormReset">
    <view class="form-container">
      
      <!-- 工作类型选择 -->
      <view class="form-section">
        <work-type-selector
          title="选择工作类型"
          subtitle="请选择一个工作类型"
          multiple="{{false}}"
          default-selected="{{selectedWorkTypeIds}}"
          show-rate="{{true}}"
          show-stats="{{false}}"
          bind:selectionchange="onWorkTypeChange"
        />
      </view>

      <!-- 工作标题 -->
      <view class="form-section">
        <view class="form-group">
          <label class="form-label">工作标题</label>
          <input 
            class="form-input"
            type="text"
            placeholder="请输入工作标题（可选）"
            value="{{formData.title}}"
            bindinput="onInputChange"
            data-field="title"
            maxlength="50"
          />
        </view>
      </view>

      <!-- 工作日期 -->
      <view class="form-section">
        <view class="form-group">
          <label class="form-label required">工作日期</label>
          <picker 
            mode="date" 
            value="{{formData.date}}" 
            bindchange="onDateChange"
          >
            <view class="date-picker">
              <text class="date-text">{{formData.date || '请选择日期'}}</text>
              <text class="picker-icon">📅</text>
            </view>
          </picker>
        </view>

        <!-- 多日模式的结束日期 -->
        <view class="form-group" wx:if="{{isMultiDay}}">
          <label class="form-label required">结束日期</label>
          <picker 
            mode="date" 
            value="{{formData.endDate}}" 
            bindchange="onEndDateChange"
          >
            <view class="date-picker">
              <text class="date-text">{{formData.endDate || '请选择结束日期'}}</text>
              <text class="picker-icon">📅</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 工作时间 -->
      <view class="form-section">
        <time-picker
          title="工作时间"
          subtitle="设置每日的工作时间"
          default-start-time="{{formData.startTime}}"
          default-end-time="{{formData.endTime}}"
          hourly-rate="{{selectedWorkType.defaultHourlyRate || 0}}"
          show-duration="{{true}}"
          show-details="{{true}}"
          bind:timechange="onTimeChange"
        />
      </view>

      <!-- 薪资设置 -->
      <view class="form-section">
        <view class="section-title">薪资设置</view>
        
        <!-- 薪资类型 -->
        <view class="form-group">
          <label class="form-label">薪资类型</label>
          <picker 
            range="{{salaryTypes}}" 
            range-key="label"
            value="{{salaryTypeIndex}}"
            bindchange="onSalaryTypeChange"
          >
            <view class="picker-display">
              <text>{{salaryTypes[salaryTypeIndex].label}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>

        <!-- 薪资金额 -->
        <view class="form-group">
          <label class="form-label required">{{salaryTypes[salaryTypeIndex].rateLabel}}</label>
          <input 
            class="form-input"
            type="digit"
            placeholder="请输入金额"
            value="{{formData.hourlyRate}}"
            bindinput="onInputChange"
            data-field="hourlyRate"
          />
        </view>

        <!-- 预计收入显示 -->
        <view class="income-preview" wx:if="{{estimatedIncome > 0}}">
          <text class="preview-label">预计收入：</text>
          <text class="preview-value">¥{{estimatedIncome}}</text>
          <text class="preview-note" wx:if="{{isMultiDay && workDays > 1}}">
            （{{workDays}}天 × ¥{{dailyIncome}}/天）
          </text>
        </view>
      </view>

      <!-- 工作地点 -->
      <view class="form-section">
        <view class="form-group">
          <label class="form-label">工作地点</label>
          <input 
            class="form-input"
            type="text"
            placeholder="请输入工作地点（可选）"
            value="{{formData.location}}"
            bindinput="onInputChange"
            data-field="location"
            maxlength="100"
          />
        </view>
      </view>

      <!-- 工作描述 -->
      <view class="form-section">
        <view class="form-group">
          <label class="form-label">工作描述</label>
          <textarea 
            class="form-textarea"
            placeholder="请输入工作内容描述（可选）"
            value="{{formData.description}}"
            bindinput="onInputChange"
            data-field="description"
            maxlength="200"
            auto-height
          />
          <text class="char-count">{{formData.description.length}}/200</text>
        </view>
      </view>

      <!-- 备注 -->
      <view class="form-section">
        <view class="form-group">
          <label class="form-label">备注</label>
          <textarea 
            class="form-textarea"
            placeholder="其他备注信息（可选）"
            value="{{formData.notes}}"
            bindinput="onInputChange"
            data-field="notes"
            maxlength="200"
            auto-height
          />
          <text class="char-count">{{formData.notes.length}}/200</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="form-actions">
      <button class="action-btn secondary" bindtap="onCancel">
        取消
      </button>
      <button 
        class="action-btn primary" 
        form-type="submit"
        disabled="{{!canSubmit}}"
      >
        {{isEdit ? '保存修改' : '保存记录'}}
      </button>
    </view>
  </form>
</view>

<!-- 错误提示 -->
<view class="error-toast {{showError ? 'show' : ''}}" bindtap="onHideError">
  <text class="error-text">{{errorMessage}}</text>
</view>

<!-- 成功提示 -->
<view class="success-toast {{showSuccess ? 'show' : ''}}" bindtap="onHideSuccess">
  <text class="success-text">{{successMessage}}</text>
</view>
