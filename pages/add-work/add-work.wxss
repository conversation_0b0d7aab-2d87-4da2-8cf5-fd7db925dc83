/* pages/add-work/add-work.wxss */
/* 添加工作记录页面样式 */

.add-work-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 模式切换 */
.mode-switch {
  padding: 32rpx;
  background: #ffffff;
  border-bottom: 2rpx solid #f0f0f0;
}

.switch-container {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.switch-btn {
  flex: 1;
  padding: 16rpx 24rpx;
  background: transparent;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.2s ease;
}

.switch-btn.active {
  background: #007AFF;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* 表单容器 */
.form-container {
  padding: 0 32rpx;
}

/* 表单区块 */
.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.form-label.required::after {
  content: '*';
  color: #dc3545;
  margin-left: 4rpx;
}

/* 表单输入 */
.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.form-input[placeholder] {
  color: #adb5bd;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 22rpx;
  color: #adb5bd;
  margin-top: 8rpx;
}

/* 日期选择器 */
.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.date-picker:active {
  border-color: #007AFF;
  background: #ffffff;
}

.date-text {
  font-size: 32rpx;
  color: #333;
}

.picker-icon {
  font-size: 32rpx;
  opacity: 0.6;
}

/* 选择器显示 */
.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #adb5bd;
}

/* 收入预览 */
.income-preview {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 2rpx solid #d4edda;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;
  text-align: center;
}

.preview-label {
  font-size: 26rpx;
  color: #155724;
}

.preview-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #28a745;
  margin: 0 8rpx;
}

.preview-note {
  display: block;
  font-size: 22rpx;
  color: #6c757d;
  margin-top: 8rpx;
}

/* 底部操作按钮 */
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 2rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.action-btn.secondary:active {
  background: #e9ecef;
}

.action-btn.primary {
  background: #007AFF;
  color: #ffffff;
}

.action-btn.primary:active {
  background: #0056CC;
}

.action-btn[disabled] {
  background: #e9ecef;
  color: #adb5bd;
  opacity: 0.6;
}

/* 提示消息 */
.error-toast,
.success-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #dc3545;
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.success-toast {
  background: #28a745;
}

.error-toast.show,
.success-toast.show {
  opacity: 1;
  visibility: visible;
}

.error-text,
.success-text {
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .form-container {
    padding: 0 24rpx;
  }
  
  .form-section {
    padding: 24rpx;
    margin: 16rpx 0;
  }
  
  .form-actions {
    padding: 16rpx 24rpx;
  }
  
  .action-btn {
    padding: 20rpx;
    font-size: 28rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .add-work-container {
    background: #1a1a1a;
  }
  
  .mode-switch,
  .form-section,
  .form-actions {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .section-title,
  .form-label {
    color: #ffffff;
  }
  
  .form-input,
  .form-textarea,
  .date-picker,
  .picker-display {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .switch-container {
    background: #404040;
  }
  
  .switch-btn {
    color: #adb5bd;
  }
  
  .income-preview {
    background: linear-gradient(135deg, #1a3a1a 0%, #2a4a2a 100%);
    border-color: #28a745;
  }
  
  .preview-label {
    color: #90ee90;
  }
}
