/* pages/home/<USER>/
/* 首页样式 */

.home-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.home-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
}

.header-info {
  flex: 1;
}

.welcome-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.date-text {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 24rpx;
  color: #ffffff;
  font-size: 28rpx;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 今日概览 */
.today-overview {
  margin: 32rpx;
  margin-bottom: 24rpx;
}

.overview-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-date {
  font-size: 24rpx;
  color: #666;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 日历区域 */
.calendar-section {
  margin: 0 32rpx 32rpx;
}

/* 快速操作 */
.quick-actions {
  margin: 0 32rpx 32rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.quick-action-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.action-icon.today {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
}

.action-icon.template {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
}

.action-icon.batch {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
}

.action-icon.stats {
  background: linear-gradient(135deg, #AF52DE 0%, #BF5AF2 100%);
}

.action-text {
  font-size: 22rpx;
  color: #333;
  text-align: center;
}

/* 最近记录 */
.recent-records {
  margin: 0 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #007AFF;
}

.records-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  transition: background 0.2s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background: #f8f9fa;
}

.record-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.record-type-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007AFF;
}

.type-icon {
  color: #ffffff;
  font-size: 24rpx;
}

.record-info {
  flex: 1;
}

.record-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.record-time {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.record-right {
  text-align: right;
}

.record-income {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #34C759;
  margin-bottom: 4rpx;
}

.record-duration {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.empty-action:active {
  background: #0056CC;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #adb5bd;
}

/* 日期选择弹窗 */
.date-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.date-picker-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
}

.date-picker-display {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #007AFF;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: left;
  }
  
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .home-container {
    background: #1a1a1a;
  }
  
  .overview-card,
  .quick-action-item,
  .records-list {
    background: #2a2a2a;
  }
  
  .card-title,
  .section-title,
  .record-title,
  .action-text {
    color: #ffffff;
  }
  
  .card-date,
  .record-time,
  .record-duration {
    color: #adb5bd;
  }
  
  .record-item {
    border-bottom-color: #404040;
  }
  
  .modal-content {
    background: #2a2a2a;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #404040;
  }
  
  .date-picker-display {
    background: #404040;
    color: #ffffff;
  }
}
