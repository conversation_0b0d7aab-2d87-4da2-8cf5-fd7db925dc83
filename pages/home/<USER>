<!-- pages/home/<USER>
<!-- 首页日历视图 -->

<view class="home-container">
  <!-- 页面头部 -->
  <view class="home-header">
    <view class="header-info">
      <text class="welcome-text">你好，{{userInfo.nickName || '用户'}}</text>
      <text class="date-text">{{todayText}}</text>
    </view>
    <view class="header-actions">
      <button class="action-btn" bindtap="onAddWork">
        <text class="btn-icon">+</text>
        <text class="btn-text">记录</text>
      </button>
    </view>
  </view>

  <!-- 今日概览卡片 -->
  <view class="today-overview" wx:if="{{todayStats}}">
    <view class="overview-card">
      <view class="card-header">
        <text class="card-title">今日概览</text>
        <text class="card-date">{{todayStats.date}}</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{todayStats.workCount}}</text>
          <text class="stat-label">工作记录</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{todayStats.totalHours}}</text>
          <text class="stat-label">工作时长</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">¥{{todayStats.totalIncome}}</text>
          <text class="stat-label">今日收入</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 日历组件 -->
  <view class="calendar-section">
    <calendar 
      year="{{currentYear}}"
      month="{{currentMonth}}"
      show-legend="{{true}}"
      show-income="{{true}}"
      bind:datetap="onDateTap"
      bind:monthchange="onMonthChange"
    />
  </view>

  <!-- 快速操作区域 -->
  <view class="quick-actions">
    <view class="action-grid">
      <view class="quick-action-item" bindtap="onQuickAdd" data-type="today">
        <view class="action-icon today">📅</view>
        <text class="action-text">今日工作</text>
      </view>
      <view class="quick-action-item" bindtap="onQuickAdd" data-type="template">
        <view class="action-icon template">📋</view>
        <text class="action-text">使用模板</text>
      </view>
      <view class="quick-action-item" bindtap="onQuickAdd" data-type="batch">
        <view class="action-icon batch">📊</view>
        <text class="action-text">批量添加</text>
      </view>
      <view class="quick-action-item" bindtap="onViewStats">
        <view class="action-icon stats">📈</view>
        <text class="action-text">查看统计</text>
      </view>
    </view>
  </view>

  <!-- 最近工作记录 -->
  <view class="recent-records" wx:if="{{recentRecords.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近记录</text>
      <text class="section-more" bindtap="onViewAllRecords">查看全部</text>
    </view>
    <view class="records-list">
      <view 
        class="record-item" 
        wx:for="{{recentRecords}}" 
        wx:key="id"
        bindtap="onRecordTap"
        data-id="{{item.id}}"
      >
        <view class="record-left">
          <view 
            class="record-type-icon" 
            style="background-color: {{item.workTypeColor}}"
          >
            <text class="type-icon">{{item.workTypeIcon}}</text>
          </view>
          <view class="record-info">
            <text class="record-title">{{item.workTypeName}}</text>
            <text class="record-time">{{item.date}} {{item.startTime}}-{{item.endTime}}</text>
          </view>
        </view>
        <view class="record-right">
          <text class="record-income">¥{{item.totalSalary}}</text>
          <text class="record-duration">{{item.formattedDuration}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!hasData && !loading}}">
    <view class="empty-icon">📅</view>
    <text class="empty-title">还没有工作记录</text>
    <text class="empty-desc">点击上方按钮开始记录你的第一份工作</text>
    <button class="empty-action" bindtap="onAddWork">
      开始记录
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 日期选择弹窗 -->
<view class="date-picker-modal {{showDatePicker ? 'show' : ''}}" bindtap="onCloseDatePicker">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择日期</text>
      <button class="modal-close" bindtap="onCloseDatePicker">×</button>
    </view>
    <view class="modal-body">
      <picker 
        mode="date" 
        value="{{selectedDate}}" 
        bindchange="onDatePickerChange"
      >
        <view class="date-picker-display">
          <text>{{selectedDate || '请选择日期'}}</text>
        </view>
      </picker>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseDatePicker">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmDate">确定</button>
    </view>
  </view>
</view>
