// pages/home/<USER>
// 首页日历视图

import DateUtils from "../../utils/date.js";
import SalaryCalculator from "../../utils/salary.js";
import { workRecordStorage, workTypeStorage } from "../../utils/storage.js";
import CommonErrorHandler from "../../utils/common-error-handler.js";
import { DEFAULT_WORK_TYPES } from "../../data/constants.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    todayText: "",
    todayStats: null,
    recentRecords: [],
    hasData: false,
    loading: false,
    showDatePicker: false,
    selectedDate: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.initPage();
    this.initWorkTypes();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadData();
  },

  /**
   * 初始化页面
   */
  initPage() {
    const today = new Date();
    const todayText = DateUtils.formatDate(today);
    const weekday = DateUtils.getWeekday(todayText);

    this.setData({
      todayText: `${todayText} ${weekday}`,
      currentYear: today.getFullYear(),
      currentMonth: today.getMonth() + 1,
    });

    // 获取用户信息
    this.getUserInfo();
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    try {
      const userInfo = wx.getStorageSync("userInfo") || {};
      this.setData({ userInfo });
    } catch (error) {
      console.error("获取用户信息失败:", error);
    }
  },

  /**
   * 初始化工作类型
   */
  async initWorkTypes() {
    const result = await CommonErrorHandler.handleStorage(
      () => workTypeStorage.getAll(),
      "获取工作类型"
    );

    if (result.success && result.data.length === 0) {
      // 如果没有工作类型，初始化默认工作类型
      await CommonErrorHandler.handleStorage(
        () => workTypeStorage.saveBatch(DEFAULT_WORK_TYPES),
        "初始化默认工作类型"
      );
    }
  },

  /**
   * 加载数据
   */
  async loadData() {
    this.setData({ loading: true });

    await CommonErrorHandler.handleAsync(
      async () => {
        await Promise.all([this.loadTodayStats(), this.loadRecentRecords()]);
        this.checkHasData();
      },
      { context: "加载首页数据", showToast: true }
    );

    this.setData({ loading: false });
  },

  /**
   * 加载今日统计
   */
  async loadTodayStats() {
    const today = DateUtils.getToday();

    const result = await CommonErrorHandler.handleStorage(
      () => workRecordStorage.getByDate(today),
      "加载今日统计"
    );

    if (result.success) {
      const todayRecords = result.data || [];
      const stats = SalaryCalculator.calculateIncomeStats(todayRecords);

      this.setData({
        todayStats: {
          date: DateUtils.getWeekday(today),
          workCount: stats.recordCount,
          totalHours: stats.totalHours.toFixed(1) + "h",
          totalIncome: stats.totalIncome.toFixed(0),
        },
      });
    }
  },

  /**
   * 加载最近记录
   */
  async loadRecentRecords() {
    const result = await CommonErrorHandler.handleAsync(
      async () => {
        const [recordsResult, workTypesResult] = await Promise.all([
          workRecordStorage.getAll(),
          workTypeStorage.getAll(),
        ]);

        if (recordsResult.success && workTypesResult.success) {
          const allRecords = recordsResult.data || [];
          const workTypes = workTypesResult.data || [];
          const workTypeMap = new Map(workTypes.map((wt) => [wt.id, wt]));

          // 按创建时间排序，取最近5条
          const recentRecords = allRecords
            .sort((a, b) => b.createdAt - a.createdAt)
            .slice(0, 5)
            .map((record) => {
              const workType = workTypeMap.get(record.workTypeId);
              return {
                ...record,
                workTypeName: workType?.name || "未知类型",
                workTypeColor: workType?.color || "#cccccc",
                workTypeIcon: workType?.icon || "📋",
                formattedDuration: DateUtils.formatDuration(record.duration),
              };
            });

          return recentRecords;
        }
        return [];
      },
      { context: "加载最近记录", showToast: false }
    );

    if (result.success) {
      this.setData({ recentRecords: result.data });
    }
  },

  /**
   * 检查是否有数据
   */
  checkHasData() {
    const { todayStats, recentRecords } = this.data;
    const hasData =
      (todayStats && todayStats.workCount > 0) || recentRecords.length > 0;
    this.setData({ hasData });
  },

  /**
   * 日历日期点击事件
   */
  onDateTap(e) {
    const { date, hasWork, workRecords } = e.detail;

    if (hasWork && workRecords.length > 0) {
      // 如果有工作记录，显示当日记录
      this.showDayRecords(date, workRecords);
    } else {
      // 如果没有工作记录，快速添加
      this.quickAddWork(date);
    }
  },

  /**
   * 显示当日记录
   */
  showDayRecords(date, workRecords) {
    const recordIds = workRecords.map((record) => record.id).join(",");
    wx.navigateTo({
      url: `/pages/work-detail/work-detail?date=${date}&ids=${recordIds}`,
    });
  },

  /**
   * 快速添加工作
   */
  quickAddWork(date) {
    wx.navigateTo({
      url: `/pages/add-work/add-work?date=${date}`,
    });
  },

  /**
   * 月份变化事件
   */
  onMonthChange(e) {
    const { year, month } = e.detail;
    this.setData({
      currentYear: year,
      currentMonth: month,
    });
  },

  /**
   * 添加工作记录
   */
  onAddWork() {
    wx.navigateTo({
      url: "/pages/add-work/add-work",
    });
  },

  /**
   * 快速添加操作
   */
  onQuickAdd(e) {
    const { type } = e.currentTarget.dataset;

    switch (type) {
      case "today":
        this.quickAddWork(DateUtils.getToday());
        break;
      case "template":
        this.showTemplateSelector();
        break;
      case "batch":
        this.showBatchAdd();
        break;
    }
  },

  /**
   * 显示模板选择器
   */
  showTemplateSelector() {
    wx.showActionSheet({
      itemList: [
        "上午班(9:00-12:00)",
        "下午班(14:00-18:00)",
        "全天班(9:00-18:00)",
        "夜班(20:00-23:00)",
      ],
      success: (res) => {
        const templates = [
          { start: "09:00", end: "12:00" },
          { start: "14:00", end: "18:00" },
          { start: "09:00", end: "18:00" },
          { start: "20:00", end: "23:00" },
        ];

        const template = templates[res.tapIndex];
        const today = DateUtils.getToday();

        wx.navigateTo({
          url: `/pages/add-work/add-work?date=${today}&startTime=${template.start}&endTime=${template.end}`,
        });
      },
    });
  },

  /**
   * 显示批量添加
   */
  showBatchAdd() {
    this.setData({ showDatePicker: true });
  },

  /**
   * 查看统计
   */
  onViewStats() {
    wx.switchTab({
      url: "/pages/statistics/statistics",
    });
  },

  /**
   * 工作记录点击事件
   */
  onRecordTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/work-detail/work-detail?id=${id}`,
    });
  },

  /**
   * 查看全部记录
   */
  onViewAllRecords() {
    wx.navigateTo({
      url: "/pages/work-list/work-list",
    });
  },

  /**
   * 关闭日期选择器
   */
  onCloseDatePicker() {
    this.setData({ showDatePicker: false });
  },

  /**
   * 日期选择器变化
   */
  onDatePickerChange(e) {
    this.setData({ selectedDate: e.detail.value });
  },

  /**
   * 确认日期
   */
  onConfirmDate() {
    const { selectedDate } = this.data;
    if (selectedDate) {
      wx.navigateTo({
        url: `/pages/add-work/add-work?date=${selectedDate}&isMultiDay=true`,
      });
    }
    this.onCloseDatePicker();
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: "兼职工作管理 - 轻松记录工作时间",
      path: "/pages/home/<USER>",
    };
  },
});
