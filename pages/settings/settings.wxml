<!-- pages/settings/settings.wxml -->
<!-- 个人设置页面 -->

<view class="settings-container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-card">
      <view class="user-avatar" bindtap="onChangeAvatar">
        <image 
          class="avatar-image" 
          src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
          mode="aspectFill"
        />
        <view class="avatar-edit">
          <text class="edit-icon">📷</text>
        </view>
      </view>
      <view class="user-info">
        <text class="user-name">{{userInfo.nickName || '点击设置昵称'}}</text>
        <text class="user-desc">{{userInfo.signature || '还没有个性签名'}}</text>
      </view>
      <view class="user-actions">
        <button class="edit-btn" bindtap="onEditProfile">
          <text class="btn-icon">✏️</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="data-overview">
    <view class="overview-title">数据概览</view>
    <view class="overview-grid">
      <view class="overview-item">
        <text class="item-value">{{dataStats.totalRecords}}</text>
        <text class="item-label">工作记录</text>
      </view>
      <view class="overview-item">
        <text class="item-value">{{dataStats.totalIncome}}</text>
        <text class="item-label">总收入</text>
      </view>
      <view class="overview-item">
        <text class="item-value">{{dataStats.totalHours}}</text>
        <text class="item-label">工作时长</text>
      </view>
      <view class="overview-item">
        <text class="item-value">{{dataStats.workTypes}}</text>
        <text class="item-label">工作类型</text>
      </view>
    </view>
  </view>

  <!-- 设置选项 -->
  <view class="settings-sections">
    
    <!-- 应用设置 -->
    <view class="settings-section">
      <view class="section-title">应用设置</view>
      <view class="settings-list">
        
        <view class="setting-item" bindtap="onThemeSettings">
          <view class="item-left">
            <text class="item-icon">🎨</text>
            <text class="item-title">主题设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{themeText}}</text>
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item">
          <view class="item-left">
            <text class="item-icon">🔔</text>
            <text class="item-title">消息通知</text>
          </view>
          <view class="item-right">
            <switch 
              checked="{{settings.notifications}}"
              bindchange="onNotificationChange"
              color="#007AFF"
            />
          </view>
        </view>
        
        <view class="setting-item">
          <view class="item-left">
            <text class="item-icon">📊</text>
            <text class="item-title">统计提醒</text>
          </view>
          <view class="item-right">
            <switch 
              checked="{{settings.statsReminder}}"
              bindchange="onStatsReminderChange"
              color="#007AFF"
            />
          </view>
        </view>
        
        <view class="setting-item" bindtap="onCurrencySettings">
          <view class="item-left">
            <text class="item-icon">💰</text>
            <text class="item-title">货币设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{settings.currency}}</text>
            <text class="item-arrow">›</text>
          </view>
        </view>
        
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="settings-section">
      <view class="section-title">数据管理</view>
      <view class="settings-list">
        
        <view class="setting-item" bindtap="onBackupData">
          <view class="item-left">
            <text class="item-icon">☁️</text>
            <text class="item-title">数据备份</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{lastBackupTime}}</text>
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onRestoreData">
          <view class="item-left">
            <text class="item-icon">📥</text>
            <text class="item-title">数据恢复</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onExportData">
          <view class="item-left">
            <text class="item-icon">📤</text>
            <text class="item-title">导出数据</text>
          </view>
          <view class="item-right">
            <text class="item-value">Excel格式</text>
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onClearData">
          <view class="item-left">
            <text class="item-icon">🗑️</text>
            <text class="item-title">清空数据</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
      </view>
    </view>

    <!-- 工作设置 -->
    <view class="settings-section">
      <view class="section-title">工作设置</view>
      <view class="settings-list">
        
        <view class="setting-item" bindtap="onWorkTypeManage">
          <view class="item-left">
            <text class="item-icon">🏷️</text>
            <text class="item-title">工作类型管理</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{dataStats.workTypes}}个类型</text>
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onDefaultSettings">
          <view class="item-left">
            <text class="item-icon">⚙️</text>
            <text class="item-title">默认设置</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onReminderSettings">
          <view class="item-left">
            <text class="item-icon">⏰</text>
            <text class="item-title">提醒设置</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
      </view>
    </view>

    <!-- 帮助与反馈 -->
    <view class="settings-section">
      <view class="section-title">帮助与反馈</view>
      <view class="settings-list">
        
        <view class="setting-item" bindtap="onUserGuide">
          <view class="item-left">
            <text class="item-icon">📖</text>
            <text class="item-title">使用指南</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onFeedback">
          <view class="item-left">
            <text class="item-icon">💬</text>
            <text class="item-title">意见反馈</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onContactUs">
          <view class="item-left">
            <text class="item-icon">📞</text>
            <text class="item-title">联系我们</text>
          </view>
          <view class="item-right">
            <text class="item-arrow">›</text>
          </view>
        </view>
        
        <view class="setting-item" bindtap="onAbout">
          <view class="item-left">
            <text class="item-icon">ℹ️</text>
            <text class="item-title">关于应用</text>
          </view>
          <view class="item-right">
            <text class="item-value">v{{appVersion}}</text>
            <text class="item-arrow">›</text>
          </view>
        </view>
        
      </view>
    </view>

  </view>
</view>

<!-- 用户信息编辑弹窗 -->
<view class="profile-modal {{showProfileModal ? 'show' : ''}}" bindtap="onCloseProfileModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑个人信息</text>
      <button class="modal-close" bindtap="onCloseProfileModal">×</button>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <label class="form-label">昵称</label>
        <input 
          class="form-input"
          type="text"
          placeholder="请输入昵称"
          value="{{profileForm.nickName}}"
          bindinput="onProfileInput"
          data-field="nickName"
          maxlength="20"
        />
      </view>
      <view class="form-group">
        <label class="form-label">个性签名</label>
        <textarea 
          class="form-textarea"
          placeholder="请输入个性签名"
          value="{{profileForm.signature}}"
          bindinput="onProfileInput"
          data-field="signature"
          maxlength="50"
          auto-height
        />
        <text class="char-count">{{profileForm.signature.length}}/50</text>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseProfileModal">取消</button>
      <button class="modal-btn confirm" bindtap="onSaveProfile">保存</button>
    </view>
  </view>
</view>

<!-- 主题选择弹窗 -->
<view class="theme-modal {{showThemeModal ? 'show' : ''}}" bindtap="onCloseThemeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择主题</text>
      <button class="modal-close" bindtap="onCloseThemeModal">×</button>
    </view>
    <view class="modal-body">
      <view class="theme-options">
        <view 
          class="theme-option {{settings.theme === item.value ? 'selected' : ''}}"
          wx:for="{{themeOptions}}" 
          wx:key="value"
          bindtap="onThemeSelect"
          data-theme="{{item.value}}"
        >
          <view class="theme-preview" style="background: {{item.color}}">
            <text class="theme-icon">{{item.icon}}</text>
          </view>
          <text class="theme-name">{{item.name}}</text>
          <text class="check-icon" wx:if="{{settings.theme === item.value}}">✓</text>
        </view>
      </view>
    </view>
  </view>
</view>
