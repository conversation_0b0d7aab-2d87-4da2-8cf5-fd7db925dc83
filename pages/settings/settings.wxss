/* pages/settings/settings.wxss */
/* 个人设置页面样式 */

.settings-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 用户信息区域 */
.user-section {
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
}

.user-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
}

.user-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #ffffff;
}

.edit-icon {
  font-size: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.user-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-actions {
  display: flex;
  gap: 16rpx;
}

.edit-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.edit-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 数据概览 */
.data-overview {
  margin: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.overview-item {
  text-align: center;
}

.item-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.item-label {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 设置区域 */
.settings-sections {
  margin: 0 32rpx;
}

.settings-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

.settings-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  transition: background 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background: #f8f9fa;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.item-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.item-value {
  font-size: 26rpx;
  color: #666;
}

.item-arrow {
  font-size: 32rpx;
  color: #adb5bd;
  font-weight: bold;
}

/* 弹窗样式 */
.profile-modal,
.theme-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.profile-modal.show,
.theme-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 22rpx;
  color: #adb5bd;
  margin-top: 8rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #007AFF;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 主题选择 */
.theme-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.2s ease;
  position: relative;
}

.theme-option.selected {
  border-color: #007AFF;
  background: #f0f9ff;
}

.theme-option:active {
  transform: scale(0.95);
}

.theme-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.theme-icon {
  font-size: 32rpx;
  color: #ffffff;
}

.theme-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.check-icon {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  color: #007AFF;
  font-size: 20rpx;
  font-weight: bold;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
  
  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .user-info {
    text-align: center;
  }
  
  .theme-options {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .settings-container {
    background: #1a1a1a;
  }
  
  .data-overview,
  .settings-list {
    background: #2a2a2a;
  }
  
  .overview-title,
  .section-title,
  .item-title {
    color: #ffffff;
  }
  
  .setting-item {
    border-bottom-color: #404040;
  }
  
  .setting-item:active {
    background: #404040;
  }
  
  .item-icon {
    background: #1a3a5c;
  }
  
  .modal-content {
    background: #2a2a2a;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #404040;
  }
  
  .form-input,
  .form-textarea {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .theme-option {
    border-color: #555555;
  }
  
  .theme-option.selected {
    background: #1a3a5c;
  }
  
  .theme-name {
    color: #ffffff;
  }
}
