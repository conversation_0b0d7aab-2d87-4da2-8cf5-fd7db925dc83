// pages/settings/settings.js
// 个人设置页面

import { workRecordStorage, workTypeStorage } from "../../utils/storage.js";
import SalaryCalculator from "../../utils/salary.js";
import DateUtils from "../../utils/date.js";
import ThemeManager from "../../utils/theme-manager.js";
import CommonErrorHandler from "../../utils/common-error-handler.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    dataStats: {
      totalRecords: 0,
      totalIncome: "¥0",
      totalHours: "0h",
      workTypes: 0,
    },
    settings: {
      theme: "auto",
      notifications: true,
      statsReminder: true,
      currency: "CNY",
    },
    themeText: "跟随系统",
    lastBackupTime: "从未备份",
    appVersion: "1.0.0",

    // 弹窗状态
    showProfileModal: false,
    showThemeModal: false,

    // 表单数据
    profileForm: {
      nickName: "",
      signature: "",
    },

    // 主题选项
    themeOptions: [
      { value: "light", name: "浅色模式", icon: "☀️", color: "#007AFF" },
      { value: "dark", name: "深色模式", icon: "🌙", color: "#1a1a1a" },
      { value: "auto", name: "跟随系统", icon: "🔄", color: "#666666" },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo();
    this.loadDataStats();
    this.loadSettings();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadDataStats();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync("userInfo") || {};
      this.setData({ userInfo });
    } catch (error) {
      console.error("加载用户信息失败:", error);
    }
  },

  /**
   * 加载数据统计
   */
  async loadDataStats() {
    try {
      const [recordsResult, workTypesResult] = await Promise.all([
        workRecordStorage.getAll(),
        workTypeStorage.getAll(),
      ]);

      if (recordsResult.success && workTypesResult.success) {
        const records = recordsResult.data || [];
        const workTypes = workTypesResult.data || [];

        const stats = SalaryCalculator.calculateIncomeStats(records);

        this.setData({
          dataStats: {
            totalRecords: records.length,
            totalIncome: `¥${stats.totalIncome.toFixed(0)}`,
            totalHours: `${stats.totalHours.toFixed(1)}h`,
            workTypes: workTypes.length,
          },
        });
      }
    } catch (error) {
      console.error("加载数据统计失败:", error);
    }
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const settings = wx.getStorageSync("appSettings") || this.data.settings;
      const themeText = this.getThemeText(settings.theme);

      this.setData({
        settings,
        themeText,
      });
    } catch (error) {
      console.error("加载设置失败:", error);
    }
  },

  /**
   * 获取主题文本
   */
  getThemeText(theme) {
    const themeMap = {
      light: "浅色模式",
      dark: "深色模式",
      auto: "跟随系统",
    };
    return themeMap[theme] || "跟随系统";
  },

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      wx.setStorageSync("appSettings", this.data.settings);
    } catch (error) {
      console.error("保存设置失败:", error);
    }
  },

  /**
   * 更换头像
   */
  onChangeAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 这里可以上传到服务器或保存到本地
        const userInfo = { ...this.data.userInfo };
        userInfo.avatarUrl = tempFilePath;

        this.setData({ userInfo });

        try {
          wx.setStorageSync("userInfo", userInfo);
          wx.showToast({
            title: "头像更新成功",
            icon: "success",
          });
        } catch (error) {
          console.error("保存头像失败:", error);
        }
      },
    });
  },

  /**
   * 编辑个人信息
   */
  onEditProfile() {
    const { userInfo } = this.data;
    this.setData({
      showProfileModal: true,
      profileForm: {
        nickName: userInfo.nickName || "",
        signature: userInfo.signature || "",
      },
    });
  },

  /**
   * 个人信息输入
   */
  onProfileInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const profileForm = { ...this.data.profileForm };
    profileForm[field] = value;

    this.setData({ profileForm });
  },

  /**
   * 保存个人信息
   */
  onSaveProfile() {
    const { profileForm } = this.data;

    if (!profileForm.nickName.trim()) {
      wx.showToast({
        title: "请输入昵称",
        icon: "none",
      });
      return;
    }

    const userInfo = {
      ...this.data.userInfo,
      nickName: profileForm.nickName.trim(),
      signature: profileForm.signature.trim(),
    };

    try {
      wx.setStorageSync("userInfo", userInfo);
      this.setData({
        userInfo,
        showProfileModal: false,
      });

      wx.showToast({
        title: "保存成功",
        icon: "success",
      });
    } catch (error) {
      console.error("保存个人信息失败:", error);
      wx.showToast({
        title: "保存失败",
        icon: "none",
      });
    }
  },

  /**
   * 主题设置
   */
  onThemeSettings() {
    this.setData({ showThemeModal: true });
  },

  /**
   * 主题选择
   */
  onThemeSelect(e) {
    const { theme } = e.currentTarget.dataset;
    const settings = { ...this.data.settings };
    settings.theme = theme;

    const themeText = this.getThemeText(theme);

    this.setData({
      settings,
      themeText,
      showThemeModal: false,
    });

    this.saveSettings();

    wx.showToast({
      title: "主题已更新",
      icon: "success",
    });
  },

  /**
   * 通知设置变化
   */
  onNotificationChange(e) {
    const settings = { ...this.data.settings };
    settings.notifications = e.detail.value;

    this.setData({ settings });
    this.saveSettings();
  },

  /**
   * 统计提醒变化
   */
  onStatsReminderChange(e) {
    const settings = { ...this.data.settings };
    settings.statsReminder = e.detail.value;

    this.setData({ settings });
    this.saveSettings();
  },

  /**
   * 货币设置
   */
  onCurrencySettings() {
    wx.showActionSheet({
      itemList: ["人民币 (CNY)", "美元 (USD)", "欧元 (EUR)", "日元 (JPY)"],
      success: (res) => {
        const currencies = ["CNY", "USD", "EUR", "JPY"];
        const currency = currencies[res.tapIndex];

        const settings = { ...this.data.settings };
        settings.currency = currency;

        this.setData({ settings });
        this.saveSettings();

        wx.showToast({
          title: "货币设置已更新",
          icon: "success",
        });
      },
    });
  },

  /**
   * 数据备份
   */
  async onBackupData() {
    try {
      wx.showLoading({ title: "备份中..." });

      const [recordsResult, workTypesResult] = await Promise.all([
        workRecordStorage.getAll(),
        workTypeStorage.getAll(),
      ]);

      const backupData = {
        version: "1.0.0",
        timestamp: Date.now(),
        records: recordsResult.data || [],
        workTypes: workTypesResult.data || [],
        userInfo: this.data.userInfo,
        settings: this.data.settings,
      };

      // 保存到本地
      wx.setStorageSync("backupData", backupData);

      this.setData({
        lastBackupTime: DateUtils.formatDateTime(new Date()),
      });

      wx.hideLoading();
      wx.showToast({
        title: "备份成功",
        icon: "success",
      });
    } catch (error) {
      wx.hideLoading();
      console.error("备份失败:", error);
      wx.showToast({
        title: "备份失败",
        icon: "none",
      });
    }
  },

  /**
   * 数据恢复
   */
  onRestoreData() {
    wx.showModal({
      title: "确认恢复",
      content: "恢复数据将覆盖当前所有数据，确定要继续吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: "恢复中..." });

            const backupData = wx.getStorageSync("backupData");
            if (!backupData) {
              wx.hideLoading();
              wx.showToast({
                title: "没有备份数据",
                icon: "none",
              });
              return;
            }

            // 恢复数据
            if (backupData.records) {
              await workRecordStorage.saveBatch(backupData.records);
            }
            if (backupData.workTypes) {
              await workTypeStorage.saveBatch(backupData.workTypes);
            }
            if (backupData.userInfo) {
              wx.setStorageSync("userInfo", backupData.userInfo);
            }
            if (backupData.settings) {
              wx.setStorageSync("appSettings", backupData.settings);
            }

            wx.hideLoading();
            wx.showToast({
              title: "恢复成功",
              icon: "success",
            });

            // 刷新页面数据
            this.loadUserInfo();
            this.loadDataStats();
            this.loadSettings();
          } catch (error) {
            wx.hideLoading();
            console.error("恢复失败:", error);
            wx.showToast({
              title: "恢复失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 导出数据
   */
  async onExportData() {
    wx.navigateTo({
      url: "/pages/data-manage/data-manage?tab=export",
    });
  },

  /**
   * 清空数据
   */
  onClearData() {
    wx.showModal({
      title: "危险操作",
      content: "确定要清空所有数据吗？此操作不可恢复！",
      confirmColor: "#FF3B30",
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: "清理中..." });

            await Promise.all([
              workRecordStorage.clear(),
              workTypeStorage.clear(),
            ]);

            wx.clearStorageSync();

            wx.hideLoading();
            wx.showToast({
              title: "数据已清空",
              icon: "success",
            });

            // 重新加载数据
            this.loadUserInfo();
            this.loadDataStats();
            this.loadSettings();
          } catch (error) {
            wx.hideLoading();
            console.error("清空数据失败:", error);
            wx.showToast({
              title: "清空失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 工作类型管理
   */
  onWorkTypeManage() {
    wx.navigateTo({
      url: "/pages/work-type-manage/work-type-manage",
    });
  },

  /**
   * 默认设置
   */
  onDefaultSettings() {
    wx.showActionSheet({
      itemList: [
        "设置默认工作类型",
        "设置默认时薪",
        "设置默认工作时长",
        "重置所有默认值",
      ],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.setDefaultWorkType();
            break;
          case 1:
            this.setDefaultHourlyRate();
            break;
          case 2:
            this.setDefaultDuration();
            break;
          case 3:
            this.resetDefaultSettings();
            break;
        }
      },
    });
  },

  /**
   * 设置默认工作类型
   */
  setDefaultWorkType() {
    const result = workTypeStorage.getAll();
    if (result.success && result.data.length > 0) {
      const workTypes = result.data.filter((wt) => wt.isActive);
      const itemList = workTypes.map((wt) => wt.name);

      wx.showActionSheet({
        itemList,
        success: (res) => {
          const selectedWorkType = workTypes[res.tapIndex];
          const settings = wx.getStorageSync("defaultSettings") || {};
          settings.defaultWorkTypeId = selectedWorkType.id;
          wx.setStorageSync("defaultSettings", settings);

          wx.showToast({
            title: `已设置默认工作类型：${selectedWorkType.name}`,
            icon: "success",
          });
        },
      });
    } else {
      wx.showToast({
        title: "请先创建工作类型",
        icon: "none",
      });
    }
  },

  /**
   * 设置默认时薪
   */
  setDefaultHourlyRate() {
    wx.showModal({
      title: "设置默认时薪",
      content: "请输入默认时薪（元/小时）",
      editable: true,
      placeholderText: "例如：25",
      success: (res) => {
        if (res.confirm && res.content) {
          const hourlyRate = parseFloat(res.content);
          if (isNaN(hourlyRate) || hourlyRate <= 0) {
            wx.showToast({
              title: "请输入有效的时薪",
              icon: "none",
            });
            return;
          }

          const settings = wx.getStorageSync("defaultSettings") || {};
          settings.defaultHourlyRate = hourlyRate;
          wx.setStorageSync("defaultSettings", settings);

          wx.showToast({
            title: `已设置默认时薪：¥${hourlyRate}/小时`,
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 设置默认工作时长
   */
  setDefaultDuration() {
    wx.showActionSheet({
      itemList: ["2小时", "4小时", "6小时", "8小时", "自定义"],
      success: (res) => {
        const durations = [2, 4, 6, 8];

        if (res.tapIndex < 4) {
          const duration = durations[res.tapIndex];
          const settings = wx.getStorageSync("defaultSettings") || {};
          settings.defaultDuration = duration * 60; // 转换为分钟
          wx.setStorageSync("defaultSettings", settings);

          wx.showToast({
            title: `已设置默认工作时长：${duration}小时`,
            icon: "success",
          });
        } else {
          // 自定义时长
          wx.showModal({
            title: "自定义工作时长",
            content: "请输入默认工作时长（小时）",
            editable: true,
            placeholderText: "例如：3.5",
            success: (modalRes) => {
              if (modalRes.confirm && modalRes.content) {
                const hours = parseFloat(modalRes.content);
                if (isNaN(hours) || hours <= 0) {
                  wx.showToast({
                    title: "请输入有效的时长",
                    icon: "none",
                  });
                  return;
                }

                const settings = wx.getStorageSync("defaultSettings") || {};
                settings.defaultDuration = hours * 60; // 转换为分钟
                wx.setStorageSync("defaultSettings", settings);

                wx.showToast({
                  title: `已设置默认工作时长：${hours}小时`,
                  icon: "success",
                });
              }
            },
          });
        }
      },
    });
  },

  /**
   * 重置默认设置
   */
  resetDefaultSettings() {
    wx.showModal({
      title: "重置默认设置",
      content: "确定要重置所有默认设置吗？",
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync("defaultSettings");
          wx.showToast({
            title: "已重置默认设置",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 提醒设置
   */
  onReminderSettings() {
    wx.navigateTo({
      url: "/pages/reminder-settings/reminder-settings",
    });
  },

  /**
   * 使用指南
   */
  onUserGuide() {
    wx.navigateTo({
      url: "/pages/user-guide/user-guide",
    });
  },

  /**
   * 意见反馈
   */
  onFeedback() {
    wx.navigateTo({
      url: "/pages/feedback/feedback",
    });
  },

  /**
   * 联系我们
   */
  onContactUs() {
    wx.showToast({
      title: "功能开发中",
      icon: "none",
    });
  },

  /**
   * 关于应用
   */
  onAbout() {
    wx.showModal({
      title: "关于应用",
      content: `兼职工作管理 v${this.data.appVersion}\n\n一款简洁高效的兼职工作记录和收入管理工具。\n\n© 2024 兼职工作管理`,
      showCancel: false,
    });
  },

  /**
   * 关闭个人信息弹窗
   */
  onCloseProfileModal() {
    this.setData({ showProfileModal: false });
  },

  /**
   * 关闭主题弹窗
   */
  onCloseThemeModal() {
    this.setData({ showThemeModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: "兼职工作管理 - 高效记录工作收入",
      path: "/pages/home/<USER>",
    };
  },
});
