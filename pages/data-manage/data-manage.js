// pages/data-manage/data-manage.js
// 数据管理页面

import DataExporter from "../../utils/data-export.js";
import DataImporter from "../../utils/data-import.js";
import { workRecordStorage, workTypeStorage } from "../../utils/storage.js";
import SalaryCalculator from "../../utils/salary.js";
import DateUtils from "../../utils/date.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    dataStats: {
      totalRecords: 0,
      totalIncome: "¥0",
      workTypes: 0,
      dataSize: "0KB",
    },
    backupCount: 0,
    recentOperations: [],

    // 弹窗状态
    showExportModal: false,
    showImportModal: false,
    showProgressModal: false,

    // 导出选项
    exportType: "json",
    exportOptions: {
      timeRange: "all",
      startDate: "",
      endDate: "",
      includeWorkRecords: true,
      includeWorkTypes: true,
      includeUserInfo: true,
      includeSettings: true,
    },

    // 导入选项
    importOptions: {
      mode: "merge",
      validateData: true,
      createBackup: true,
    },

    // 进度信息
    progressInfo: {
      icon: "⏳",
      title: "处理中...",
      description: "请稍候",
      percentage: 0,
    },

    // 时间范围选项
    timeRangeOptions: [
      { value: "all", label: "全部时间" },
      { value: "year", label: "本年" },
      { value: "quarter", label: "本季度" },
      { value: "month", label: "本月" },
      { value: "week", label: "本周" },
      { value: "custom", label: "自定义" },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadDataStats();
    this.loadRecentOperations();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理定时器，防止内存泄漏
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadDataStats();
  },

  /**
   * 加载数据统计
   */
  async loadDataStats() {
    try {
      const [recordsResult, workTypesResult] = await Promise.all([
        workRecordStorage.getAll(),
        workTypeStorage.getAll(),
      ]);

      if (recordsResult.success && workTypesResult.success) {
        const records = recordsResult.data || [];
        const workTypes = workTypesResult.data || [];

        const stats = SalaryCalculator.calculateIncomeStats(records);

        // 计算数据大小（估算）
        const dataSize = this.calculateDataSize(records, workTypes);

        this.setData({
          dataStats: {
            totalRecords: records.length,
            totalIncome: `¥${stats.totalIncome.toFixed(0)}`,
            workTypes: workTypes.length,
            dataSize: this.formatDataSize(dataSize),
          },
        });
      }
    } catch (error) {
      console.error("加载数据统计失败:", error);
    }
  },

  /**
   * 计算数据大小
   */
  calculateDataSize(records, workTypes) {
    const recordsSize = JSON.stringify(records).length;
    const workTypesSize = JSON.stringify(workTypes).length;
    const userInfoSize = JSON.stringify(
      wx.getStorageSync("userInfo") || {}
    ).length;
    const settingsSize = JSON.stringify(
      wx.getStorageSync("appSettings") || {}
    ).length;

    return recordsSize + workTypesSize + userInfoSize + settingsSize;
  },

  /**
   * 格式化数据大小
   */
  formatDataSize(bytes) {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  },

  /**
   * 加载最近操作
   */
  loadRecentOperations() {
    try {
      const operations = wx.getStorageSync("recentOperations") || [];
      this.setData({
        recentOperations: operations.slice(0, 5),
        backupCount: operations.filter((op) => op.type === "backup").length,
      });
    } catch (error) {
      console.error("加载最近操作失败:", error);
    }
  },

  /**
   * 添加操作记录
   */
  addOperationRecord(operation) {
    try {
      const operations = wx.getStorageSync("recentOperations") || [];
      const newOperation = {
        id: Date.now(),
        ...operation,
        time: DateUtils.formatDateTime(new Date()),
      };

      operations.unshift(newOperation);

      // 只保留最近20条记录
      if (operations.length > 20) {
        operations.splice(20);
      }

      wx.setStorageSync("recentOperations", operations);
      this.loadRecentOperations();
    } catch (error) {
      console.error("添加操作记录失败:", error);
    }
  },

  /**
   * 创建备份
   */
  async onCreateBackup() {
    this.showProgress("☁️", "创建备份", "正在备份数据...");

    try {
      const result = await DataExporter.exportAllData();

      if (result.success) {
        // 保存备份到本地存储
        const backups = wx.getStorageSync("dataBackups") || [];
        const backup = {
          id: Date.now(),
          filename: result.filename,
          data: result.data,
          size: JSON.stringify(result.data).length,
          createdAt: Date.now(),
        };

        backups.unshift(backup);

        // 只保留最近10个备份
        if (backups.length > 10) {
          backups.splice(10);
        }

        wx.setStorageSync("dataBackups", backups);

        this.addOperationRecord({
          type: "backup",
          icon: "☁️",
          title: "数据备份",
          description: `备份文件: ${result.filename}`,
          status: "success",
          statusText: "成功",
        });

        this.hideProgress();
        wx.showToast({
          title: "备份成功",
          icon: "success",
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      this.hideProgress();
      console.error("创建备份失败:", error);
      wx.showToast({
        title: "备份失败",
        icon: "none",
      });

      this.addOperationRecord({
        type: "backup",
        icon: "☁️",
        title: "数据备份",
        description: error.message,
        status: "error",
        statusText: "失败",
      });
    }
  },

  /**
   * 查看备份历史
   */
  onViewBackups() {
    const backups = wx.getStorageSync("dataBackups") || [];

    if (backups.length === 0) {
      wx.showToast({
        title: "暂无备份记录",
        icon: "none",
      });
      return;
    }

    const itemList = backups.map(
      (backup) =>
        `${DateUtils.formatDateTime(
          new Date(backup.createdAt)
        )} (${this.formatDataSize(backup.size)})`
    );

    wx.showActionSheet({
      itemList,
      success: (res) => {
        const backup = backups[res.tapIndex];
        this.showBackupActions(backup);
      },
    });
  },

  /**
   * 显示备份操作
   */
  showBackupActions(backup) {
    wx.showActionSheet({
      itemList: ["恢复此备份", "删除此备份"],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.restoreFromBackup(backup);
        } else if (res.tapIndex === 1) {
          this.deleteBackup(backup);
        }
      },
    });
  },

  /**
   * 从备份恢复
   */
  async restoreFromBackup(backup) {
    wx.showModal({
      title: "确认恢复",
      content: "恢复备份将覆盖当前所有数据，确定要继续吗？",
      success: async (res) => {
        if (res.confirm) {
          this.showProgress("📥", "恢复数据", "正在恢复备份...");

          try {
            const result = await DataImporter.importFullData(backup.data, {
              overwrite: true,
              validateData: false,
            });

            if (result.success) {
              this.addOperationRecord({
                type: "restore",
                icon: "📥",
                title: "数据恢复",
                description: `从备份恢复: ${backup.filename}`,
                status: "success",
                statusText: "成功",
              });

              this.hideProgress();
              wx.showToast({
                title: "恢复成功",
                icon: "success",
              });

              this.loadDataStats();
            } else {
              throw new Error(result.error);
            }
          } catch (error) {
            this.hideProgress();
            console.error("恢复失败:", error);
            wx.showToast({
              title: "恢复失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 删除备份
   */
  deleteBackup(backup) {
    wx.showModal({
      title: "确认删除",
      content: "确定要删除这个备份吗？",
      success: (res) => {
        if (res.confirm) {
          try {
            const backups = wx.getStorageSync("dataBackups") || [];
            const filteredBackups = backups.filter((b) => b.id !== backup.id);
            wx.setStorageSync("dataBackups", filteredBackups);

            wx.showToast({
              title: "删除成功",
              icon: "success",
            });

            this.loadRecentOperations();
          } catch (error) {
            console.error("删除备份失败:", error);
            wx.showToast({
              title: "删除失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 导出JSON
   */
  onExportJSON() {
    this.setData({
      exportType: "json",
      showExportModal: true,
    });
  },

  /**
   * 导出CSV
   */
  onExportCSV() {
    this.setData({
      exportType: "csv",
      showExportModal: true,
    });
  },

  /**
   * 导出报告
   */
  onExportReport() {
    this.setData({
      exportType: "report",
      showExportModal: true,
    });
  },

  /**
   * 时间范围选择
   */
  onTimeRangeSelect(e) {
    const { value } = e.currentTarget.dataset;
    const exportOptions = { ...this.data.exportOptions };
    exportOptions.timeRange = value;

    this.setData({ exportOptions });
  },

  /**
   * 开始日期变化
   */
  onStartDateChange(e) {
    const exportOptions = { ...this.data.exportOptions };
    exportOptions.startDate = e.detail.value;

    this.setData({ exportOptions });
  },

  /**
   * 结束日期变化
   */
  onEndDateChange(e) {
    const exportOptions = { ...this.data.exportOptions };
    exportOptions.endDate = e.detail.value;

    this.setData({ exportOptions });
  },

  /**
   * 内容选项变化
   */
  onContentOptionChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const exportOptions = { ...this.data.exportOptions };
    exportOptions[field] = value;

    this.setData({ exportOptions });
  },

  /**
   * 确认导出
   */
  async onConfirmExport() {
    this.setData({ showExportModal: false });

    const { exportType, exportOptions } = this.data;

    this.showProgress("📤", "导出数据", "正在生成文件...");

    try {
      let result;

      switch (exportType) {
        case "json":
          result = await DataExporter.exportAllData();
          break;
        case "csv":
          result = await DataExporter.exportWorkRecordsCSV(
            exportOptions.startDate,
            exportOptions.endDate
          );
          break;
        case "report":
          result = await DataExporter.exportStatisticsReport(
            exportOptions.timeRange
          );
          break;
      }

      if (result.success) {
        // 在小程序中，我们只能显示数据，无法直接下载文件
        this.showExportResult(result);

        this.addOperationRecord({
          type: "export",
          icon: "📤",
          title: "数据导出",
          description: `导出${exportType.toUpperCase()}文件: ${
            result.filename
          }`,
          status: "success",
          statusText: "成功",
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("导出失败:", error);
      wx.showToast({
        title: "导出失败",
        icon: "none",
      });

      this.addOperationRecord({
        type: "export",
        icon: "📤",
        title: "数据导出",
        description: error.message,
        status: "error",
        statusText: "失败",
      });
    } finally {
      this.hideProgress();
    }
  },

  /**
   * 显示导出结果
   */
  showExportResult(result) {
    // 在小程序中，我们可以将数据复制到剪贴板
    wx.setClipboardData({
      data:
        typeof result.data === "string"
          ? result.data
          : JSON.stringify(result.data, null, 2),
      success: () => {
        wx.showModal({
          title: "导出成功",
          content: `文件名: ${result.filename}\n数据已复制到剪贴板，可以粘贴到其他应用中保存。`,
          showCancel: false,
        });
      },
    });
  },

  /**
   * 导入数据
   */
  onImportData() {
    this.setData({ showImportModal: true });
  },

  /**
   * 导入CSV
   */
  onImportCSV() {
    wx.showToast({
      title: "功能开发中",
      icon: "none",
    });
  },

  /**
   * 导入模式变化
   */
  onImportModeChange(e) {
    const { mode } = e.currentTarget.dataset;
    const importOptions = { ...this.data.importOptions };
    importOptions.mode = mode;

    this.setData({ importOptions });
  },

  /**
   * 导入选项变化
   */
  onImportOptionChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const importOptions = { ...this.data.importOptions };
    importOptions[field] = value;

    this.setData({ importOptions });
  },

  /**
   * 确认导入
   */
  onConfirmImport() {
    // 在小程序中，我们可以让用户粘贴数据
    wx.showModal({
      title: "导入数据",
      content: "请将要导入的JSON数据复制到剪贴板，然后点击确定。",
      success: (res) => {
        if (res.confirm) {
          this.importFromClipboard();
        }
      },
    });
  },

  /**
   * 从剪贴板导入
   */
  async importFromClipboard() {
    try {
      const clipboardData = await this.getClipboardData();

      if (!clipboardData) {
        wx.showToast({
          title: "剪贴板为空",
          icon: "none",
        });
        return;
      }

      this.setData({ showImportModal: false });
      this.showProgress("📥", "导入数据", "正在处理数据...");

      const dataPackage = JSON.parse(clipboardData);
      const { importOptions } = this.data;

      // 如果需要创建备份
      if (importOptions.createBackup) {
        await this.onCreateBackup();
      }

      const result = await DataImporter.importFullData(dataPackage, {
        overwrite: importOptions.mode === "overwrite",
        validateData: importOptions.validateData,
      });

      if (result.success) {
        this.addOperationRecord({
          type: "import",
          icon: "📥",
          title: "数据导入",
          description: `导入${result.summary.totalImported}条记录`,
          status: "success",
          statusText: "成功",
        });

        wx.showModal({
          title: "导入成功",
          content: `成功导入 ${result.summary.totalImported} 条记录\n跳过 ${result.summary.totalSkipped} 条记录\n错误 ${result.summary.totalErrors} 条记录`,
          showCancel: false,
        });

        this.loadDataStats();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("导入失败:", error);
      wx.showToast({
        title: "导入失败",
        icon: "none",
      });

      this.addOperationRecord({
        type: "import",
        icon: "📥",
        title: "数据导入",
        description: error.message,
        status: "error",
        statusText: "失败",
      });
    } finally {
      this.hideProgress();
    }
  },

  /**
   * 获取剪贴板数据
   */
  getClipboardData() {
    return new Promise((resolve) => {
      wx.getClipboardData({
        success: (res) => {
          resolve(res.data);
        },
        fail: () => {
          resolve(null);
        },
      });
    });
  },

  /**
   * 清理旧数据
   */
  onCleanOldData() {
    wx.showActionSheet({
      itemList: ["删除3个月前的记录", "删除6个月前的记录", "删除1年前的记录"],
      success: async (res) => {
        const months = [3, 6, 12][res.tapIndex];
        const cutoffDate = new Date();
        cutoffDate.setMonth(cutoffDate.getMonth() - months);
        const cutoffDateStr = DateUtils.formatDate(cutoffDate);

        wx.showModal({
          title: "确认清理",
          content: `确定要删除${cutoffDateStr}之前的所有工作记录吗？`,
          success: async (modalRes) => {
            if (modalRes.confirm) {
              await this.cleanDataBefore(cutoffDateStr);
            }
          },
        });
      },
    });
  },

  /**
   * 清理指定日期前的数据
   */
  async cleanDataBefore(cutoffDate) {
    try {
      this.showProgress("🧹", "清理数据", "正在删除旧记录...");

      const result = await workRecordStorage.getAll();
      if (result.success) {
        const allRecords = result.data || [];
        const recordsToKeep = allRecords.filter(
          (record) => record.date >= cutoffDate
        );
        const deletedCount = allRecords.length - recordsToKeep.length;

        // 清空并重新保存
        await workRecordStorage.clear();
        if (recordsToKeep.length > 0) {
          await workRecordStorage.saveBatch(recordsToKeep);
        }

        this.addOperationRecord({
          type: "clean",
          icon: "🧹",
          title: "数据清理",
          description: `删除了${deletedCount}条旧记录`,
          status: "success",
          statusText: "成功",
        });

        this.hideProgress();
        wx.showToast({
          title: `清理完成，删除${deletedCount}条记录`,
          icon: "success",
        });

        this.loadDataStats();
      }
    } catch (error) {
      this.hideProgress();
      console.error("清理数据失败:", error);
      wx.showToast({
        title: "清理失败",
        icon: "none",
      });
    }
  },

  /**
   * 清空所有数据
   */
  onClearAllData() {
    wx.showModal({
      title: "危险操作",
      content: "确定要清空所有数据吗？此操作不可恢复！建议先创建备份。",
      confirmColor: "#FF3B30",
      success: async (res) => {
        if (res.confirm) {
          try {
            this.showProgress("🗑️", "清空数据", "正在删除所有数据...");

            await Promise.all([
              workRecordStorage.clear(),
              workTypeStorage.clear(),
            ]);

            wx.clearStorageSync();

            this.hideProgress();
            wx.showToast({
              title: "数据已清空",
              icon: "success",
            });

            this.loadDataStats();
            this.loadRecentOperations();
          } catch (error) {
            this.hideProgress();
            console.error("清空数据失败:", error);
            wx.showToast({
              title: "清空失败",
              icon: "none",
            });
          }
        }
      },
    });
  },

  /**
   * 显示进度
   */
  showProgress(icon, title, description) {
    this.setData({
      showProgressModal: true,
      progressInfo: {
        icon,
        title,
        description,
        percentage: 0,
      },
    });

    // 模拟进度
    this.simulateProgress();
  },

  /**
   * 模拟进度
   */
  simulateProgress() {
    let percentage = 0;
    const interval = setInterval(() => {
      percentage += Math.random() * 20;
      if (percentage >= 100) {
        percentage = 100;
        clearInterval(interval);
      }

      this.setData({
        "progressInfo.percentage": Math.floor(percentage),
      });
    }, 200);

    this.progressInterval = interval;
  },

  /**
   * 隐藏进度
   */
  hideProgress() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    this.setData({ showProgressModal: false });
  },

  /**
   * 关闭导出弹窗
   */
  onCloseExportModal() {
    this.setData({ showExportModal: false });
  },

  /**
   * 关闭导入弹窗
   */
  onCloseImportModal() {
    this.setData({ showImportModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: "数据管理 - 兼职工作管理",
      path: "/pages/data-manage/data-manage",
    };
  },
});
