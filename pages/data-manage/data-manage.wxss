/* pages/data-manage/data-manage.wxss */
/* 数据管理页面样式 */

.data-manage-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 数据概览 */
.data-overview {
  margin: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.stat-card {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 管理区域 */
.manage-section {
  margin: 0 32rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.action-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.action-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.action-card.danger {
  border: 2rpx solid #FF3B30;
}

.action-card.danger .card-title {
  color: #FF3B30;
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}

.card-icon.backup {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
}

.card-icon.history {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
}

.card-icon.export {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
}

.card-icon.csv {
  background: linear-gradient(135deg, #AF52DE 0%, #BF5AF2 100%);
}

.card-icon.report {
  background: linear-gradient(135deg, #FF2D92 0%, #FF6B6B 100%);
}

.card-icon.import {
  background: linear-gradient(135deg, #32D74B 0%, #34C759 100%);
}

.card-icon.csv-import {
  background: linear-gradient(135deg, #5AC8FA 0%, #007AFF 100%);
}

.card-icon.clean {
  background: linear-gradient(135deg, #FFCC02 0%, #FF9500 100%);
}

.card-icon.clear {
  background: linear-gradient(135deg, #FF3B30 0%, #FF2D92 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.card-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.card-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-text {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

.action-arrow {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
}

/* 最近操作 */
.operations-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.operation-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.operation-item:last-child {
  border-bottom: none;
}

.operation-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.operation-content {
  flex: 1;
}

.operation-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.operation-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.operation-time {
  display: block;
  font-size: 20rpx;
  color: #adb5bd;
}

.operation-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.operation-status.success {
  background: #d4edda;
  color: #155724;
}

.operation-status.error {
  background: #f8d7da;
  color: #721c24;
}

.operation-status.processing {
  background: #d1ecf1;
  color: #0c5460;
}

/* 弹窗样式 */
.export-modal,
.import-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.export-modal.show,
.import-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  max-width: 700rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: none;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.option-group {
  margin-bottom: 32rpx;
}

.option-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.time-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.time-option {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s ease;
}

.time-option.active {
  background: #007AFF;
  border-color: #007AFF;
  color: #ffffff;
}

.custom-time {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  flex: 1;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.date-separator {
  font-size: 24rpx;
  color: #666;
}

.content-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.import-modes {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.mode-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #007AFF;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 进度弹窗 */
.progress-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.progress-modal.show {
  opacity: 1;
  visibility: visible;
}

.progress-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 48rpx;
  text-align: center;
  min-width: 400rpx;
}

.progress-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.progress-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.progress-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 32rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #007AFF;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
  
  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .card-action {
    justify-content: center;
  }
  
  .time-options {
    flex-direction: column;
  }
  
  .custom-time {
    flex-direction: column;
    gap: 12rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .data-manage-container {
    background: #1a1a1a;
  }
  
  .data-overview,
  .action-card,
  .operations-list {
    background: #2a2a2a;
  }
  
  .overview-title,
  .section-title,
  .card-title,
  .operation-title {
    color: #ffffff;
  }
  
  .stat-card {
    background: #404040;
  }
  
  .operation-item {
    border-bottom-color: #404040;
  }
  
  .operation-icon {
    background: #1a3a5c;
  }
  
  .modal-content {
    background: #2a2a2a;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #404040;
  }
  
  .time-option,
  .date-picker,
  .checkbox-item,
  .radio-item {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .time-option.active {
    background: #007AFF;
    border-color: #007AFF;
  }
  
  .progress-content {
    background: #2a2a2a;
  }
  
  .progress-title {
    color: #ffffff;
  }
  
  .progress-bar {
    background: #404040;
  }
}
