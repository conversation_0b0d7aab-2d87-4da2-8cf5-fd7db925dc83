<!-- pages/data-manage/data-manage.wxml -->
<!-- 数据管理页面 -->

<view class="data-manage-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">数据管理</text>
    <text class="page-subtitle">备份、导出、导入你的工作数据</text>
  </view>

  <!-- 数据概览 -->
  <view class="data-overview">
    <view class="overview-title">数据概览</view>
    <view class="overview-stats">
      <view class="stat-card">
        <text class="stat-value">{{dataStats.totalRecords}}</text>
        <text class="stat-label">工作记录</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{dataStats.totalIncome}}</text>
        <text class="stat-label">总收入</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{dataStats.workTypes}}</text>
        <text class="stat-label">工作类型</text>
      </view>
      <view class="stat-card">
        <text class="stat-value">{{dataStats.dataSize}}</text>
        <text class="stat-label">数据大小</text>
      </view>
    </view>
  </view>

  <!-- 数据备份 -->
  <view class="manage-section">
    <view class="section-title">数据备份</view>
    <view class="action-cards">
      
      <view class="action-card" bindtap="onCreateBackup">
        <view class="card-icon backup">☁️</view>
        <view class="card-content">
          <text class="card-title">创建备份</text>
          <text class="card-desc">备份所有工作数据到本地</text>
        </view>
        <view class="card-action">
          <text class="action-text">立即备份</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
      <view class="action-card" bindtap="onViewBackups">
        <view class="card-icon history">📋</view>
        <view class="card-content">
          <text class="card-title">备份历史</text>
          <text class="card-desc">查看和管理历史备份</text>
        </view>
        <view class="card-action">
          <text class="action-text">{{backupCount}}个备份</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 数据导出 -->
  <view class="manage-section">
    <view class="section-title">数据导出</view>
    <view class="action-cards">
      
      <view class="action-card" bindtap="onExportJSON">
        <view class="card-icon export">📤</view>
        <view class="card-content">
          <text class="card-title">导出完整数据</text>
          <text class="card-desc">JSON格式，包含所有数据</text>
        </view>
        <view class="card-action">
          <text class="action-text">导出JSON</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
      <view class="action-card" bindtap="onExportCSV">
        <view class="card-icon csv">📊</view>
        <view class="card-content">
          <text class="card-title">导出工作记录</text>
          <text class="card-desc">CSV格式，便于Excel打开</text>
        </view>
        <view class="card-action">
          <text class="action-text">导出CSV</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
      <view class="action-card" bindtap="onExportReport">
        <view class="card-icon report">📈</view>
        <view class="card-content">
          <text class="card-title">导出统计报告</text>
          <text class="card-desc">详细的数据分析报告</text>
        </view>
        <view class="card-action">
          <text class="action-text">导出报告</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 数据导入 -->
  <view class="manage-section">
    <view class="section-title">数据导入</view>
    <view class="action-cards">
      
      <view class="action-card" bindtap="onImportData">
        <view class="card-icon import">📥</view>
        <view class="card-content">
          <text class="card-title">导入数据包</text>
          <text class="card-desc">从JSON文件恢复数据</text>
        </view>
        <view class="card-action">
          <text class="action-text">选择文件</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
      <view class="action-card" bindtap="onImportCSV">
        <view class="card-icon csv-import">📋</view>
        <view class="card-content">
          <text class="card-title">导入CSV文件</text>
          <text class="card-desc">从Excel或CSV导入记录</text>
        </view>
        <view class="card-action">
          <text class="action-text">选择CSV</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 数据清理 -->
  <view class="manage-section">
    <view class="section-title">数据清理</view>
    <view class="action-cards">
      
      <view class="action-card" bindtap="onCleanOldData">
        <view class="card-icon clean">🧹</view>
        <view class="card-content">
          <text class="card-title">清理旧数据</text>
          <text class="card-desc">删除指定时间前的记录</text>
        </view>
        <view class="card-action">
          <text class="action-text">开始清理</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
      <view class="action-card danger" bindtap="onClearAllData">
        <view class="card-icon clear">🗑️</view>
        <view class="card-content">
          <text class="card-title">清空所有数据</text>
          <text class="card-desc">删除所有工作记录和设置</text>
        </view>
        <view class="card-action">
          <text class="action-text">危险操作</text>
          <text class="action-arrow">›</text>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 最近操作 -->
  <view class="manage-section" wx:if="{{recentOperations.length > 0}}">
    <view class="section-title">最近操作</view>
    <view class="operations-list">
      <view 
        class="operation-item"
        wx:for="{{recentOperations}}" 
        wx:key="id"
      >
        <view class="operation-icon">{{item.icon}}</view>
        <view class="operation-content">
          <text class="operation-title">{{item.title}}</text>
          <text class="operation-desc">{{item.description}}</text>
          <text class="operation-time">{{item.time}}</text>
        </view>
        <view class="operation-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 导出选项弹窗 -->
<view class="export-modal {{showExportModal ? 'show' : ''}}" bindtap="onCloseExportModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">导出选项</text>
      <button class="modal-close" bindtap="onCloseExportModal">×</button>
    </view>
    <view class="modal-body">
      
      <!-- 时间范围选择 -->
      <view class="option-group">
        <text class="option-label">时间范围</text>
        <view class="time-options">
          <button 
            class="time-option {{exportOptions.timeRange === item.value ? 'active' : ''}}"
            wx:for="{{timeRangeOptions}}" 
            wx:key="value"
            bindtap="onTimeRangeSelect"
            data-value="{{item.value}}"
          >
            {{item.label}}
          </button>
        </view>
      </view>
      
      <!-- 自定义时间 -->
      <view class="option-group" wx:if="{{exportOptions.timeRange === 'custom'}}">
        <text class="option-label">自定义时间</text>
        <view class="custom-time">
          <picker 
            mode="date" 
            value="{{exportOptions.startDate}}" 
            bindchange="onStartDateChange"
          >
            <view class="date-picker">
              <text>{{exportOptions.startDate || '开始日期'}}</text>
            </view>
          </picker>
          <text class="date-separator">至</text>
          <picker 
            mode="date" 
            value="{{exportOptions.endDate}}" 
            bindchange="onEndDateChange"
          >
            <view class="date-picker">
              <text>{{exportOptions.endDate || '结束日期'}}</text>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 导出内容选择 -->
      <view class="option-group" wx:if="{{exportType === 'json'}}">
        <text class="option-label">导出内容</text>
        <view class="content-options">
          <label class="checkbox-item">
            <checkbox 
              checked="{{exportOptions.includeWorkRecords}}"
              bindchange="onContentOptionChange"
              data-field="includeWorkRecords"
            />
            <text>工作记录</text>
          </label>
          <label class="checkbox-item">
            <checkbox 
              checked="{{exportOptions.includeWorkTypes}}"
              bindchange="onContentOptionChange"
              data-field="includeWorkTypes"
            />
            <text>工作类型</text>
          </label>
          <label class="checkbox-item">
            <checkbox 
              checked="{{exportOptions.includeUserInfo}}"
              bindchange="onContentOptionChange"
              data-field="includeUserInfo"
            />
            <text>用户信息</text>
          </label>
          <label class="checkbox-item">
            <checkbox 
              checked="{{exportOptions.includeSettings}}"
              bindchange="onContentOptionChange"
              data-field="includeSettings"
            />
            <text>应用设置</text>
          </label>
        </view>
      </view>
      
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseExportModal">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmExport">开始导出</button>
    </view>
  </view>
</view>

<!-- 导入选项弹窗 -->
<view class="import-modal {{showImportModal ? 'show' : ''}}" bindtap="onCloseImportModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">导入选项</text>
      <button class="modal-close" bindtap="onCloseImportModal">×</button>
    </view>
    <view class="modal-body">
      
      <!-- 导入模式 -->
      <view class="option-group">
        <text class="option-label">导入模式</text>
        <view class="import-modes">
          <label class="radio-item">
            <radio 
              checked="{{importOptions.mode === 'merge'}}"
              bindchange="onImportModeChange"
              data-mode="merge"
            />
            <text>合并模式</text>
            <text class="mode-desc">保留现有数据，添加新数据</text>
          </label>
          <label class="radio-item">
            <radio 
              checked="{{importOptions.mode === 'overwrite'}}"
              bindchange="onImportModeChange"
              data-mode="overwrite"
            />
            <text>覆盖模式</text>
            <text class="mode-desc">覆盖重复的数据</text>
          </label>
        </view>
      </view>
      
      <!-- 数据验证 -->
      <view class="option-group">
        <label class="checkbox-item">
          <checkbox 
            checked="{{importOptions.validateData}}"
            bindchange="onImportOptionChange"
            data-field="validateData"
          />
          <text>启用数据验证</text>
        </label>
        <label class="checkbox-item">
          <checkbox 
            checked="{{importOptions.createBackup}}"
            bindchange="onImportOptionChange"
            data-field="createBackup"
          />
          <text>导入前创建备份</text>
        </label>
      </view>
      
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCloseImportModal">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmImport">开始导入</button>
    </view>
  </view>
</view>

<!-- 进度弹窗 -->
<view class="progress-modal {{showProgressModal ? 'show' : ''}}" catchtap="stopPropagation">
  <view class="progress-content">
    <view class="progress-icon">{{progressInfo.icon}}</view>
    <text class="progress-title">{{progressInfo.title}}</text>
    <text class="progress-desc">{{progressInfo.description}}</text>
    <view class="progress-bar">
      <view 
        class="progress-fill" 
        style="width: {{progressInfo.percentage}}%"
      ></view>
    </view>
    <text class="progress-text">{{progressInfo.percentage}}%</text>
  </view>
</view>
