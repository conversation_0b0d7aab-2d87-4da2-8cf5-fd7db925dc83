/* pages/user-guide/user-guide.wxss */
/* 使用指南页面样式 */

.guide-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 指南分类 */
.guide-categories {
  background: #ffffff;
  padding: 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
  display: flex;
  gap: 24rpx;
  overflow-x: auto;
}

.category-item {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  min-width: 160rpx;
  position: relative;
  transition: all 0.3s ease;
}

.category-item.selected {
  background: rgba(0, 122, 255, 0.1);
}

.category-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.category-title {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.category-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #007AFF;
  border-radius: 3rpx;
}

/* 指南内容 */
.guide-content {
  padding: 32rpx;
}

.content-header {
  margin-bottom: 32rpx;
}

.content-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.content-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 步骤列表 */
.steps-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.step-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.step-item:last-child {
  border-bottom: none;
}

.step-item:active {
  background: #f8f9fa;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: #007AFF;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.step-arrow {
  font-size: 32rpx;
  color: #cccccc;
  margin-left: 16rpx;
}

/* 快速导航 */
.quick-nav {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.nav-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.nav-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.nav-btn {
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:active {
  background: #e5e5e5;
}

/* 步骤详情弹窗 */
.step-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.step-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.step-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  background: #f0f0f0;
  border: none;
  border-radius: 50%;
  font-size: 32rpx;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.step-image {
  margin-bottom: 24rpx;
}

.guide-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.step-detail {
  line-height: 1.6;
}

.detail-content {
  font-size: 28rpx;
  color: #333333;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #e5e5e5;
  background: #f8f9fa;
}

.step-btn {
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  background: #007AFF;
  color: #ffffff;
}

.step-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.step-indicator {
  font-size: 24rpx;
  color: #666666;
}
