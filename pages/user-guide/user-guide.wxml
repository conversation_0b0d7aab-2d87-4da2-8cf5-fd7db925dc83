<!-- pages/user-guide/user-guide.wxml -->
<!-- 使用指南页面 -->

<view class="guide-container">
  <!-- 指南分类 -->
  <view class="guide-categories">
    <view 
      class="category-item {{selectedGuide && selectedGuide.id === item.id ? 'selected' : ''}}"
      wx:for="{{guides}}" 
      wx:key="id"
      bindtap="onSelectGuide"
      data-index="{{index}}"
    >
      <view class="category-icon" style="color: {{item.color}}">{{item.icon}}</view>
      <text class="category-title">{{item.title}}</text>
      <view class="category-indicator" wx:if="{{selectedGuide && selectedGuide.id === item.id}}"></view>
    </view>
  </view>

  <!-- 指南内容 -->
  <view class="guide-content" wx:if="{{selectedGuide}}">
    <view class="content-header">
      <text class="content-title">{{selectedGuide.title}}</text>
      <text class="content-desc">共{{selectedGuide.steps.length}}个步骤</text>
    </view>

    <!-- 步骤列表 -->
    <view class="steps-list">
      <view 
        class="step-item"
        wx:for="{{selectedGuide.steps}}" 
        wx:key="index"
        bindtap="onViewStep"
        data-index="{{index}}"
      >
        <view class="step-number">{{index + 1}}</view>
        <view class="step-content">
          <text class="step-title">{{item.title}}</text>
          <text class="step-desc">{{item.content}}</text>
        </view>
        <view class="step-arrow">›</view>
      </view>
    </view>

    <!-- 快速导航 -->
    <view class="quick-nav">
      <view class="nav-title">快速导航</view>
      <view class="nav-buttons">
        <button 
          class="nav-btn"
          bindtap="onNavigateToPage"
          data-page="add-work"
        >
          📝 添加工作
        </button>
        <button 
          class="nav-btn"
          bindtap="onNavigateToPage"
          data-page="statistics"
        >
          📊 查看统计
        </button>
        <button 
          class="nav-btn"
          bindtap="onNavigateToPage"
          data-page="settings"
        >
          ⚙️ 应用设置
        </button>
        <button 
          class="nav-btn"
          bindtap="onContactSupport"
        >
          💬 联系客服
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 步骤详情弹窗 -->
<view class="step-modal {{showStepModal ? 'show' : ''}}" bindtap="onCloseStepModal">
  <view class="modal-content" catchtap="stopPropagation" wx:if="{{selectedGuide && selectedGuide.steps[currentStep]}}">
    <view class="modal-header">
      <text class="modal-title">{{selectedGuide.steps[currentStep].title}}</text>
      <button class="modal-close" bindtap="onCloseStepModal">×</button>
    </view>
    
    <view class="modal-body">
      <!-- 步骤图片 -->
      <view class="step-image" wx:if="{{selectedGuide.steps[currentStep].image}}">
        <image 
          src="{{selectedGuide.steps[currentStep].image}}" 
          mode="aspectFit"
          class="guide-image"
        />
      </view>
      
      <!-- 步骤内容 -->
      <view class="step-detail">
        <text class="detail-content">{{selectedGuide.steps[currentStep].content}}</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button 
        class="step-btn prev-btn {{currentStep === 0 ? 'disabled' : ''}}"
        bindtap="onPrevStep"
        disabled="{{currentStep === 0}}"
      >
        ‹ 上一步
      </button>
      
      <view class="step-indicator">
        {{currentStep + 1}} / {{selectedGuide.steps.length}}
      </view>
      
      <button 
        class="step-btn next-btn {{currentStep === selectedGuide.steps.length - 1 ? 'disabled' : ''}}"
        bindtap="onNextStep"
        disabled="{{currentStep === selectedGuide.steps.length - 1}}"
      >
        下一步 ›
      </button>
    </view>
  </view>
</view>
