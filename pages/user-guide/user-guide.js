// pages/user-guide/user-guide.js
// 使用指南页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentStep: 0,
    guides: [
      {
        id: 'basic',
        title: '基础使用',
        icon: '📱',
        color: '#007AFF',
        steps: [
          {
            title: '添加工作记录',
            content: '点击首页的"+"按钮，选择工作类型，填写工作时间和时薪，即可快速记录一次工作。',
            image: '/images/guide/add-work.png'
          },
          {
            title: '查看统计数据',
            content: '在统计页面可以查看收入趋势、工作时长分析等详细数据，帮助你了解工作情况。',
            image: '/images/guide/statistics.png'
          },
          {
            title: '管理工作类型',
            content: '在设置页面可以添加、编辑工作类型，设置不同的图标和颜色进行区分。',
            image: '/images/guide/work-types.png'
          }
        ]
      },
      {
        id: 'advanced',
        title: '高级功能',
        icon: '⚡',
        color: '#FF9500',
        steps: [
          {
            title: '数据备份与恢复',
            content: '定期备份你的工作数据，避免数据丢失。支持一键备份和恢复功能。',
            image: '/images/guide/backup.png'
          },
          {
            title: '数据导出',
            content: '可以将工作记录导出为Excel格式，方便进行进一步的数据分析。',
            image: '/images/guide/export.png'
          },
          {
            title: '提醒设置',
            content: '设置工作提醒，让你不会错过任何一次工作机会。支持多种提醒方式。',
            image: '/images/guide/reminder.png'
          }
        ]
      },
      {
        id: 'tips',
        title: '使用技巧',
        icon: '💡',
        color: '#34C759',
        steps: [
          {
            title: '快速记录技巧',
            content: '使用默认设置功能，预设常用的工作类型和时薪，让记录更加快速。',
            image: '/images/guide/quick-record.png'
          },
          {
            title: '数据分析技巧',
            content: '定期查看统计数据，了解自己的工作模式，优化时间安排和收入结构。',
            image: '/images/guide/analysis.png'
          },
          {
            title: '主题个性化',
            content: '根据个人喜好选择浅色或深色主题，也可以设置跟随系统自动切换。',
            image: '/images/guide/theme.png'
          }
        ]
      }
    ],
    selectedGuide: null,
    showStepModal: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 设置默认选中第一个指南
    this.setData({
      selectedGuide: this.data.guides[0]
    })
  },

  /**
   * 选择指南分类
   */
  onSelectGuide(e) {
    const { index } = e.currentTarget.dataset
    const selectedGuide = this.data.guides[index]
    
    this.setData({
      selectedGuide,
      currentStep: 0
    })
  },

  /**
   * 查看步骤详情
   */
  onViewStep(e) {
    const { index } = e.currentTarget.dataset
    
    this.setData({
      currentStep: index,
      showStepModal: true
    })
  },

  /**
   * 上一步
   */
  onPrevStep() {
    const { currentStep, selectedGuide } = this.data
    if (currentStep > 0) {
      this.setData({
        currentStep: currentStep - 1
      })
    }
  },

  /**
   * 下一步
   */
  onNextStep() {
    const { currentStep, selectedGuide } = this.data
    if (currentStep < selectedGuide.steps.length - 1) {
      this.setData({
        currentStep: currentStep + 1
      })
    }
  },

  /**
   * 关闭步骤弹窗
   */
  onCloseStepModal() {
    this.setData({
      showStepModal: false
    })
  },

  /**
   * 跳转到相关页面
   */
  onNavigateToPage(e) {
    const { page } = e.currentTarget.dataset
    
    const pageMap = {
      'add-work': '/pages/add-work/add-work',
      'statistics': '/pages/statistics/statistics',
      'work-types': '/pages/work-type-manage/work-type-manage',
      'settings': '/pages/settings/settings',
      'reminder': '/pages/reminder-settings/reminder-settings',
      'data-manage': '/pages/data-manage/data-manage'
    }
    
    const url = pageMap[page]
    if (url) {
      wx.navigateTo({
        url,
        fail: () => {
          wx.switchTab({ url })
        }
      })
    }
  },

  /**
   * 联系客服
   */
  onContactSupport() {
    wx.showModal({
      title: '联系客服',
      content: '如果您在使用过程中遇到问题，可以通过以下方式联系我们：\n\n1. 在设置页面点击"意见反馈"\n2. 发送邮件至：<EMAIL>\n3. 微信群：搜索"兼职工作管理"',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '兼职工作管理使用指南',
      path: '/pages/user-guide/user-guide'
    }
  }
})
