// pages/statistics/statistics.js
// 统计分析页面

import DateUtils from '../../utils/date.js'
import SalaryCalculator from '../../utils/salary.js'
import { workRecordStorage, workTypeStorage } from '../../utils/storage.js'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    isEmpty: false,
    
    // 时间筛选
    activeTab: 'month',
    customStartDate: '',
    customEndDate: '',
    timeTabs: [
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' },
      { value: 'custom', label: '自定义' }
    ],
    
    // 核心统计
    coreStats: {
      totalIncome: 0,
      totalHours: 0,
      totalDays: 0,
      averageHourlyRate: 0
    },
    incomeTrend: null,
    
    // 图表数据
    incomeChartData: [],
    workTypeStats: [],
    monthlyData: [],
    
    // 效率分析
    bestTimeSlot: { time: '--', rate: 0 },
    bestWorkDay: { day: '--', income: 0 },
    workIntensity: { level: '--', description: '--' },
    
    // 详细统计
    detailedStats: [],
    
    // 目标设置
    showGoals: false,
    goals: [],
    
    // 数据洞察
    insights: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadStatistics()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadStatistics()
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    this.setData({ loading: true })
    
    try {
      const [recordsResult, workTypesResult] = await Promise.all([
        workRecordStorage.getAll(),
        workTypeStorage.getAll()
      ])
      
      if (recordsResult.success && workTypesResult.success) {
        const allRecords = recordsResult.data || []
        const workTypes = workTypesResult.data || []
        
        if (allRecords.length === 0) {
          this.setData({ isEmpty: true, loading: false })
          return
        }
        
        // 根据时间筛选过滤记录
        const filteredRecords = this.filterRecordsByTime(allRecords)
        
        // 计算各种统计数据
        await this.calculateStatistics(filteredRecords, workTypes)
        
        this.setData({ isEmpty: false })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 根据时间筛选记录
   */
  filterRecordsByTime(records) {
    const { activeTab, customStartDate, customEndDate } = this.data
    const now = new Date()
    let startDate, endDate
    
    switch (activeTab) {
      case 'week':
        startDate = DateUtils.getWeekStart(now)
        endDate = DateUtils.getWeekEnd(now)
        break
      case 'month':
        startDate = DateUtils.getMonthStart(now)
        endDate = DateUtils.getMonthEnd(now)
        break
      case 'quarter':
        startDate = DateUtils.getQuarterStart(now)
        endDate = DateUtils.getQuarterEnd(now)
        break
      case 'year':
        startDate = DateUtils.getYearStart(now)
        endDate = DateUtils.getYearEnd(now)
        break
      case 'custom':
        if (customStartDate && customEndDate) {
          startDate = customStartDate
          endDate = customEndDate
        } else {
          return records
        }
        break
      default:
        return records
    }
    
    return records.filter(record => {
      return record.date >= startDate && record.date <= endDate
    })
  },

  /**
   * 计算统计数据
   */
  async calculateStatistics(records, workTypes) {
    // 核心统计
    const coreStats = SalaryCalculator.calculateIncomeStats(records)
    const workDays = new Set(records.map(r => r.date)).size
    
    this.setData({
      coreStats: {
        totalIncome: coreStats.totalIncome.toFixed(0),
        totalHours: coreStats.totalHours.toFixed(1),
        totalDays: workDays,
        averageHourlyRate: coreStats.averageHourlyRate.toFixed(0)
      }
    })
    
    // 收入趋势
    this.calculateIncomeTrend(records)
    
    // 图表数据
    this.generateChartData(records, workTypes)
    
    // 效率分析
    this.analyzeEfficiency(records)
    
    // 详细统计
    this.generateDetailedStats(records, workTypes)
    
    // 数据洞察
    this.generateInsights(records, workTypes)
  },

  /**
   * 计算收入趋势
   */
  calculateIncomeTrend(records) {
    // 简化的趋势计算，实际应该对比上一期间
    const currentIncome = records.reduce((sum, r) => sum + r.totalSalary, 0)
    const trend = {
      change: 15.5, // 示例数据
      period: '较上月'
    }
    
    this.setData({ incomeTrend: trend })
  },

  /**
   * 生成图表数据
   */
  generateChartData(records, workTypes) {
    // 收入趋势图表数据
    const incomeByDate = this.groupRecordsByDate(records)
    const incomeChartData = Object.entries(incomeByDate).map(([date, income]) => ({
      label: DateUtils.formatDate(new Date(date), 'MM/DD'),
      value: income,
      color: '#007AFF'
    }))
    
    // 工作类型统计
    const workTypeMap = new Map(workTypes.map(wt => [wt.id, wt]))
    const typeStats = this.groupRecordsByType(records, workTypeMap)
    const workTypeStats = Object.entries(typeStats).map(([typeId, data]) => ({
      id: typeId,
      name: data.name,
      value: `¥${data.income.toFixed(0)}`,
      unit: '收入',
      icon: data.icon,
      color: data.color,
      description: `${data.hours.toFixed(1)}小时`
    }))
    
    this.setData({
      incomeChartData,
      workTypeStats
    })
  },

  /**
   * 按日期分组记录
   */
  groupRecordsByDate(records) {
    return records.reduce((groups, record) => {
      const date = record.date
      groups[date] = (groups[date] || 0) + record.totalSalary
      return groups
    }, {})
  },

  /**
   * 按工作类型分组记录
   */
  groupRecordsByType(records, workTypeMap) {
    return records.reduce((groups, record) => {
      const typeId = record.workTypeId
      const workType = workTypeMap.get(typeId)
      
      if (!groups[typeId]) {
        groups[typeId] = {
          name: workType?.name || '未知类型',
          icon: workType?.icon || '📋',
          color: workType?.color || '#cccccc',
          income: 0,
          hours: 0,
          count: 0
        }
      }
      
      groups[typeId].income += record.totalSalary
      groups[typeId].hours += DateUtils.minutesToHours(record.duration)
      groups[typeId].count += 1
      
      return groups
    }, {})
  },

  /**
   * 分析工作效率
   */
  analyzeEfficiency(records) {
    if (records.length === 0) return
    
    // 最高效时段分析
    const timeSlotStats = this.analyzeTimeSlots(records)
    const bestTimeSlot = timeSlotStats.reduce((best, slot) => 
      slot.rate > best.rate ? slot : best
    )
    
    // 最佳工作日分析
    const dayStats = this.analyzeDays(records)
    const bestWorkDay = dayStats.reduce((best, day) => 
      day.income > best.income ? day : best
    )
    
    // 工作强度分析
    const avgHoursPerDay = records.length > 0 ? 
      records.reduce((sum, r) => sum + DateUtils.minutesToHours(r.duration), 0) / 
      new Set(records.map(r => r.date)).size : 0
    
    let intensity
    if (avgHoursPerDay >= 8) {
      intensity = { level: '高强度', description: '工作时间较长' }
    } else if (avgHoursPerDay >= 4) {
      intensity = { level: '中等强度', description: '工作时间适中' }
    } else {
      intensity = { level: '低强度', description: '工作时间较短' }
    }
    
    this.setData({
      bestTimeSlot: {
        time: bestTimeSlot.time || '--',
        rate: bestTimeSlot.rate || 0
      },
      bestWorkDay: {
        day: bestWorkDay.day || '--',
        income: bestWorkDay.income || 0
      },
      workIntensity: intensity
    })
  },

  /**
   * 分析时段效率
   */
  analyzeTimeSlots(records) {
    const slots = {}
    
    records.forEach(record => {
      const hour = parseInt(record.startTime.split(':')[0])
      let timeSlot
      
      if (hour >= 6 && hour < 12) timeSlot = '上午'
      else if (hour >= 12 && hour < 18) timeSlot = '下午'
      else if (hour >= 18 && hour < 24) timeSlot = '晚上'
      else timeSlot = '深夜'
      
      if (!slots[timeSlot]) {
        slots[timeSlot] = { income: 0, hours: 0 }
      }
      
      slots[timeSlot].income += record.totalSalary
      slots[timeSlot].hours += DateUtils.minutesToHours(record.duration)
    })
    
    return Object.entries(slots).map(([time, data]) => ({
      time,
      rate: data.hours > 0 ? (data.income / data.hours).toFixed(0) : 0
    }))
  },

  /**
   * 分析工作日效率
   */
  analyzeDays(records) {
    const days = {}
    
    records.forEach(record => {
      const dayOfWeek = DateUtils.getDayOfWeek(record.date)
      
      if (!days[dayOfWeek]) {
        days[dayOfWeek] = { income: 0, count: 0 }
      }
      
      days[dayOfWeek].income += record.totalSalary
      days[dayOfWeek].count += 1
    })
    
    return Object.entries(days).map(([day, data]) => ({
      day,
      income: data.count > 0 ? (data.income / data.count).toFixed(0) : 0
    }))
  },

  /**
   * 生成详细统计
   */
  generateDetailedStats(records, workTypes) {
    const totalIncome = records.reduce((sum, r) => sum + r.totalSalary, 0)
    const totalHours = records.reduce((sum, r) => sum + DateUtils.minutesToHours(r.duration), 0)
    
    const detailedStats = [
      {
        label: '总工作记录',
        value: `${records.length}条`,
        percentage: 100
      },
      {
        label: '已完成记录',
        value: `${records.filter(r => r.status === 'completed').length}条`,
        percentage: ((records.filter(r => r.status === 'completed').length / records.length) * 100).toFixed(1)
      },
      {
        label: '平均单次收入',
        value: `¥${records.length > 0 ? (totalIncome / records.length).toFixed(0) : 0}`,
        percentage: 100
      },
      {
        label: '平均工作时长',
        value: `${records.length > 0 ? (totalHours / records.length).toFixed(1) : 0}小时`,
        percentage: 100
      }
    ]
    
    this.setData({ detailedStats })
  },

  /**
   * 生成数据洞察
   */
  generateInsights(records, workTypes) {
    const insights = []
    
    if (records.length > 0) {
      const avgIncome = records.reduce((sum, r) => sum + r.totalSalary, 0) / records.length
      
      if (avgIncome > 100) {
        insights.push({
          id: 1,
          icon: '💰',
          title: '收入表现良好',
          description: `平均单次收入¥${avgIncome.toFixed(0)}，超过平均水平`
        })
      }
      
      const workDays = new Set(records.map(r => r.date)).size
      if (workDays >= 20) {
        insights.push({
          id: 2,
          icon: '📈',
          title: '工作频率较高',
          description: `本期工作${workDays}天，工作频率较为稳定`
        })
      }
    }
    
    this.setData({ insights })
  },

  /**
   * 标签页切换
   */
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({ activeTab: tab })
    
    if (tab !== 'custom') {
      this.loadStatistics()
    }
  },

  /**
   * 自定义开始日期变化
   */
  onStartDateChange(e) {
    this.setData({ customStartDate: e.detail.value })
    this.loadStatistics()
  },

  /**
   * 自定义结束日期变化
   */
  onEndDateChange(e) {
    this.setData({ customEndDate: e.detail.value })
    this.loadStatistics()
  },

  /**
   * 查看收入详情
   */
  onViewIncomeDetail() {
    // 可以跳转到更详细的收入分析页面
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 查看工作类型详情
   */
  onViewWorkTypeDetail() {
    wx.navigateTo({
      url: '/pages/work-type-manage/work-type-manage'
    })
  },

  /**
   * 导出数据
   */
  onExportData() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    })
  },

  /**
   * 添加工作记录
   */
  onAddWork() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadStatistics().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '我的工作统计分析',
      path: '/pages/statistics/statistics'
    }
  }
})
