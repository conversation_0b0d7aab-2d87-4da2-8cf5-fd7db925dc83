/* pages/statistics/statistics.wxss */
/* 统计分析页面样式 */

.statistics-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.stats-header {
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 时间筛选 */
.time-filter {
  margin: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.filter-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 24rpx;
}

.filter-tab {
  flex: 1;
  padding: 16rpx 24rpx;
  background: transparent;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.2s ease;
}

.filter-tab.active {
  background: #007AFF;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.custom-time {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  flex: 1;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.date-picker:active {
  border-color: #007AFF;
  background: #ffffff;
}

.date-separator {
  font-size: 24rpx;
  color: #666;
}

/* 核心统计 */
.core-stats {
  margin: 0 32rpx 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

/* 图表区域 */
.chart-section,
.work-type-analysis {
  margin: 0 32rpx 32rpx;
}

/* 工作效率分析 */
.efficiency-section,
.monthly-comparison,
.detailed-stats,
.goal-section,
.insights-section {
  margin: 0 32rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.efficiency-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.efficiency-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.card-icon {
  font-size: 28rpx;
}

.card-content {
  text-align: center;
}

.efficiency-time,
.efficiency-day,
.intensity-level {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.efficiency-rate,
.efficiency-income,
.intensity-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 月度对比 */
.comparison-chart {
  display: flex;
  align-items: end;
  justify-content: space-around;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  height: 300rpx;
}

.month-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80rpx;
}

.bar-container {
  width: 32rpx;
  height: 200rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  position: relative;
  margin-bottom: 12rpx;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 4rpx;
  transition: height 0.3s ease;
}

.month-label {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.month-value {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}

/* 详细统计表格 */
.stats-table {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row.header {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.col-1,
.col-2,
.col-3 {
  font-size: 26rpx;
  color: #333;
}

.col-2,
.col-3 {
  text-align: center;
}

/* 目标设置 */
.goal-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.goal-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.goal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.goal-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.goal-progress {
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
}

.progress-bar {
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.goal-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-value,
.target-value {
  font-size: 24rpx;
  color: #666;
}

/* 数据洞察 */
.insights-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.insight-item:last-child {
  border-bottom: none;
}

.insight-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.insight-content {
  flex: 1;
}

.insight-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.insight-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 导出按钮 */
.export-section {
  margin: 0 32rpx;
}

.export-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.export-btn:active {
  opacity: 0.8;
}

.export-icon {
  font-size: 36rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.empty-action:active {
  background: #0056CC;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .efficiency-cards {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .comparison-chart {
    height: 250rpx;
    padding: 24rpx 16rpx;
  }
  
  .bar-container {
    height: 150rpx;
  }
  
  .table-row {
    padding: 16rpx 24rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .statistics-container {
    background: #1a1a1a;
  }
  
  .time-filter,
  .efficiency-card,
  .comparison-chart,
  .stats-table,
  .goal-card,
  .insights-list {
    background: #2a2a2a;
  }
  
  .section-title,
  .card-title,
  .goal-title,
  .insight-title,
  .col-1,
  .col-2,
  .col-3 {
    color: #ffffff;
  }
  
  .filter-tabs {
    background: #404040;
  }
  
  .filter-tab {
    color: #adb5bd;
  }
  
  .date-picker {
    background: #404040;
    border-color: #555555;
    color: #ffffff;
  }
  
  .table-row {
    border-bottom-color: #404040;
  }
  
  .table-row.header {
    background: #404040;
  }
  
  .insight-item {
    border-bottom-color: #404040;
  }
  
  .insight-icon {
    background: #1a3a5c;
  }
  
  .progress-bar,
  .bar-container {
    background: #404040;
  }
}
