<!-- pages/statistics/statistics.wxml -->
<!-- 统计分析页面 -->

<view class="statistics-container">
  <!-- 页面头部 -->
  <view class="stats-header">
    <text class="page-title">统计分析</text>
    <text class="page-subtitle">了解你的工作数据</text>
  </view>

  <!-- 时间筛选 -->
  <view class="time-filter">
    <view class="filter-tabs">
      <button 
        class="filter-tab {{activeTab === item.value ? 'active' : ''}}"
        wx:for="{{timeTabs}}" 
        wx:key="value"
        bindtap="onTabChange"
        data-tab="{{item.value}}"
      >
        {{item.label}}
      </button>
    </view>
    
    <!-- 自定义时间选择 -->
    <view class="custom-time" wx:if="{{activeTab === 'custom'}}">
      <picker 
        mode="date" 
        value="{{customStartDate}}" 
        bindchange="onStartDateChange"
      >
        <view class="date-picker">
          <text>{{customStartDate || '开始日期'}}</text>
        </view>
      </picker>
      <text class="date-separator">至</text>
      <picker 
        mode="date" 
        value="{{customEndDate}}" 
        bindchange="onEndDateChange"
      >
        <view class="date-picker">
          <text>{{customEndDate || '结束日期'}}</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 核心统计卡片 -->
  <view class="core-stats">
    <view class="stats-grid">
      <stat-card
        card-type="number"
        title="总收入"
        value="{{coreStats.totalIncome}}"
        unit="元"
        theme="success"
        show-trend="{{true}}"
        trend-data="{{incomeTrend}}"
      />
      <stat-card
        card-type="number"
        title="工作时长"
        value="{{coreStats.totalHours}}"
        unit="小时"
        theme="primary"
      />
      <stat-card
        card-type="number"
        title="工作天数"
        value="{{coreStats.totalDays}}"
        unit="天"
        theme="warning"
      />
      <stat-card
        card-type="number"
        title="平均时薪"
        value="{{coreStats.averageHourlyRate}}"
        unit="元/时"
        theme="default"
      />
    </view>
  </view>

  <!-- 收入趋势图表 -->
  <view class="chart-section">
    <stat-card
      card-type="chart"
      title="收入趋势"
      chart-type="bar"
      chart-data="{{incomeChartData}}"
      show-more="{{true}}"
      bind:moretap="onViewIncomeDetail"
    />
  </view>

  <!-- 工作类型分析 -->
  <view class="work-type-analysis">
    <stat-card
      card-type="list"
      title="工作类型分析"
      list-data="{{workTypeStats}}"
      key-field="id"
      show-more="{{true}}"
      bind:moretap="onViewWorkTypeDetail"
    />
  </view>

  <!-- 工作效率分析 -->
  <view class="efficiency-section">
    <view class="section-title">工作效率分析</view>
    <view class="efficiency-cards">
      <view class="efficiency-card">
        <view class="card-header">
          <text class="card-title">最高效时段</text>
          <text class="card-icon">⏰</text>
        </view>
        <view class="card-content">
          <text class="efficiency-time">{{bestTimeSlot.time}}</text>
          <text class="efficiency-rate">平均时薪 ¥{{bestTimeSlot.rate}}</text>
        </view>
      </view>
      
      <view class="efficiency-card">
        <view class="card-header">
          <text class="card-title">最佳工作日</text>
          <text class="card-icon">📅</text>
        </view>
        <view class="card-content">
          <text class="efficiency-day">{{bestWorkDay.day}}</text>
          <text class="efficiency-income">平均收入 ¥{{bestWorkDay.income}}</text>
        </view>
      </view>
      
      <view class="efficiency-card">
        <view class="card-header">
          <text class="card-title">工作强度</text>
          <text class="card-icon">💪</text>
        </view>
        <view class="card-content">
          <text class="intensity-level">{{workIntensity.level}}</text>
          <text class="intensity-desc">{{workIntensity.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 月度对比 -->
  <view class="monthly-comparison" wx:if="{{monthlyData.length > 1}}">
    <view class="section-title">月度对比</view>
    <view class="comparison-chart">
      <view 
        class="month-bar"
        wx:for="{{monthlyData}}" 
        wx:key="month"
      >
        <view class="bar-container">
          <view 
            class="bar-fill" 
            style="height: {{item.percentage}}%; background-color: {{item.color}}"
          ></view>
        </view>
        <text class="month-label">{{item.month}}</text>
        <text class="month-value">¥{{item.income}}</text>
      </view>
    </view>
  </view>

  <!-- 详细统计 -->
  <view class="detailed-stats">
    <view class="section-title">详细统计</view>
    <view class="stats-table">
      <view class="table-row header">
        <text class="col-1">指标</text>
        <text class="col-2">数值</text>
        <text class="col-3">占比</text>
      </view>
      
      <view class="table-row" wx:for="{{detailedStats}}" wx:key="label">
        <text class="col-1">{{item.label}}</text>
        <text class="col-2">{{item.value}}</text>
        <text class="col-3">{{item.percentage}}%</text>
      </view>
    </view>
  </view>

  <!-- 目标设置 -->
  <view class="goal-section" wx:if="{{showGoals}}">
    <view class="section-title">目标达成</view>
    <view class="goal-cards">
      <view class="goal-card" wx:for="{{goals}}" wx:key="type">
        <view class="goal-header">
          <text class="goal-title">{{item.title}}</text>
          <text class="goal-progress">{{item.progress}}%</text>
        </view>
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            style="width: {{item.progress}}%; background-color: {{item.color}}"
          ></view>
        </view>
        <view class="goal-details">
          <text class="current-value">当前: {{item.current}}</text>
          <text class="target-value">目标: {{item.target}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据洞察 */
  <view class="insights-section" wx:if="{{insights.length > 0}}">
    <view class="section-title">数据洞察</view>
    <view class="insights-list">
      <view class="insight-item" wx:for="{{insights}}" wx:key="id">
        <view class="insight-icon">{{item.icon}}</view>
        <view class="insight-content">
          <text class="insight-title">{{item.title}}</text>
          <text class="insight-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 导出按钮 -->
  <view class="export-section">
    <button class="export-btn" bindtap="onExportData">
      <text class="export-icon">📊</text>
      <text class="export-text">导出统计报告</text>
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-text">正在分析数据...</text>
  </view>
</view>

<!-- 空状态 -->
<view class="empty-state" wx:if="{{!loading && isEmpty}}">
  <view class="empty-icon">📊</view>
  <text class="empty-title">暂无统计数据</text>
  <text class="empty-desc">添加一些工作记录后再来查看统计分析</text>
  <button class="empty-action" bindtap="onAddWork">
    添加工作记录
  </button>
</view>
