// pages/work-types/work-types.js
// 工作类型页面（重定向到工作类型管理页面）

Page({
  /**
   * 页面的初始数据
   */
  data: {},

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 重定向到工作类型管理页面
    wx.redirectTo({
      url: '/pages/work-type-manage/work-type-manage',
      fail: () => {
        console.error('重定向到工作类型管理页面失败')
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '兼职工作管理 - 工作类型',
      path: '/pages/home/<USER>'
    }
  }
})
