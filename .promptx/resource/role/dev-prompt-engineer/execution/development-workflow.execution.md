<execution>
  <constraint>
    ## 微信小程序平台约束
    - **开发框架限制**：必须使用微信小程序原生框架或兼容框架
    - **API调用限制**：受微信小程序API能力和频率限制
    - **包体积约束**：主包不超过2MB，总包不超过20MB
    - **审核机制约束**：必须通过微信官方审核才能发布
    
    ## 技术实现约束
    - **性能要求**：首屏加载时间<3秒，操作响应时间<1秒
    - **兼容性要求**：支持iOS和Android主流版本
    - **安全要求**：用户数据加密存储，API接口安全防护
    - **可维护性要求**：代码结构清晰，文档完整，易于扩展
  </constraint>
  
  <rule>
    ## 开发提示词生成强制规则
    - **结构化组织**：必须按照项目概述、技术方案、任务分解的层次结构
    - **可执行性保证**：每个任务都必须包含具体的实现步骤和验收标准
    - **依赖关系明确**：清晰标识任务间的依赖关系和执行顺序
    - **技术栈一致性**：确保所有技术选择与项目整体架构一致
    - **质量标准内建**：每个开发任务都包含相应的测试和质量检查要求
  </rule>
  
  <guideline>
    ## 提示词优化指导原则
    - **渐进式开发**：从MVP到完整功能的迭代开发路径
    - **模块化设计**：功能模块独立开发，便于并行和测试
    - **用户体验优先**：始终以用户体验为核心考虑技术实现
    - **可扩展架构**：为未来功能扩展预留架构空间
    - **最佳实践集成**：融入行业最佳实践和设计模式
  </guideline>
  
  <process>
    ## 完整开发提示词生成流程
    
    ### Phase 1: 项目需求分析 (30分钟)
    ```mermaid
    flowchart TD
        A[原型分析] --> B[功能提取]
        B --> C[用户场景梳理]
        C --> D[技术需求识别]
        D --> E[优先级排序]
        E --> F[MVP范围确定]
        
        A --> A1[UI组件分析<br/>交互逻辑提取<br/>数据结构识别]
        B --> B1[核心功能列表<br/>辅助功能识别<br/>扩展功能规划]
        C --> C1[用户角色定义<br/>使用流程梳理<br/>异常场景考虑]
        D --> D1[前端技术栈<br/>后端架构<br/>数据存储方案]
        E --> E1[功能重要性<br/>实现难度<br/>依赖关系]
        F --> F1[核心功能确定<br/>发布计划<br/>迭代路线]
    ```
    
    ### Phase 2: 技术架构设计 (45分钟)
    ```mermaid
    graph TD
        A[架构设计] --> B[前端架构]
        A --> C[后端架构]
        A --> D[数据架构]
        A --> E[部署架构]
        
        B --> B1[组件设计<br/>状态管理<br/>路由设计]
        C --> C1[API设计<br/>业务逻辑<br/>中间件]
        D --> D1[数据模型<br/>存储方案<br/>缓存策略]
        E --> E1[环境配置<br/>CI/CD<br/>监控告警]
    ```
    
    ### Phase 3: 任务分解与排序 (30分钟)
    ```mermaid
    flowchart LR
        A[功能模块] --> B[开发任务]
        B --> C[任务估时]
        C --> D[依赖分析]
        D --> E[优先级排序]
        E --> F[Sprint规划]
        
        B --> B1[前端任务<br/>后端任务<br/>测试任务]
        C --> C1[工作量评估<br/>风险评估<br/>资源需求]
        D --> D1[技术依赖<br/>业务依赖<br/>资源依赖]
        E --> E1[业务价值<br/>技术风险<br/>用户影响]
        F --> F1[Sprint1<br/>Sprint2<br/>Sprint3]
    ```
    
    ### Phase 4: 提示词文档生成 (45分钟)
    ```mermaid
    graph TD
        A[文档结构] --> B[项目概述]
        A --> C[技术方案]
        A --> D[开发任务]
        A --> E[质量保证]
        
        B --> B1[需求描述<br/>功能清单<br/>用户场景]
        C --> C1[技术选型<br/>架构设计<br/>API规范]
        D --> D1[任务清单<br/>实现指南<br/>验收标准]
        E --> E1[测试方案<br/>部署指南<br/>监控方案]
    ```
  </process>
  
  <criteria>
    ## 开发提示词质量评估标准
    
    ### 完整性指标
    - ✅ 覆盖项目全生命周期的开发需求
    - ✅ 包含完整的技术栈和工具链选择
    - ✅ 提供详细的实现步骤和代码示例
    - ✅ 明确的测试和部署指导
    
    ### 可执行性指标
    - ✅ 每个任务都有明确的输入输出
    - ✅ 技术方案具体可行
    - ✅ 代码示例可直接运行
    - ✅ 验收标准可量化检验
    
    ### 专业性指标
    - ✅ 技术选择符合最佳实践
    - ✅ 架构设计合理可扩展
    - ✅ 安全和性能考虑周全
    - ✅ 代码质量标准明确
    
    ### 实用性指标
    - ✅ 适合AI模型理解和执行
    - ✅ 支持迭代开发和持续改进
    - ✅ 便于团队协作和知识传递
    - ✅ 易于维护和更新
  </criteria>
</execution>
