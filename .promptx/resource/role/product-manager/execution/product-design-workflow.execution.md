<execution>
  <constraint>
    ## 微信小程序技术约束
    - **页面栈限制**：最多10个页面层级，需要合理设计页面跳转逻辑
    - **包体积限制**：主包不超过2MB，分包总体积不超过20MB
    - **API调用限制**：网络请求、本地存储等API有频率和容量限制
    - **审核周期约束**：功能更新需要经过微信审核，影响迭代速度
    
    ## 用户体验约束
    - **加载时间要求**：页面首屏加载时间不超过3秒
    - **操作便捷性**：核心功能操作步骤不超过3步
    - **视觉一致性**：遵循微信设计规范，保持界面风格统一
    - **无障碍访问**：考虑视觉障碍用户的使用需求
  </constraint>
  
  <rule>
    ## 产品设计强制规则
    - **数据结构优先**：必须先设计清晰的数据模型，再设计功能界面
    - **异常处理完备**：每个功能都必须考虑异常情况的处理方案
    - **性能指标达标**：关键操作响应时间必须在可接受范围内
    - **用户反馈闭环**：每个用户操作都必须有明确的反馈
    - **数据安全保障**：用户数据必须加密存储，确保隐私安全
  </rule>
  
  <guideline>
    ## 产品设计指导原则
    - **渐进式披露**：复杂功能采用分步引导，避免信息过载
    - **容错性设计**：允许用户犯错，提供撤销和修改机制
    - **个性化适配**：根据用户使用习惯提供个性化设置
    - **社交化元素**：适度引入社交功能，提升用户粘性
    - **数据可视化**：用图表等方式直观展示用户数据
  </guideline>
  
  <process>
    ## 完整产品设计流程
    
    ### Phase 1: 需求分析与用户研究 (1-2周)
    ```mermaid
    flowchart TD
        A[用户访谈] --> B[痛点识别]
        B --> C[需求优先级排序]
        C --> D[用户画像构建]
        D --> E[使用场景梳理]
        E --> F[需求文档输出]
    ```
    
    ### Phase 2: 产品架构设计 (1周)
    ```mermaid
    graph TD
        A[信息架构设计] --> B[功能模块划分]
        B --> C[页面流程设计]
        C --> D[数据结构设计]
        D --> E[技术架构评估]
        E --> F[架构文档输出]
    ```
    
    ### Phase 3: 交互与视觉设计 (2-3周)
    ```mermaid
    flowchart LR
        A[线框图设计] --> B[交互原型]
        B --> C[视觉设计]
        C --> D[设计规范]
        D --> E[开发切图]
        E --> F[设计交付]
    ```
    
    ### Phase 4: 开发与测试 (4-6周)
    ```mermaid
    graph TD
        A[技术方案确认] --> B[开发排期规划]
        B --> C[功能开发]
        C --> D[联调测试]
        D --> E[用户测试]
        E --> F[问题修复]
        F --> G[发布准备]
    ```
    
    ### Phase 5: 上线与优化 (持续)
    ```mermaid
    flowchart TD
        A[产品发布] --> B[用户反馈收集]
        B --> C[数据分析]
        C --> D[问题识别]
        D --> E[优化方案制定]
        E --> F[版本迭代]
        F --> B
    ```
  </process>
  
  <criteria>
    ## 产品质量评估标准
    
    ### 功能完整性
    - ✅ 核心功能覆盖用户主要使用场景
    - ✅ 异常情况处理方案完备
    - ✅ 用户操作流程顺畅无阻断
    - ✅ 数据录入和查询功能稳定可靠
    
    ### 用户体验质量
    - ✅ 界面设计简洁直观
    - ✅ 操作响应及时准确
    - ✅ 学习成本低，易于上手
    - ✅ 错误提示友好明确
    
    ### 技术性能指标
    - ✅ 页面加载时间 < 3秒
    - ✅ 操作响应时间 < 1秒
    - ✅ 崩溃率 < 0.1%
    - ✅ 内存占用合理
    
    ### 商业价值实现
    - ✅ 用户留存率 > 60%
    - ✅ 日活跃用户稳定增长
    - ✅ 用户满意度 > 4.0/5.0
    - ✅ 功能使用率达到预期
  </criteria>
</execution>
