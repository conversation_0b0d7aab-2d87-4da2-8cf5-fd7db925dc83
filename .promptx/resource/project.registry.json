{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-28T13:34:07.043Z", "updatedAt": "2025-08-28T13:34:07.047Z", "resourceCount": 6}, "resources": [{"id": "dev-prompt-engineer", "source": "project", "protocol": "role", "name": "Dev Prompt Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/dev-prompt-engineer/dev-prompt-engineer.role.md", "metadata": {"createdAt": "2025-08-28T13:34:07.044Z", "updatedAt": "2025-08-28T13:34:07.044Z", "scannedAt": "2025-08-28T13:34:07.044Z", "path": "role/dev-prompt-engineer/dev-prompt-engineer.role.md"}}, {"id": "development-workflow", "source": "project", "protocol": "execution", "name": "Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/dev-prompt-engineer/execution/development-workflow.execution.md", "metadata": {"createdAt": "2025-08-28T13:34:07.045Z", "updatedAt": "2025-08-28T13:34:07.045Z", "scannedAt": "2025-08-28T13:34:07.045Z", "path": "role/dev-prompt-engineer/execution/development-workflow.execution.md"}}, {"id": "software-development-thinking", "source": "project", "protocol": "thought", "name": "Software Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/dev-prompt-engineer/thought/software-development-thinking.thought.md", "metadata": {"createdAt": "2025-08-28T13:34:07.045Z", "updatedAt": "2025-08-28T13:34:07.045Z", "scannedAt": "2025-08-28T13:34:07.045Z", "path": "role/dev-prompt-engineer/thought/software-development-thinking.thought.md"}}, {"id": "product-design-workflow", "source": "project", "protocol": "execution", "name": "Product Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/product-manager/execution/product-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-28T13:34:07.046Z", "updatedAt": "2025-08-28T13:34:07.046Z", "scannedAt": "2025-08-28T13:34:07.046Z", "path": "role/product-manager/execution/product-design-workflow.execution.md"}}, {"id": "product-manager", "source": "project", "protocol": "role", "name": "Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-manager/product-manager.role.md", "metadata": {"createdAt": "2025-08-28T13:34:07.046Z", "updatedAt": "2025-08-28T13:34:07.046Z", "scannedAt": "2025-08-28T13:34:07.046Z", "path": "role/product-manager/product-manager.role.md"}}, {"id": "product-thinking", "source": "project", "protocol": "thought", "name": "Product Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/product-manager/thought/product-thinking.thought.md", "metadata": {"createdAt": "2025-08-28T13:34:07.046Z", "updatedAt": "2025-08-28T13:34:07.046Z", "scannedAt": "2025-08-28T13:34:07.046Z", "path": "role/product-manager/thought/product-thinking.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 2, "execution": 2, "thought": 2}, "bySource": {"project": 6}}}