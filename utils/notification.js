// utils/notification.js
// 提醒通知工具类

import DateUtils from './date.js'
import { workRecordStorage } from './storage.js'

/**
 * 通知管理器
 */
class NotificationManager {
  
  /**
   * 初始化通知系统
   */
  static init() {
    this.checkPermission()
    this.scheduleNotifications()
  }
  
  /**
   * 检查通知权限
   */
  static checkPermission() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.userInfo']) {
          // 用户未授权，可以引导用户授权
          console.log('用户未授权通知权限')
        }
      }
    })
  }
  
  /**
   * 请求通知权限
   */
  static requestPermission() {
    return new Promise((resolve) => {
      wx.authorize({
        scope: 'scope.userInfo',
        success: () => {
          resolve(true)
        },
        fail: () => {
          wx.showModal({
            title: '通知权限',
            content: '开启通知权限可以及时提醒您的工作安排',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
              resolve(false)
            }
          })
        }
      })
    })
  }
  
  /**
   * 安排通知
   */
  static async scheduleNotifications() {
    try {
      const settings = this.getNotificationSettings()
      if (!settings.enabled) return
      
      // 清除现有通知
      this.clearAllNotifications()
      
      // 安排工作提醒
      if (settings.workReminder.enabled) {
        await this.scheduleWorkReminders()
      }
      
      // 安排统计提醒
      if (settings.statsReminder.enabled) {
        this.scheduleStatsReminders()
      }
      
      // 安排每日提醒
      if (settings.dailyReminder.enabled) {
        this.scheduleDailyReminders()
      }
    } catch (error) {
      console.error('安排通知失败:', error)
    }
  }
  
  /**
   * 安排工作提醒
   */
  static async scheduleWorkReminders() {
    try {
      const result = await workRecordStorage.getAll()
      if (!result.success) return
      
      const records = result.data || []
      const today = DateUtils.getToday()
      const tomorrow = DateUtils.getDateAfter(today, 1)
      
      // 今日工作提醒
      const todayRecords = records.filter(r => 
        r.date === today && r.status === 'planned'
      )
      
      todayRecords.forEach(record => {
        this.scheduleWorkNotification(record, 'today')
      })
      
      // 明日工作提醒
      const tomorrowRecords = records.filter(r => 
        r.date === tomorrow && r.status === 'planned'
      )
      
      if (tomorrowRecords.length > 0) {
        this.scheduleWorkNotification(tomorrowRecords[0], 'tomorrow')
      }
    } catch (error) {
      console.error('安排工作提醒失败:', error)
    }
  }
  
  /**
   * 安排单个工作通知
   */
  static scheduleWorkNotification(record, type) {
    const settings = this.getNotificationSettings()
    const workSettings = settings.workReminder
    
    let notifyTime
    let title
    let content
    
    if (type === 'today') {
      // 工作开始前提醒
      const workStartTime = new Date(`${record.date} ${record.startTime}`)
      notifyTime = new Date(workStartTime.getTime() - workSettings.advanceMinutes * 60000)
      
      title = '工作提醒'
      content = `${record.startTime} 开始工作：${record.workTypeName || '工作'}`
    } else if (type === 'tomorrow') {
      // 明日工作提醒
      const reminderTime = workSettings.tomorrowReminderTime.split(':')
      notifyTime = new Date()
      notifyTime.setDate(notifyTime.getDate())
      notifyTime.setHours(parseInt(reminderTime[0]), parseInt(reminderTime[1]), 0, 0)
      
      title = '明日工作提醒'
      content = `明天 ${record.startTime} 有工作安排`
    }
    
    // 只安排未来的通知
    if (notifyTime > new Date()) {
      this.scheduleLocalNotification({
        id: `work_${record.id}_${type}`,
        title,
        content,
        time: notifyTime,
        data: { type: 'work', recordId: record.id }
      })
    }
  }
  
  /**
   * 安排统计提醒
   */
  static scheduleStatsReminders() {
    const settings = this.getNotificationSettings()
    const statsSettings = settings.statsReminder
    
    if (statsSettings.weekly.enabled) {
      this.scheduleWeeklyStatsReminder()
    }
    
    if (statsSettings.monthly.enabled) {
      this.scheduleMonthlyStatsReminder()
    }
  }
  
  /**
   * 安排周统计提醒
   */
  static scheduleWeeklyStatsReminder() {
    const settings = this.getNotificationSettings()
    const { dayOfWeek, time } = settings.statsReminder.weekly
    
    const nextReminderTime = this.getNextWeeklyTime(dayOfWeek, time)
    
    this.scheduleLocalNotification({
      id: 'weekly_stats',
      title: '周统计提醒',
      content: '查看本周工作统计，了解收入和工作时长',
      time: nextReminderTime,
      data: { type: 'stats', period: 'week' }
    })
  }
  
  /**
   * 安排月统计提醒
   */
  static scheduleMonthlyStatsReminder() {
    const settings = this.getNotificationSettings()
    const { dayOfMonth, time } = settings.statsReminder.monthly
    
    const nextReminderTime = this.getNextMonthlyTime(dayOfMonth, time)
    
    this.scheduleLocalNotification({
      id: 'monthly_stats',
      title: '月统计提醒',
      content: '查看本月工作统计，分析收入趋势',
      time: nextReminderTime,
      data: { type: 'stats', period: 'month' }
    })
  }
  
  /**
   * 安排每日提醒
   */
  static scheduleDailyReminders() {
    const settings = this.getNotificationSettings()
    const dailySettings = settings.dailyReminder
    
    const reminderTime = dailySettings.time.split(':')
    const notifyTime = new Date()
    notifyTime.setDate(notifyTime.getDate() + 1) // 明天
    notifyTime.setHours(parseInt(reminderTime[0]), parseInt(reminderTime[1]), 0, 0)
    
    this.scheduleLocalNotification({
      id: 'daily_reminder',
      title: '每日提醒',
      content: dailySettings.message || '记得记录今天的工作哦！',
      time: notifyTime,
      data: { type: 'daily' }
    })
  }
  
  /**
   * 安排本地通知
   */
  static scheduleLocalNotification(notification) {
    try {
      // 微信小程序不支持本地通知，这里只是模拟
      // 实际应用中可以使用服务器推送或其他方式
      
      const notifications = wx.getStorageSync('scheduledNotifications') || []
      notifications.push({
        ...notification,
        scheduledAt: Date.now()
      })
      
      wx.setStorageSync('scheduledNotifications', notifications)
      
      console.log('通知已安排:', notification)
    } catch (error) {
      console.error('安排通知失败:', error)
    }
  }
  
  /**
   * 获取下一个周提醒时间
   */
  static getNextWeeklyTime(dayOfWeek, time) {
    const now = new Date()
    const [hours, minutes] = time.split(':').map(Number)
    
    const nextTime = new Date()
    nextTime.setHours(hours, minutes, 0, 0)
    
    // 计算到指定星期几的天数
    const currentDay = now.getDay()
    const targetDay = dayOfWeek === 0 ? 7 : dayOfWeek // 周日为7
    let daysToAdd = targetDay - currentDay
    
    if (daysToAdd <= 0 || (daysToAdd === 0 && now > nextTime)) {
      daysToAdd += 7 // 下周
    }
    
    nextTime.setDate(now.getDate() + daysToAdd)
    return nextTime
  }
  
  /**
   * 获取下一个月提醒时间
   */
  static getNextMonthlyTime(dayOfMonth, time) {
    const now = new Date()
    const [hours, minutes] = time.split(':').map(Number)
    
    const nextTime = new Date()
    nextTime.setDate(dayOfMonth)
    nextTime.setHours(hours, minutes, 0, 0)
    
    // 如果本月的日期已过，则设置为下个月
    if (nextTime <= now) {
      nextTime.setMonth(nextTime.getMonth() + 1)
    }
    
    // 处理月末日期（如31号在2月不存在）
    if (nextTime.getDate() !== dayOfMonth) {
      nextTime.setDate(0) // 设置为上个月的最后一天
    }
    
    return nextTime
  }
  
  /**
   * 清除所有通知
   */
  static clearAllNotifications() {
    try {
      wx.removeStorageSync('scheduledNotifications')
    } catch (error) {
      console.error('清除通知失败:', error)
    }
  }
  
  /**
   * 清除指定通知
   */
  static clearNotification(id) {
    try {
      const notifications = wx.getStorageSync('scheduledNotifications') || []
      const filteredNotifications = notifications.filter(n => n.id !== id)
      wx.setStorageSync('scheduledNotifications', filteredNotifications)
    } catch (error) {
      console.error('清除通知失败:', error)
    }
  }
  
  /**
   * 获取通知设置
   */
  static getNotificationSettings() {
    try {
      const settings = wx.getStorageSync('notificationSettings')
      return settings || this.getDefaultSettings()
    } catch (error) {
      return this.getDefaultSettings()
    }
  }
  
  /**
   * 保存通知设置
   */
  static saveNotificationSettings(settings) {
    try {
      wx.setStorageSync('notificationSettings', settings)
      // 重新安排通知
      this.scheduleNotifications()
    } catch (error) {
      console.error('保存通知设置失败:', error)
    }
  }
  
  /**
   * 获取默认设置
   */
  static getDefaultSettings() {
    return {
      enabled: true,
      workReminder: {
        enabled: true,
        advanceMinutes: 15, // 提前15分钟提醒
        tomorrowReminderTime: '20:00' // 晚上8点提醒明日工作
      },
      statsReminder: {
        enabled: true,
        weekly: {
          enabled: true,
          dayOfWeek: 0, // 周日
          time: '19:00'
        },
        monthly: {
          enabled: true,
          dayOfMonth: 1, // 每月1号
          time: '10:00'
        }
      },
      dailyReminder: {
        enabled: false,
        time: '21:00',
        message: '记得记录今天的工作哦！'
      }
    }
  }
  
  /**
   * 检查并触发到期通知
   */
  static checkPendingNotifications() {
    try {
      const notifications = wx.getStorageSync('scheduledNotifications') || []
      const now = new Date()
      
      notifications.forEach(notification => {
        const notifyTime = new Date(notification.time)
        if (notifyTime <= now) {
          this.triggerNotification(notification)
          this.clearNotification(notification.id)
        }
      })
    } catch (error) {
      console.error('检查通知失败:', error)
    }
  }
  
  /**
   * 触发通知
   */
  static triggerNotification(notification) {
    // 在小程序中，我们可以显示模态框或toast
    wx.showModal({
      title: notification.title,
      content: notification.content,
      showCancel: false,
      success: () => {
        // 可以根据通知类型跳转到相应页面
        this.handleNotificationAction(notification.data)
      }
    })
  }
  
  /**
   * 处理通知点击
   */
  static handleNotificationAction(data) {
    if (!data) return
    
    switch (data.type) {
      case 'work':
        // 跳转到工作详情
        if (data.recordId) {
          wx.navigateTo({
            url: `/pages/work-detail/work-detail?id=${data.recordId}`
          })
        }
        break
      case 'stats':
        // 跳转到统计页面
        wx.switchTab({
          url: '/pages/statistics/statistics'
        })
        break
      case 'daily':
        // 跳转到添加工作页面
        wx.navigateTo({
          url: '/pages/add-work/add-work'
        })
        break
    }
  }
  
  /**
   * 获取已安排的通知
   */
  static getScheduledNotifications() {
    try {
      return wx.getStorageSync('scheduledNotifications') || []
    } catch (error) {
      return []
    }
  }
  
  /**
   * 测试通知
   */
  static testNotification() {
    const testTime = new Date(Date.now() + 5000) // 5秒后
    
    this.scheduleLocalNotification({
      id: 'test_notification',
      title: '测试通知',
      content: '这是一个测试通知，用于验证通知功能是否正常',
      time: testTime,
      data: { type: 'test' }
    })
    
    wx.showToast({
      title: '测试通知已安排',
      icon: 'success'
    })
  }
}

export default NotificationManager
