// utils/test-suite.js
// 完整的测试套件

import { featureTester, deviceCompatibility } from "./compatibility.js";
import { logger } from "./error-handler.js";
import { performanceManager } from "./performance.js";
import { workRecordStorage, workTypeStorage } from "./storage.js";
import { WorkRecord } from "../data/work-record.js";
import { WorkType } from "../data/work-type.js";

/**
 * 测试套件管理器
 */
class TestSuite {
  constructor() {
    this.testResults = new Map();
    this.testConfig = {
      timeout: 10000, // 10秒超时
      retries: 3,
      parallel: false,
    };
  }

  /**
   * 运行完整测试套件
   */
  async runFullTestSuite() {
    console.log("🧪 开始运行完整测试套件...");

    const startTime = Date.now();
    const results = {
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0,
      },
      categories: {},
      details: [],
    };

    try {
      // 设备兼容性测试
      results.categories.compatibility = await this.runCompatibilityTests();

      // 功能测试
      results.categories.functionality = await this.runFunctionalityTests();

      // 性能测试
      results.categories.performance = await this.runPerformanceTests();

      // 数据完整性测试
      results.categories.dataIntegrity = await this.runDataIntegrityTests();

      // UI/UX测试
      results.categories.uiux = await this.runUIUXTests();

      // 边界条件测试
      results.categories.boundary = await this.runBoundaryTests();

      // 计算总结
      this.calculateSummary(results);

      results.summary.duration = Date.now() - startTime;

      console.log("✅ 测试套件运行完成");
      this.printTestReport(results);

      return results;
    } catch (error) {
      console.error("❌ 测试套件运行失败:", error);
      logger.error("测试套件运行失败", error, "test");
      throw error;
    }
  }

  /**
   * 运行兼容性测试
   */
  async runCompatibilityTests() {
    console.log("📱 运行设备兼容性测试...");

    const tests = [
      {
        name: "设备信息检测",
        test: () => this.testDeviceInfo(),
      },
      {
        name: "API兼容性检查",
        test: () => this.testAPICompatibility(),
      },
      {
        name: "存储兼容性",
        test: () => this.testStorageCompatibility(),
      },
      {
        name: "网络兼容性",
        test: () => this.testNetworkCompatibility(),
      },
    ];

    return await this.runTestCategory("compatibility", tests);
  }

  /**
   * 运行功能测试
   */
  async runFunctionalityTests() {
    console.log("⚙️ 运行功能测试...");

    const tests = [
      {
        name: "工作记录CRUD",
        test: () => this.testWorkRecordCRUD(),
      },
      {
        name: "工作类型管理",
        test: () => this.testWorkTypeManagement(),
      },
      {
        name: "数据计算功能",
        test: () => this.testCalculationFunctions(),
      },
      {
        name: "导入导出功能",
        test: () => this.testImportExportFunctions(),
      },
      {
        name: "通知功能",
        test: () => this.testNotificationFunctions(),
      },
    ];

    return await this.runTestCategory("functionality", tests);
  }

  /**
   * 运行性能测试
   */
  async runPerformanceTests() {
    console.log("🚀 运行性能测试...");

    const tests = [
      {
        name: "页面加载性能",
        test: () => this.testPageLoadPerformance(),
      },
      {
        name: "数据处理性能",
        test: () => this.testDataProcessingPerformance(),
      },
      {
        name: "内存使用测试",
        test: () => this.testMemoryUsage(),
      },
      {
        name: "大数据量测试",
        test: () => this.testLargeDataSet(),
      },
    ];

    return await this.runTestCategory("performance", tests);
  }

  /**
   * 运行数据完整性测试
   */
  async runDataIntegrityTests() {
    console.log("🔒 运行数据完整性测试...");

    const tests = [
      {
        name: "数据验证测试",
        test: () => this.testDataValidation(),
      },
      {
        name: "数据持久化测试",
        test: () => this.testDataPersistence(),
      },
      {
        name: "数据同步测试",
        test: () => this.testDataSynchronization(),
      },
      {
        name: "数据恢复测试",
        test: () => this.testDataRecovery(),
      },
    ];

    return await this.runTestCategory("dataIntegrity", tests);
  }

  /**
   * 运行UI/UX测试
   */
  async runUIUXTests() {
    console.log("🎨 运行UI/UX测试...");

    const tests = [
      {
        name: "响应式布局测试",
        test: () => this.testResponsiveLayout(),
      },
      {
        name: "主题切换测试",
        test: () => this.testThemeSwitching(),
      },
      {
        name: "交互反馈测试",
        test: () => this.testInteractionFeedback(),
      },
      {
        name: "无障碍访问测试",
        test: () => this.testAccessibility(),
      },
    ];

    return await this.runTestCategory("uiux", tests);
  }

  /**
   * 运行边界条件测试
   */
  async runBoundaryTests() {
    console.log("🔍 运行边界条件测试...");

    const tests = [
      {
        name: "空数据处理",
        test: () => this.testEmptyDataHandling(),
      },
      {
        name: "异常输入处理",
        test: () => this.testInvalidInputHandling(),
      },
      {
        name: "网络异常处理",
        test: () => this.testNetworkErrorHandling(),
      },
      {
        name: "存储限制测试",
        test: () => this.testStorageLimits(),
      },
    ];

    return await this.runTestCategory("boundary", tests);
  }

  /**
   * 运行测试分类
   */
  async runTestCategory(categoryName, tests) {
    const results = {
      total: tests.length,
      passed: 0,
      failed: 0,
      skipped: 0,
      tests: [],
    };

    for (const testCase of tests) {
      try {
        console.log(`  🧪 ${testCase.name}...`);

        const startTime = Date.now();
        const result = await this.runSingleTest(testCase.test);
        const duration = Date.now() - startTime;

        results.tests.push({
          name: testCase.name,
          status: "passed",
          duration,
          result,
        });
        results.passed++;

        console.log(`  ✅ ${testCase.name} (${duration}ms)`);
      } catch (error) {
        results.tests.push({
          name: testCase.name,
          status: "failed",
          error: error.message,
          stack: error.stack,
        });
        results.failed++;

        console.error(`  ❌ ${testCase.name}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * 运行单个测试
   */
  async runSingleTest(testFunction) {
    return Promise.race([
      testFunction(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("测试超时")), this.testConfig.timeout)
      ),
    ]);
  }

  /**
   * 测试设备信息
   */
  async testDeviceInfo() {
    const deviceInfo = deviceCompatibility.getDeviceInfo();

    if (!deviceInfo) {
      throw new Error("无法获取设备信息");
    }

    const requiredFields = ["platform", "system", "version", "model"];
    for (const field of requiredFields) {
      if (!deviceInfo[field]) {
        throw new Error(`缺少设备信息字段: ${field}`);
      }
    }

    return { deviceInfo, message: "设备信息检测通过" };
  }

  /**
   * 测试API兼容性
   */
  async testAPICompatibility() {
    const apis = [
      "getSystemInfo",
      "setStorage",
      "getStorage",
      "request",
      "showToast",
      "showModal",
    ];

    const results = {};

    for (const api of apis) {
      results[api] = typeof wx[api] === "function";
    }

    const unsupportedAPIs = Object.entries(results)
      .filter(([api, supported]) => !supported)
      .map(([api]) => api);

    if (unsupportedAPIs.length > 0) {
      throw new Error(`不支持的API: ${unsupportedAPIs.join(", ")}`);
    }

    return { results, message: "API兼容性检查通过" };
  }

  /**
   * 测试存储兼容性
   */
  async testStorageCompatibility() {
    const testKey = "compatibility_test";
    const testData = {
      string: "test",
      number: 123,
      boolean: true,
      object: { nested: "value" },
      array: [1, 2, 3],
    };

    // 测试同步存储
    wx.setStorageSync(testKey, testData);
    const retrieved = wx.getStorageSync(testKey);

    if (JSON.stringify(retrieved) !== JSON.stringify(testData)) {
      throw new Error("同步存储数据不一致");
    }

    // 测试异步存储
    await new Promise((resolve, reject) => {
      wx.setStorage({
        key: testKey + "_async",
        data: testData,
        success: resolve,
        fail: reject,
      });
    });

    const asyncRetrieved = await new Promise((resolve, reject) => {
      wx.getStorage({
        key: testKey + "_async",
        success: (res) => resolve(res.data),
        fail: reject,
      });
    });

    if (JSON.stringify(asyncRetrieved) !== JSON.stringify(testData)) {
      throw new Error("异步存储数据不一致");
    }

    // 清理测试数据
    wx.removeStorageSync(testKey);
    wx.removeStorageSync(testKey + "_async");

    return { message: "存储兼容性测试通过" };
  }

  /**
   * 测试网络兼容性
   */
  async testNetworkCompatibility() {
    // 测试网络状态获取
    const networkType = await new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => resolve(res.networkType),
        fail: () => resolve("unknown"),
      });
    });

    if (networkType === "none") {
      throw new Error("无网络连接");
    }

    return { networkType, message: "网络兼容性测试通过" };
  }

  /**
   * 测试工作记录CRUD
   */
  async testWorkRecordCRUD() {
    const testRecord = new WorkRecord({
      workTypeId: "test-type",
      date: "2024-01-01",
      startTime: "09:00",
      endTime: "17:00",
      hourlyRate: 50,
      title: "测试工作",
    });

    // 创建
    const saveResult = await workRecordStorage.save(testRecord.toStorage());
    if (!saveResult.success) {
      throw new Error("创建工作记录失败");
    }

    // 读取
    const getResult = await workRecordStorage.getById(testRecord.id);
    if (!getResult.success || !getResult.data) {
      throw new Error("读取工作记录失败");
    }

    // 更新
    testRecord.title = "更新后的测试工作";
    const updateResult = await workRecordStorage.save(testRecord.toStorage());
    if (!updateResult.success) {
      throw new Error("更新工作记录失败");
    }

    // 删除
    const deleteResult = await workRecordStorage.delete(testRecord.id);
    if (!deleteResult.success) {
      throw new Error("删除工作记录失败");
    }

    return { message: "工作记录CRUD测试通过" };
  }

  /**
   * 测试工作类型管理
   */
  async testWorkTypeManagement() {
    const testWorkType = new WorkType({
      name: "测试工作类型",
      icon: "🧪",
      color: "#FF6B6B",
      defaultHourlyRate: 100,
      isActive: true,
    });

    // 创建工作类型
    const saveResult = await workTypeStorage.save(testWorkType.toStorage());
    if (!saveResult.success) {
      throw new Error("创建工作类型失败");
    }

    // 读取工作类型
    const getResult = await workTypeStorage.getById(testWorkType.id);
    if (!getResult.success || !getResult.data) {
      throw new Error("读取工作类型失败");
    }

    // 更新工作类型
    testWorkType.name = "更新后的测试工作类型";
    const updateResult = await workTypeStorage.save(testWorkType.toStorage());
    if (!updateResult.success) {
      throw new Error("更新工作类型失败");
    }

    // 删除工作类型
    const deleteResult = await workTypeStorage.delete(testWorkType.id);
    if (!deleteResult.success) {
      throw new Error("删除工作类型失败");
    }

    return { message: "工作类型管理测试通过" };
  }

  /**
   * 测试数据计算功能
   */
  async testCalculationFunctions() {
    const { SalaryCalculator } = await import("../utils/salary.js");

    // 测试时薪计算
    const hourlyResult = SalaryCalculator.calculateHourlySalary(8, 50);
    if (hourlyResult !== 400) {
      throw new Error("时薪计算错误");
    }

    // 测试工作时长计算
    const duration = SalaryCalculator.calculateDuration("09:00", "17:00");
    if (duration !== 480) {
      // 8小时 = 480分钟
      throw new Error("工作时长计算错误");
    }

    return { message: "数据计算功能测试通过" };
  }

  /**
   * 测试导入导出功能
   */
  async testImportExportFunctions() {
    const { DataExporter } = await import("../utils/data-export.js");
    const { DataImporter } = await import("../utils/data-import.js");

    // 测试数据导出
    const exportResult = await DataExporter.exportAllData();
    if (!exportResult.success) {
      throw new Error("数据导出失败");
    }

    // 测试数据包验证
    const validation = DataImporter.validateDataPackage(exportResult.data);
    if (!validation.isValid) {
      throw new Error("数据包验证失败");
    }

    return { message: "导入导出功能测试通过" };
  }

  /**
   * 测试通知功能
   */
  async testNotificationFunctions() {
    const NotificationManager = await import("../utils/notification.js");

    // 测试通知权限检查
    const hasPermission = await NotificationManager.default.checkPermission();

    // 测试通知调度
    const scheduleResult = NotificationManager.default.scheduleNotification({
      title: "测试通知",
      content: "这是一个测试通知",
      time: Date.now() + 1000,
    });

    return {
      hasPermission,
      scheduleResult,
      message: "通知功能测试通过",
    };
  }

  /**
   * 测试页面加载性能
   */
  async testPageLoadPerformance() {
    const startTime = Date.now();

    // 模拟页面加载
    await new Promise((resolve) => setTimeout(resolve, 100));

    const loadTime = Date.now() - startTime;

    if (loadTime > 3000) {
      throw new Error(`页面加载时间过长: ${loadTime}ms`);
    }

    return { loadTime, message: "页面加载性能测试通过" };
  }

  /**
   * 测试数据处理性能
   */
  async testDataProcessingPerformance() {
    const startTime = Date.now();

    // 模拟大量数据处理
    const testData = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      value: Math.random(),
      timestamp: Date.now(),
    }));

    // 数据排序和过滤
    const processed = testData
      .filter((item) => item.value > 0.5)
      .sort((a, b) => b.value - a.value)
      .slice(0, 100);

    const processTime = Date.now() - startTime;

    if (processTime > 1000) {
      throw new Error(`数据处理时间过长: ${processTime}ms`);
    }

    return {
      processTime,
      processedCount: processed.length,
      message: "数据处理性能测试通过",
    };
  }

  /**
   * 测试内存使用
   */
  async testMemoryUsage() {
    // 创建大量对象测试内存
    const objects = [];
    const initialTime = Date.now();

    try {
      for (let i = 0; i < 10000; i++) {
        objects.push({
          id: i,
          data: new Array(100).fill(Math.random()),
          timestamp: Date.now(),
        });
      }

      // 清理内存
      objects.length = 0;

      const memoryTestTime = Date.now() - initialTime;

      return {
        memoryTestTime,
        objectsCreated: 10000,
        message: "内存使用测试通过",
      };
    } catch (error) {
      throw new Error(`内存测试失败: ${error.message}`);
    }
  }

  /**
   * 测试大数据量
   */
  async testLargeDataSet() {
    const startTime = Date.now();

    // 创建大数据集
    const largeDataSet = Array.from({ length: 5000 }, (_, i) => ({
      id: i,
      workType: `type_${i % 10}`,
      date: new Date(Date.now() - i * 86400000).toISOString().split("T")[0],
      duration: Math.floor(Math.random() * 480) + 60,
      salary: Math.floor(Math.random() * 500) + 50,
    }));

    // 执行复杂查询
    const grouped = largeDataSet.reduce((acc, item) => {
      if (!acc[item.workType]) {
        acc[item.workType] = [];
      }
      acc[item.workType].push(item);
      return acc;
    }, {});

    const totalSalary = largeDataSet.reduce(
      (sum, item) => sum + item.salary,
      0
    );
    const avgSalary = totalSalary / largeDataSet.length;

    const processTime = Date.now() - startTime;

    if (processTime > 2000) {
      throw new Error(`大数据量处理时间过长: ${processTime}ms`);
    }

    return {
      processTime,
      dataCount: largeDataSet.length,
      groupCount: Object.keys(grouped).length,
      avgSalary: Math.round(avgSalary),
      message: "大数据量测试通过",
    };
  }

  /**
   * 测试数据验证
   */
  async testDataValidation() {
    const Validator = await import("../utils/validator.js");

    // 测试工作记录验证
    const validRecord = {
      workTypeId: "test-type",
      date: "2024-01-01",
      startTime: "09:00",
      endTime: "17:00",
      hourlyRate: 50,
    };

    const invalidRecord = {
      workTypeId: "",
      date: "invalid-date",
      startTime: "25:00",
      endTime: "08:00",
      hourlyRate: -10,
    };

    const validResult = Validator.default.validateWorkRecord(validRecord);
    const invalidResult = Validator.default.validateWorkRecord(invalidRecord);

    if (!validResult.isValid) {
      throw new Error("有效数据验证失败");
    }

    if (invalidResult.isValid) {
      throw new Error("无效数据验证应该失败");
    }

    return { message: "数据验证测试通过" };
  }

  /**
   * 测试数据持久化
   */
  async testDataPersistence() {
    const testKey = "persistence_test";
    const testData = {
      id: "test-123",
      name: "持久化测试",
      timestamp: Date.now(),
      nested: {
        value: "nested-data",
        array: [1, 2, 3],
      },
    };

    // 保存数据
    wx.setStorageSync(testKey, testData);

    // 读取数据
    const retrievedData = wx.getStorageSync(testKey);

    // 验证数据完整性
    if (JSON.stringify(testData) !== JSON.stringify(retrievedData)) {
      throw new Error("数据持久化失败，数据不一致");
    }

    // 清理测试数据
    wx.removeStorageSync(testKey);

    return { message: "数据持久化测试通过" };
  }

  /**
   * 测试数据同步
   */
  async testDataSynchronization() {
    // 模拟多个数据源同步
    const source1 = [
      { id: "1", name: "Item 1", version: 1 },
      { id: "2", name: "Item 2", version: 1 },
    ];

    const source2 = [
      { id: "1", name: "Item 1 Updated", version: 2 },
      { id: "3", name: "Item 3", version: 1 },
    ];

    // 模拟同步逻辑
    const merged = [...source1];
    source2.forEach((item) => {
      const existingIndex = merged.findIndex((m) => m.id === item.id);
      if (existingIndex >= 0) {
        if (item.version > merged[existingIndex].version) {
          merged[existingIndex] = item;
        }
      } else {
        merged.push(item);
      }
    });

    // 验证同步结果
    if (merged.length !== 3) {
      throw new Error("数据同步失败，数量不正确");
    }

    const item1 = merged.find((m) => m.id === "1");
    if (item1.name !== "Item 1 Updated" || item1.version !== 2) {
      throw new Error("数据同步失败，版本更新不正确");
    }

    return {
      mergedCount: merged.length,
      message: "数据同步测试通过",
    };
  }

  /**
   * 测试数据恢复
   */
  async testDataRecovery() {
    const backupKey = "backup_test";
    const originalData = {
      workRecords: [
        { id: "1", title: "Work 1" },
        { id: "2", title: "Work 2" },
      ],
      workTypes: [{ id: "1", name: "Type 1" }],
    };

    // 创建备份
    const backup = {
      timestamp: Date.now(),
      version: "1.0.0",
      data: originalData,
    };

    wx.setStorageSync(backupKey, backup);

    // 模拟数据损坏
    wx.setStorageSync("work_records", null);
    wx.setStorageSync("work_types", null);

    // 恢复数据
    const restoredBackup = wx.getStorageSync(backupKey);
    if (!restoredBackup || !restoredBackup.data) {
      throw new Error("备份数据不存在");
    }

    wx.setStorageSync("work_records", restoredBackup.data.workRecords);
    wx.setStorageSync("work_types", restoredBackup.data.workTypes);

    // 验证恢复结果
    const restoredRecords = wx.getStorageSync("work_records");
    const restoredTypes = wx.getStorageSync("work_types");

    if (!restoredRecords || restoredRecords.length !== 2) {
      throw new Error("工作记录恢复失败");
    }

    if (!restoredTypes || restoredTypes.length !== 1) {
      throw new Error("工作类型恢复失败");
    }

    // 清理测试数据
    wx.removeStorageSync(backupKey);
    wx.removeStorageSync("work_records");
    wx.removeStorageSync("work_types");

    return { message: "数据恢复测试通过" };
  }

  /**
   * 测试响应式布局
   */
  async testResponsiveLayout() {
    // 模拟不同屏幕尺寸测试
    const screenSizes = [
      { width: 375, height: 667, name: "iPhone SE" },
      { width: 414, height: 896, name: "iPhone 11" },
      { width: 768, height: 1024, name: "iPad" },
    ];

    const layoutTests = screenSizes.map((size) => {
      // 模拟布局计算
      const containerWidth = size.width - 32; // 减去padding
      const cardWidth = Math.floor(containerWidth / 2) - 8; // 两列布局

      return {
        screenSize: size.name,
        containerWidth,
        cardWidth,
        isValid: cardWidth > 120, // 最小卡片宽度
      };
    });

    const failedTests = layoutTests.filter((test) => !test.isValid);
    if (failedTests.length > 0) {
      throw new Error(
        `响应式布局测试失败: ${failedTests.map((t) => t.screenSize).join(", ")}`
      );
    }

    return {
      testedSizes: layoutTests.length,
      message: "响应式布局测试通过",
    };
  }

  /**
   * 测试主题切换
   */
  async testThemeSwitching() {
    const ThemeManager = await import("../utils/theme-manager.js");

    // 测试主题切换
    const originalTheme = ThemeManager.default.getCurrentTheme();

    // 切换到深色主题
    ThemeManager.default.setTheme("dark");
    const darkTheme = ThemeManager.default.getCurrentTheme();

    if (darkTheme !== "dark") {
      throw new Error("切换到深色主题失败");
    }

    // 切换到浅色主题
    ThemeManager.default.setTheme("light");
    const lightTheme = ThemeManager.default.getCurrentTheme();

    if (lightTheme !== "light") {
      throw new Error("切换到浅色主题失败");
    }

    // 恢复原始主题
    ThemeManager.default.setTheme(originalTheme);

    return { message: "主题切换测试通过" };
  }

  /**
   * 测试交互反馈
   */
  async testInteractionFeedback() {
    // 模拟触觉反馈测试
    const feedbackTests = [];

    try {
      // 测试轻触反馈
      wx.vibrateShort({ type: "light" });
      feedbackTests.push({ type: "light", success: true });
    } catch (error) {
      feedbackTests.push({
        type: "light",
        success: false,
        error: error.message,
      });
    }

    try {
      // 测试中等反馈
      wx.vibrateShort({ type: "medium" });
      feedbackTests.push({ type: "medium", success: true });
    } catch (error) {
      feedbackTests.push({
        type: "medium",
        success: false,
        error: error.message,
      });
    }

    try {
      // 测试重触反馈
      wx.vibrateShort({ type: "heavy" });
      feedbackTests.push({ type: "heavy", success: true });
    } catch (error) {
      feedbackTests.push({
        type: "heavy",
        success: false,
        error: error.message,
      });
    }

    const successCount = feedbackTests.filter((test) => test.success).length;

    return {
      totalTests: feedbackTests.length,
      successCount,
      feedbackTests,
      message: "交互反馈测试通过",
    };
  }

  /**
   * 测试无障碍访问
   */
  async testAccessibility() {
    // 模拟无障碍功能测试
    const accessibilityFeatures = [
      {
        name: "语音朗读支持",
        test: () => {
          // 检查是否有aria-label或其他无障碍属性
          return true; // 模拟通过
        },
      },
      {
        name: "键盘导航支持",
        test: () => {
          // 检查tabindex和键盘事件处理
          return true; // 模拟通过
        },
      },
      {
        name: "高对比度支持",
        test: () => {
          // 检查颜色对比度
          return true; // 模拟通过
        },
      },
      {
        name: "字体大小适配",
        test: () => {
          // 检查字体缩放支持
          return true; // 模拟通过
        },
      },
    ];

    const results = accessibilityFeatures.map((feature) => ({
      name: feature.name,
      passed: feature.test(),
    }));

    const failedFeatures = results.filter((result) => !result.passed);
    if (failedFeatures.length > 0) {
      throw new Error(
        `无障碍访问测试失败: ${failedFeatures.map((f) => f.name).join(", ")}`
      );
    }

    return {
      testedFeatures: results.length,
      passedFeatures: results.filter((r) => r.passed).length,
      message: "无障碍访问测试通过",
    };
  }

  /**
   * 测试空数据处理
   */
  async testEmptyDataHandling() {
    const { workRecordStorage } = await import("../utils/storage.js");

    // 测试空数据查询
    const emptyResult = await workRecordStorage.getAll();
    if (!emptyResult.success) {
      throw new Error("空数据查询失败");
    }

    // 测试空数组处理
    const emptyArray = [];
    const processedEmpty = emptyArray
      .filter((item) => item.active)
      .map((item) => ({ ...item, processed: true }));

    if (processedEmpty.length !== 0) {
      throw new Error("空数组处理错误");
    }

    // 测试null/undefined处理
    const nullData = null;
    const undefinedData = undefined;

    const safeNullProcess = nullData || [];
    const safeUndefinedProcess = undefinedData || [];

    if (safeNullProcess.length !== 0 || safeUndefinedProcess.length !== 0) {
      throw new Error("null/undefined数据处理错误");
    }

    return { message: "空数据处理测试通过" };
  }

  /**
   * 测试异常输入处理
   */
  async testInvalidInputHandling() {
    const Validator = await import("../utils/validator.js");

    // 测试各种异常输入
    const invalidInputs = [
      { input: "", type: "empty string" },
      { input: null, type: "null" },
      { input: undefined, type: "undefined" },
      { input: {}, type: "empty object" },
      { input: [], type: "empty array" },
      { input: "invalid-date", type: "invalid date" },
      { input: -1, type: "negative number" },
      { input: Infinity, type: "infinity" },
      { input: NaN, type: "NaN" },
    ];

    const results = invalidInputs.map((test) => {
      try {
        const result = Validator.default.validateWorkRecord({
          workTypeId: test.input,
          date: test.input,
          startTime: test.input,
          endTime: test.input,
          hourlyRate: test.input,
        });

        return {
          type: test.type,
          input: test.input,
          handled: !result.isValid, // 应该返回无效
          result,
        };
      } catch (error) {
        return {
          type: test.type,
          input: test.input,
          handled: true, // 异常被捕获也算处理了
          error: error.message,
        };
      }
    });

    const unhandledInputs = results.filter((r) => !r.handled);
    if (unhandledInputs.length > 0) {
      throw new Error(
        `未处理的异常输入: ${unhandledInputs.map((r) => r.type).join(", ")}`
      );
    }

    return {
      testedInputs: results.length,
      handledInputs: results.filter((r) => r.handled).length,
      message: "异常输入处理测试通过",
    };
  }

  /**
   * 测试网络异常处理
   */
  async testNetworkErrorHandling() {
    // 模拟网络请求失败
    const networkTests = [
      {
        name: "连接超时",
        simulate: () => Promise.reject(new Error("网络连接超时")),
      },
      {
        name: "服务器错误",
        simulate: () => Promise.reject(new Error("服务器内部错误")),
      },
      {
        name: "网络不可用",
        simulate: () => Promise.reject(new Error("网络不可用")),
      },
    ];

    const results = [];

    for (const test of networkTests) {
      try {
        await test.simulate();
        results.push({
          name: test.name,
          handled: false,
          error: "应该抛出错误但没有",
        });
      } catch (error) {
        // 检查错误是否被正确处理
        const isHandled = error.message && typeof error.message === "string";
        results.push({
          name: test.name,
          handled: isHandled,
          error: error.message,
        });
      }
    }

    const unhandledErrors = results.filter((r) => !r.handled);
    if (unhandledErrors.length > 0) {
      throw new Error(
        `未处理的网络错误: ${unhandledErrors.map((r) => r.name).join(", ")}`
      );
    }

    return {
      testedScenarios: results.length,
      handledScenarios: results.filter((r) => r.handled).length,
      message: "网络异常处理测试通过",
    };
  }

  /**
   * 测试存储限制
   */
  async testStorageLimits() {
    const testKey = "storage_limit_test";

    try {
      // 测试大数据存储
      const largeData = {
        id: "large-test",
        data: new Array(10000).fill(0).map((_, i) => ({
          id: i,
          content: `测试数据${i}`.repeat(10),
          timestamp: Date.now(),
        })),
      };

      // 尝试存储大数据
      wx.setStorageSync(testKey, largeData);

      // 验证存储成功
      const retrieved = wx.getStorageSync(testKey);
      if (!retrieved || retrieved.data.length !== largeData.data.length) {
        throw new Error("大数据存储失败");
      }

      // 清理测试数据
      wx.removeStorageSync(testKey);

      // 测试存储信息
      const storageInfo = wx.getStorageInfoSync();

      return {
        storageInfo,
        largeDataSize: largeData.data.length,
        message: "存储限制测试通过",
      };
    } catch (error) {
      // 清理可能的残留数据
      try {
        wx.removeStorageSync(testKey);
      } catch (cleanupError) {
        // 忽略清理错误
      }

      if (error.message.includes("exceed")) {
        // 存储超限是预期的
        return {
          limitReached: true,
          error: error.message,
          message: "存储限制测试通过（达到限制）",
        };
      } else {
        throw error;
      }
    }
  }

  /**
   * 计算测试总结
   */
  calculateSummary(results) {
    let total = 0,
      passed = 0,
      failed = 0,
      skipped = 0;

    Object.values(results.categories).forEach((category) => {
      total += category.total;
      passed += category.passed;
      failed += category.failed;
      skipped += category.skipped;
    });

    results.summary.total = total;
    results.summary.passed = passed;
    results.summary.failed = failed;
    results.summary.skipped = skipped;
  }

  /**
   * 打印测试报告
   */
  printTestReport(results) {
    console.log("\n📊 测试报告");
    console.log("=".repeat(50));
    console.log(`总计: ${results.summary.total}`);
    console.log(`通过: ${results.summary.passed}`);
    console.log(`失败: ${results.summary.failed}`);
    console.log(`跳过: ${results.summary.skipped}`);
    console.log(`耗时: ${results.summary.duration}ms`);
    console.log(
      `成功率: ${(
        (results.summary.passed / results.summary.total) *
        100
      ).toFixed(2)}%`
    );

    console.log("\n📋 分类详情:");
    Object.entries(results.categories).forEach(([name, category]) => {
      console.log(`${name}: ${category.passed}/${category.total} 通过`);
    });

    // 显示失败的测试
    const failedTests = [];
    Object.values(results.categories).forEach((category) => {
      category.tests.forEach((test) => {
        if (test.status === "failed") {
          failedTests.push(test);
        }
      });
    });

    if (failedTests.length > 0) {
      console.log("\n❌ 失败的测试:");
      failedTests.forEach((test) => {
        console.log(`- ${test.name}: ${test.error}`);
      });
    }
  }

  /**
   * 生成测试报告
   */
  generateReport(results) {
    return {
      timestamp: Date.now(),
      deviceInfo: deviceCompatibility.getDeviceInfo(),
      testResults: results,
      environment: {
        platform: wx.getSystemInfoSync().platform,
        version: wx.getSystemInfoSync().version,
        SDKVersion: wx.getSystemInfoSync().SDKVersion,
      },
    };
  }
}

// 创建全局实例
const testSuite = new TestSuite();

export { TestSuite, testSuite };
