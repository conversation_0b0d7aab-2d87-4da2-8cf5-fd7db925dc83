/**
 * 公共工具类
 * 提取项目中重复使用的通用方法
 */

import CommonErrorHandler from './common-error-handler.js';
import { DateUtils } from './date.js';

/**
 * 通用工具类
 */
class CommonUtils {
  /**
   * 格式化货币显示
   * @param {number} amount - 金额
   * @param {string} currency - 货币符号
   * @returns {string} 格式化后的货币字符串
   */
  static formatCurrency(amount, currency = '¥') {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return `${currency}0.00`;
    }
    return `${currency}${amount.toFixed(2)}`;
  }

  /**
   * 格式化时长显示
   * @param {number} minutes - 分钟数
   * @returns {string} 格式化后的时长字符串
   */
  static formatDuration(minutes) {
    if (typeof minutes !== 'number' || isNaN(minutes) || minutes < 0) {
      return '0小时0分钟';
    }
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours === 0) {
      return `${mins}分钟`;
    } else if (mins === 0) {
      return `${hours}小时`;
    } else {
      return `${hours}小时${mins}分钟`;
    }
  }

  /**
   * 格式化日期显示
   * @param {string|Date} date - 日期
   * @param {string} format - 格式
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date, format = 'YYYY-MM-DD') {
    try {
      return DateUtils.format(date, format);
    } catch (error) {
      CommonErrorHandler.handleAsync(() => {
        throw error;
      }, { context: '日期格式化', showToast: false });
      return '无效日期';
    }
  }

  /**
   * 深拷贝对象
   * @param {any} obj - 要拷贝的对象
   * @returns {any} 拷贝后的对象
   */
  static deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }
    
    if (typeof obj === 'object') {
      const cloned = {};
      Object.keys(obj).forEach(key => {
        cloned[key] = this.deepClone(obj[key]);
      });
      return cloned;
    }
    
    return obj;
  }

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖后的函数
   */
  static debounce(func, delay = 300) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 节流后的函数
   */
  static throttle(func, delay = 300) {
    let lastCall = 0;
    return function (...args) {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        return func.apply(this, args);
      }
    };
  }

  /**
   * 生成唯一ID
   * @param {string} prefix - 前缀
   * @returns {string} 唯一ID
   */
  static generateId(prefix = 'id') {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * 数组分组
   * @param {Array} array - 要分组的数组
   * @param {string|Function} key - 分组键或分组函数
   * @returns {Object} 分组后的对象
   */
  static groupBy(array, key) {
    if (!Array.isArray(array)) {
      return {};
    }
    
    return array.reduce((groups, item) => {
      const groupKey = typeof key === 'function' ? key(item) : item[key];
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {});
  }

  /**
   * 数组去重
   * @param {Array} array - 要去重的数组
   * @param {string} key - 去重键（可选）
   * @returns {Array} 去重后的数组
   */
  static unique(array, key = null) {
    if (!Array.isArray(array)) {
      return [];
    }
    
    if (key) {
      const seen = new Set();
      return array.filter(item => {
        const keyValue = item[key];
        if (seen.has(keyValue)) {
          return false;
        }
        seen.add(keyValue);
        return true;
      });
    } else {
      return [...new Set(array)];
    }
  }

  /**
   * 数组排序
   * @param {Array} array - 要排序的数组
   * @param {string|Function} key - 排序键或排序函数
   * @param {string} order - 排序顺序 'asc' | 'desc'
   * @returns {Array} 排序后的数组
   */
  static sortBy(array, key, order = 'asc') {
    if (!Array.isArray(array)) {
      return [];
    }
    
    const sorted = [...array].sort((a, b) => {
      let aValue, bValue;
      
      if (typeof key === 'function') {
        aValue = key(a);
        bValue = key(b);
      } else {
        aValue = a[key];
        bValue = b[key];
      }
      
      if (aValue < bValue) {
        return order === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return order === 'asc' ? 1 : -1;
      }
      return 0;
    });
    
    return sorted;
  }

  /**
   * 对象合并
   * @param {Object} target - 目标对象
   * @param {...Object} sources - 源对象
   * @returns {Object} 合并后的对象
   */
  static merge(target, ...sources) {
    if (!target || typeof target !== 'object') {
      target = {};
    }
    
    sources.forEach(source => {
      if (source && typeof source === 'object') {
        Object.keys(source).forEach(key => {
          if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            target[key] = this.merge(target[key] || {}, source[key]);
          } else {
            target[key] = source[key];
          }
        });
      }
    });
    
    return target;
  }

  /**
   * 检查对象是否为空
   * @param {any} obj - 要检查的对象
   * @returns {boolean} 是否为空
   */
  static isEmpty(obj) {
    if (obj === null || obj === undefined) {
      return true;
    }
    
    if (typeof obj === 'string' || Array.isArray(obj)) {
      return obj.length === 0;
    }
    
    if (typeof obj === 'object') {
      return Object.keys(obj).length === 0;
    }
    
    return false;
  }

  /**
   * 安全的JSON解析
   * @param {string} jsonString - JSON字符串
   * @param {any} defaultValue - 默认值
   * @returns {any} 解析结果
   */
  static safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      CommonErrorHandler.handleAsync(() => {
        throw error;
      }, { context: 'JSON解析', showToast: false });
      return defaultValue;
    }
  }

  /**
   * 安全的JSON字符串化
   * @param {any} obj - 要字符串化的对象
   * @param {string} defaultValue - 默认值
   * @returns {string} JSON字符串
   */
  static safeJsonStringify(obj, defaultValue = '{}') {
    try {
      return JSON.stringify(obj);
    } catch (error) {
      CommonErrorHandler.handleAsync(() => {
        throw error;
      }, { context: 'JSON字符串化', showToast: false });
      return defaultValue;
    }
  }

  /**
   * 获取嵌套对象属性
   * @param {Object} obj - 对象
   * @param {string} path - 属性路径，如 'a.b.c'
   * @param {any} defaultValue - 默认值
   * @returns {any} 属性值
   */
  static getNestedProperty(obj, path, defaultValue = undefined) {
    if (!obj || typeof obj !== 'object' || !path) {
      return defaultValue;
    }
    
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current === null || current === undefined || !(key in current)) {
        return defaultValue;
      }
      current = current[key];
    }
    
    return current;
  }

  /**
   * 设置嵌套对象属性
   * @param {Object} obj - 对象
   * @param {string} path - 属性路径，如 'a.b.c'
   * @param {any} value - 要设置的值
   * @returns {Object} 修改后的对象
   */
  static setNestedProperty(obj, path, value) {
    if (!obj || typeof obj !== 'object' || !path) {
      return obj;
    }
    
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
    return obj;
  }
}

export default CommonUtils;
