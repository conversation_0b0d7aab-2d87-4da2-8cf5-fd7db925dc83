// utils/validator.js
// 数据验证工具类

import { VALIDATION_RULES, SALARY_TYPES, WORK_RECORD_STATUS } from '../data/constants.js'
import DateUtils from './date.js'

/**
 * 数据验证工具类
 */
export class Validator {
  /**
   * 验证工作记录数据
   */
  static validateWorkRecord(record) {
    const errors = []

    // 验证工作类型
    if (!record.workTypeId) {
      errors.push('请选择工作类型')
    }

    // 验证标题
    if (record.title && record.title.length > VALIDATION_RULES.WORK_RECORD.TITLE_MAX_LENGTH) {
      errors.push(`标题不能超过${VALIDATION_RULES.WORK_RECORD.TITLE_MAX_LENGTH}个字符`)
    }

    // 验证描述
    if (record.description && record.description.length > VALIDATION_RULES.WORK_RECORD.DESCRIPTION_MAX_LENGTH) {
      errors.push(`描述不能超过${VALIDATION_RULES.WORK_RECORD.DESCRIPTION_MAX_LENGTH}个字符`)
    }

    // 验证日期
    if (!record.date) {
      errors.push('请选择工作日期')
    } else if (!DateUtils.isValidDate(record.date)) {
      errors.push('日期格式不正确')
    }

    // 验证时间
    if (!record.startTime) {
      errors.push('请设置开始时间')
    } else if (!DateUtils.isValidTime(record.startTime)) {
      errors.push('开始时间格式不正确')
    }

    if (!record.endTime) {
      errors.push('请设置结束时间')
    } else if (!DateUtils.isValidTime(record.endTime)) {
      errors.push('结束时间格式不正确')
    }

    // 验证时间逻辑
    if (record.startTime && record.endTime && DateUtils.isValidTime(record.startTime) && DateUtils.isValidTime(record.endTime)) {
      const duration = DateUtils.getMinutesDiff(record.startTime, record.endTime)
      
      if (duration <= 0) {
        errors.push('结束时间必须晚于开始时间')
      } else if (duration < VALIDATION_RULES.WORK_RECORD.MIN_DURATION_MINUTES) {
        errors.push(`工作时长不能少于${VALIDATION_RULES.WORK_RECORD.MIN_DURATION_MINUTES}分钟`)
      } else if (duration > VALIDATION_RULES.WORK_RECORD.MAX_DURATION_HOURS * 60) {
        errors.push(`工作时长不能超过${VALIDATION_RULES.WORK_RECORD.MAX_DURATION_HOURS}小时`)
      }
    }

    // 验证薪资类型
    if (record.salaryType && !Object.values(SALARY_TYPES).includes(record.salaryType)) {
      errors.push('薪资类型不正确')
    }

    // 验证薪资金额
    if (record.hourlyRate !== undefined) {
      if (record.hourlyRate < VALIDATION_RULES.WORK_RECORD.MIN_SALARY) {
        errors.push('薪资不能小于0')
      } else if (record.hourlyRate > VALIDATION_RULES.WORK_RECORD.MAX_SALARY) {
        errors.push(`薪资金额不能超过${VALIDATION_RULES.WORK_RECORD.MAX_SALARY}`)
      }
    }

    // 验证状态
    if (record.status && !Object.values(WORK_RECORD_STATUS).includes(record.status)) {
      errors.push('工作状态不正确')
    }

    // 验证多日工作
    if (record.isMultiDay) {
      if (!record.endDate) {
        errors.push('多日工作请设置结束日期')
      } else if (!DateUtils.isValidDate(record.endDate)) {
        errors.push('结束日期格式不正确')
      } else if (record.date && record.endDate && record.date >= record.endDate) {
        errors.push('结束日期必须晚于开始日期')
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证工作类型数据
   */
  static validateWorkType(workType) {
    const errors = []

    // 验证名称
    if (!workType.name || workType.name.trim().length === 0) {
      errors.push('工作类型名称不能为空')
    } else if (workType.name.length < VALIDATION_RULES.WORK_TYPE.NAME_MIN_LENGTH) {
      errors.push(`工作类型名称不能少于${VALIDATION_RULES.WORK_TYPE.NAME_MIN_LENGTH}个字符`)
    } else if (workType.name.length > VALIDATION_RULES.WORK_TYPE.NAME_MAX_LENGTH) {
      errors.push(`工作类型名称不能超过${VALIDATION_RULES.WORK_TYPE.NAME_MAX_LENGTH}个字符`)
    }

    // 验证颜色
    if (workType.color && !this.isValidColor(workType.color)) {
      errors.push('颜色格式不正确，请使用十六进制格式（如：#FF0000）')
    }

    // 验证默认时薪
    if (workType.defaultHourlyRate !== undefined) {
      if (workType.defaultHourlyRate < VALIDATION_RULES.WORK_TYPE.MIN_HOURLY_RATE) {
        errors.push('默认时薪不能小于0')
      } else if (workType.defaultHourlyRate > VALIDATION_RULES.WORK_TYPE.MAX_HOURLY_RATE) {
        errors.push(`默认时薪不能超过${VALIDATION_RULES.WORK_TYPE.MAX_HOURLY_RATE}`)
      }
    }

    // 验证薪资类型
    if (workType.defaultSalaryType && !Object.values(SALARY_TYPES).includes(workType.defaultSalaryType)) {
      errors.push('默认薪资类型不正确')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证颜色格式
   */
  static isValidColor(color) {
    // 验证十六进制颜色格式
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    return hexColorRegex.test(color)
  }

  /**
   * 验证邮箱格式
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 验证手机号格式
   */
  static isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 验证身份证号格式
   */
  static isValidIdCard(idCard) {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }

  /**
   * 验证数字范围
   */
  static isInRange(value, min, max) {
    const num = parseFloat(value)
    return !isNaN(num) && num >= min && num <= max
  }

  /**
   * 验证字符串长度
   */
  static isValidLength(str, minLength, maxLength) {
    if (typeof str !== 'string') return false
    return str.length >= minLength && str.length <= maxLength
  }

  /**
   * 验证必填字段
   */
  static isRequired(value) {
    if (value === null || value === undefined) return false
    if (typeof value === 'string') return value.trim().length > 0
    if (Array.isArray(value)) return value.length > 0
    return true
  }

  /**
   * 验证数组
   */
  static isValidArray(arr, minLength = 0, maxLength = Infinity) {
    if (!Array.isArray(arr)) return false
    return arr.length >= minLength && arr.length <= maxLength
  }

  /**
   * 验证对象
   */
  static isValidObject(obj, requiredKeys = []) {
    if (typeof obj !== 'object' || obj === null) return false
    
    for (const key of requiredKeys) {
      if (!(key in obj)) return false
    }
    
    return true
  }

  /**
   * 验证URL格式
   */
  static isValidUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  /**
   * 验证正整数
   */
  static isPositiveInteger(value) {
    const num = parseInt(value)
    return !isNaN(num) && num > 0 && num === parseFloat(value)
  }

  /**
   * 验证非负数
   */
  static isNonNegativeNumber(value) {
    const num = parseFloat(value)
    return !isNaN(num) && num >= 0
  }

  /**
   * 清理和验证输入数据
   */
  static sanitizeInput(input, type = 'string') {
    if (input === null || input === undefined) return ''
    
    switch (type) {
      case 'string':
        return String(input).trim()
      case 'number':
        const num = parseFloat(input)
        return isNaN(num) ? 0 : num
      case 'integer':
        const int = parseInt(input)
        return isNaN(int) ? 0 : int
      case 'boolean':
        return Boolean(input)
      case 'array':
        return Array.isArray(input) ? input : []
      case 'object':
        return typeof input === 'object' && input !== null ? input : {}
      default:
        return input
    }
  }

  /**
   * 批量验证数据
   */
  static validateBatch(data, rules) {
    const errors = {}
    let isValid = true

    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = data[field]
      const fieldErrors = []

      for (const rule of fieldRules) {
        const result = this.applyRule(value, rule)
        if (!result.isValid) {
          fieldErrors.push(result.message)
          isValid = false
        }
      }

      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors
      }
    }

    return {
      isValid,
      errors
    }
  }

  /**
   * 应用验证规则
   */
  static applyRule(value, rule) {
    switch (rule.type) {
      case 'required':
        return {
          isValid: this.isRequired(value),
          message: rule.message || '此字段为必填项'
        }
      case 'length':
        return {
          isValid: this.isValidLength(value, rule.min || 0, rule.max || Infinity),
          message: rule.message || `长度必须在${rule.min || 0}-${rule.max || '无限'}之间`
        }
      case 'range':
        return {
          isValid: this.isInRange(value, rule.min, rule.max),
          message: rule.message || `值必须在${rule.min}-${rule.max}之间`
        }
      case 'email':
        return {
          isValid: this.isValidEmail(value),
          message: rule.message || '邮箱格式不正确'
        }
      case 'phone':
        return {
          isValid: this.isValidPhone(value),
          message: rule.message || '手机号格式不正确'
        }
      case 'date':
        return {
          isValid: DateUtils.isValidDate(value),
          message: rule.message || '日期格式不正确'
        }
      case 'time':
        return {
          isValid: DateUtils.isValidTime(value),
          message: rule.message || '时间格式不正确'
        }
      case 'color':
        return {
          isValid: this.isValidColor(value),
          message: rule.message || '颜色格式不正确'
        }
      case 'custom':
        return rule.validator(value)
      default:
        return { isValid: true, message: '' }
    }
  }
}

// 导出默认实例
export default Validator
