// utils/salary.js
// 薪资计算工具类

import { SALARY_TYPES } from '../data/constants.js'
import DateUtils from './date.js'

/**
 * 薪资计算工具类
 */
export class SalaryCalculator {
  /**
   * 计算时薪工作的薪资
   */
  static calculateHourlySalary(hourlyRate, durationMinutes) {
    if (hourlyRate <= 0 || durationMinutes <= 0) return 0
    
    const hours = durationMinutes / 60
    return Math.round(hourlyRate * hours * 100) / 100
  }

  /**
   * 计算日薪工作的薪资
   */
  static calculateDailySalary(dailyRate, workDays = 1) {
    if (dailyRate <= 0 || workDays <= 0) return 0
    
    return Math.round(dailyRate * workDays * 100) / 100
  }

  /**
   * 计算项目薪资
   */
  static calculateProjectSalary(projectRate, completionRate = 1) {
    if (projectRate <= 0 || completionRate <= 0) return 0
    
    return Math.round(projectRate * completionRate * 100) / 100
  }

  /**
   * 计算计件薪资
   */
  static calculatePieceSalary(pieceRate, quantity) {
    if (pieceRate <= 0 || quantity <= 0) return 0
    
    return Math.round(pieceRate * quantity * 100) / 100
  }

  /**
   * 根据薪资类型计算薪资
   */
  static calculateSalary(salaryType, rate, duration, quantity = 1) {
    switch (salaryType) {
      case SALARY_TYPES.HOURLY:
        return this.calculateHourlySalary(rate, duration)
      case SALARY_TYPES.DAILY:
        return this.calculateDailySalary(rate, quantity)
      case SALARY_TYPES.PROJECT:
        return this.calculateProjectSalary(rate, quantity)
      case SALARY_TYPES.PIECE:
        return this.calculatePieceSalary(rate, quantity)
      default:
        return 0
    }
  }

  /**
   * 计算个人所得税（简化版）
   */
  static calculateIncomeTax(income, monthlyIncome = 0) {
    // 简化的个人所得税计算，实际应根据当地税法
    const totalIncome = income + monthlyIncome
    const threshold = 5000 // 起征点
    
    if (totalIncome <= threshold) return 0
    
    const taxableIncome = totalIncome - threshold
    let tax = 0
    
    // 累进税率计算
    if (taxableIncome <= 3000) {
      tax = taxableIncome * 0.03
    } else if (taxableIncome <= 12000) {
      tax = 3000 * 0.03 + (taxableIncome - 3000) * 0.1
    } else if (taxableIncome <= 25000) {
      tax = 3000 * 0.03 + 9000 * 0.1 + (taxableIncome - 12000) * 0.2
    } else {
      tax = 3000 * 0.03 + 9000 * 0.1 + 13000 * 0.2 + (taxableIncome - 25000) * 0.25
    }
    
    // 计算当前收入应承担的税额比例
    const currentTaxRatio = income / totalIncome
    return Math.round(tax * currentTaxRatio * 100) / 100
  }

  /**
   * 计算社保费用（简化版）
   */
  static calculateSocialInsurance(income, rate = 0.105) {
    // 简化的社保计算，实际应根据当地政策
    return Math.round(income * rate * 100) / 100
  }

  /**
   * 计算税后薪资
   */
  static calculateAfterTaxSalary(grossSalary, monthlyIncome = 0, includeSocialInsurance = false) {
    let afterTaxSalary = grossSalary
    
    // 扣除个人所得税
    const incomeTax = this.calculateIncomeTax(grossSalary, monthlyIncome)
    afterTaxSalary -= incomeTax
    
    // 扣除社保（如果适用）
    if (includeSocialInsurance) {
      const socialInsurance = this.calculateSocialInsurance(grossSalary)
      afterTaxSalary -= socialInsurance
    }
    
    return {
      grossSalary: Math.round(grossSalary * 100) / 100,
      incomeTax: incomeTax,
      socialInsurance: includeSocialInsurance ? this.calculateSocialInsurance(grossSalary) : 0,
      afterTaxSalary: Math.round(Math.max(0, afterTaxSalary) * 100) / 100
    }
  }

  /**
   * 计算平均时薪
   */
  static calculateAverageHourlyRate(totalSalary, totalMinutes) {
    if (totalMinutes <= 0) return 0
    
    const totalHours = totalMinutes / 60
    return Math.round((totalSalary / totalHours) * 100) / 100
  }

  /**
   * 计算工作效率（每小时收入）
   */
  static calculateEfficiency(records) {
    if (!records || records.length === 0) return 0
    
    const totalSalary = records.reduce((sum, record) => sum + (record.totalSalary || 0), 0)
    const totalMinutes = records.reduce((sum, record) => sum + (record.duration || 0), 0)
    
    return this.calculateAverageHourlyRate(totalSalary, totalMinutes)
  }

  /**
   * 计算收入统计
   */
  static calculateIncomeStats(records) {
    if (!records || records.length === 0) {
      return {
        totalIncome: 0,
        totalHours: 0,
        totalDays: 0,
        averageHourlyRate: 0,
        averageDailyIncome: 0,
        recordCount: 0
      }
    }

    const totalIncome = records.reduce((sum, record) => sum + (record.totalSalary || 0), 0)
    const totalMinutes = records.reduce((sum, record) => sum + (record.duration || 0), 0)
    const totalHours = totalMinutes / 60
    
    // 计算工作天数（去重日期）
    const uniqueDates = new Set(records.map(record => record.date))
    const totalDays = uniqueDates.size
    
    const averageHourlyRate = this.calculateAverageHourlyRate(totalIncome, totalMinutes)
    const averageDailyIncome = totalDays > 0 ? totalIncome / totalDays : 0

    return {
      totalIncome: Math.round(totalIncome * 100) / 100,
      totalHours: Math.round(totalHours * 100) / 100,
      totalDays,
      averageHourlyRate,
      averageDailyIncome: Math.round(averageDailyIncome * 100) / 100,
      recordCount: records.length
    }
  }

  /**
   * 按工作类型统计收入
   */
  static calculateIncomeByWorkType(records, workTypes) {
    const stats = {}
    
    // 初始化统计数据
    workTypes.forEach(workType => {
      stats[workType.id] = {
        workType,
        totalIncome: 0,
        totalHours: 0,
        totalDays: 0,
        recordCount: 0,
        averageHourlyRate: 0,
        percentage: 0
      }
    })

    // 计算各工作类型的统计数据
    records.forEach(record => {
      const workTypeId = record.workTypeId
      if (stats[workTypeId]) {
        stats[workTypeId].totalIncome += record.totalSalary || 0
        stats[workTypeId].totalHours += (record.duration || 0) / 60
        stats[workTypeId].recordCount += 1
        
        // 统计工作天数
        if (!stats[workTypeId].dates) {
          stats[workTypeId].dates = new Set()
        }
        stats[workTypeId].dates.add(record.date)
      }
    })

    // 计算总收入用于百分比计算
    const totalIncome = Object.values(stats).reduce((sum, stat) => sum + stat.totalIncome, 0)

    // 完善统计数据
    Object.values(stats).forEach(stat => {
      stat.totalDays = stat.dates ? stat.dates.size : 0
      stat.averageHourlyRate = this.calculateAverageHourlyRate(stat.totalIncome, stat.totalHours * 60)
      stat.percentage = totalIncome > 0 ? (stat.totalIncome / totalIncome * 100) : 0
      
      // 清理临时数据
      delete stat.dates
      
      // 保留两位小数
      stat.totalIncome = Math.round(stat.totalIncome * 100) / 100
      stat.totalHours = Math.round(stat.totalHours * 100) / 100
      stat.percentage = Math.round(stat.percentage * 100) / 100
    })

    return stats
  }

  /**
   * 按时间段统计收入
   */
  static calculateIncomeByPeriod(records, period = 'month') {
    const stats = {}
    
    records.forEach(record => {
      let key
      const date = DateUtils.parseDate(record.date)
      
      if (!date) return
      
      switch (period) {
        case 'week':
          key = DateUtils.getWeekStart(date)
          break
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
          break
        case 'quarter':
          const quarter = Math.floor(date.getMonth() / 3) + 1
          key = `${date.getFullYear()}-Q${quarter}`
          break
        case 'year':
          key = date.getFullYear().toString()
          break
        default:
          key = record.date
      }
      
      if (!stats[key]) {
        stats[key] = {
          period: key,
          totalIncome: 0,
          totalHours: 0,
          totalDays: 0,
          recordCount: 0,
          dates: new Set()
        }
      }
      
      stats[key].totalIncome += record.totalSalary || 0
      stats[key].totalHours += (record.duration || 0) / 60
      stats[key].recordCount += 1
      stats[key].dates.add(record.date)
    })

    // 完善统计数据
    Object.values(stats).forEach(stat => {
      stat.totalDays = stat.dates.size
      stat.averageHourlyRate = this.calculateAverageHourlyRate(stat.totalIncome, stat.totalHours * 60)
      stat.averageDailyIncome = stat.totalDays > 0 ? stat.totalIncome / stat.totalDays : 0
      
      // 清理临时数据
      delete stat.dates
      
      // 保留两位小数
      stat.totalIncome = Math.round(stat.totalIncome * 100) / 100
      stat.totalHours = Math.round(stat.totalHours * 100) / 100
      stat.averageDailyIncome = Math.round(stat.averageDailyIncome * 100) / 100
    })

    return stats
  }

  /**
   * 计算收入趋势
   */
  static calculateIncomeTrend(records, period = 'month', months = 6) {
    const now = new Date()
    const trends = []
    
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      let key, label
      
      switch (period) {
        case 'week':
          const weekStart = DateUtils.getWeekStart(date)
          key = weekStart
          label = `${DateUtils.formatDate(date).substring(5)}`
          break
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
          label = `${date.getMonth() + 1}月`
          break
        default:
          key = DateUtils.formatDate(date)
          label = DateUtils.formatDate(date)
      }
      
      const periodRecords = records.filter(record => {
        const recordDate = DateUtils.parseDate(record.date)
        if (!recordDate) return false
        
        switch (period) {
          case 'week':
            return DateUtils.getWeekStart(recordDate) === key
          case 'month':
            const recordKey = `${recordDate.getFullYear()}-${String(recordDate.getMonth() + 1).padStart(2, '0')}`
            return recordKey === key
          default:
            return record.date === key
        }
      })
      
      const stats = this.calculateIncomeStats(periodRecords)
      
      trends.push({
        period: key,
        label,
        ...stats
      })
    }
    
    return trends
  }

  /**
   * 格式化金额显示
   */
  static formatMoney(amount, currency = '¥') {
    if (amount === null || amount === undefined) return `${currency}0.00`
    
    const formattedAmount = Math.abs(amount).toFixed(2)
    const parts = formattedAmount.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    
    const sign = amount < 0 ? '-' : ''
    return `${sign}${currency}${parts.join('.')}`
  }

  /**
   * 计算薪资增长率
   */
  static calculateGrowthRate(currentPeriod, previousPeriod) {
    if (!previousPeriod || previousPeriod === 0) return 0
    
    const growthRate = ((currentPeriod - previousPeriod) / previousPeriod) * 100
    return Math.round(growthRate * 100) / 100
  }

  /**
   * 预测收入
   */
  static predictIncome(historicalData, targetPeriod = 'month') {
    if (!historicalData || historicalData.length < 2) return null
    
    // 简单的线性预测
    const trends = historicalData.slice(-6) // 取最近6个周期的数据
    const avgGrowth = trends.reduce((sum, trend, index) => {
      if (index === 0) return 0
      const growth = this.calculateGrowthRate(trend.totalIncome, trends[index - 1].totalIncome)
      return sum + growth
    }, 0) / (trends.length - 1)
    
    const lastIncome = trends[trends.length - 1].totalIncome
    const predictedIncome = lastIncome * (1 + avgGrowth / 100)
    
    return {
      predictedIncome: Math.round(predictedIncome * 100) / 100,
      growthRate: Math.round(avgGrowth * 100) / 100,
      confidence: Math.min(trends.length / 6, 1) // 置信度基于数据量
    }
  }
}

// 导出默认实例
export default SalaryCalculator
