// utils/data-import.js
// 数据导入工具类

import { WorkRecord } from '../data/work-record.js'
import { WorkType } from '../data/work-type.js'
import { workRecordStorage, workTypeStorage } from './storage.js'
import Validator from './validator.js'

/**
 * 数据导入工具类
 */
class DataImporter {
  
  /**
   * 导入完整数据包
   */
  static async importFullData(dataPackage, options = {}) {
    const { 
      overwrite = false, 
      validateData = true,
      importUserInfo = true,
      importSettings = true 
    } = options
    
    try {
      // 验证数据包格式
      const validation = this.validateDataPackage(dataPackage)
      if (!validation.isValid) {
        return {
          success: false,
          error: '数据包格式无效',
          details: validation.errors
        }
      }
      
      const results = {
        workTypes: { imported: 0, skipped: 0, errors: 0 },
        workRecords: { imported: 0, skipped: 0, errors: 0 },
        userInfo: false,
        settings: false
      }
      
      // 导入工作类型
      if (dataPackage.data.workTypes && dataPackage.data.workTypes.length > 0) {
        const workTypeResult = await this.importWorkTypes(
          dataPackage.data.workTypes, 
          { overwrite, validateData }
        )
        results.workTypes = workTypeResult
      }
      
      // 导入工作记录
      if (dataPackage.data.workRecords && dataPackage.data.workRecords.length > 0) {
        const recordResult = await this.importWorkRecords(
          dataPackage.data.workRecords, 
          { overwrite, validateData }
        )
        results.workRecords = recordResult
      }
      
      // 导入用户信息
      if (importUserInfo && dataPackage.data.userInfo) {
        results.userInfo = this.importUserInfo(dataPackage.data.userInfo)
      }
      
      // 导入应用设置
      if (importSettings && dataPackage.data.settings) {
        results.settings = this.importAppSettings(dataPackage.data.settings)
      }
      
      return {
        success: true,
        results,
        summary: this.generateImportSummary(results)
      }
    } catch (error) {
      console.error('导入数据失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 导入工作类型
   */
  static async importWorkTypes(workTypes, options = {}) {
    const { overwrite = false, validateData = true } = options
    const results = { imported: 0, skipped: 0, errors: 0, errorDetails: [] }
    
    try {
      // 获取现有工作类型
      const existingResult = await workTypeStorage.getAll()
      const existingTypes = existingResult.success ? existingResult.data : []
      const existingMap = new Map(existingTypes.map(wt => [wt.name, wt]))
      
      for (const typeData of workTypes) {
        try {
          // 数据验证
          if (validateData) {
            const workType = new WorkType(typeData)
            const validation = workType.validate()
            if (!validation.isValid) {
              results.errors++
              results.errorDetails.push({
                item: typeData.name || 'Unknown',
                error: validation.errors.join(', ')
              })
              continue
            }
          }
          
          // 检查是否已存在
          const existing = existingMap.get(typeData.name)
          if (existing && !overwrite) {
            results.skipped++
            continue
          }
          
          // 创建或更新工作类型
          const workType = new WorkType({
            ...typeData,
            id: existing ? existing.id : undefined,
            updatedAt: Date.now()
          })
          
          const saveResult = await workTypeStorage.save(workType.toStorage())
          if (saveResult.success) {
            results.imported++
          } else {
            results.errors++
            results.errorDetails.push({
              item: typeData.name,
              error: '保存失败'
            })
          }
        } catch (error) {
          results.errors++
          results.errorDetails.push({
            item: typeData.name || 'Unknown',
            error: error.message
          })
        }
      }
    } catch (error) {
      console.error('导入工作类型失败:', error)
      throw error
    }
    
    return results
  }
  
  /**
   * 导入工作记录
   */
  static async importWorkRecords(workRecords, options = {}) {
    const { overwrite = false, validateData = true } = options
    const results = { imported: 0, skipped: 0, errors: 0, errorDetails: [] }
    
    try {
      // 获取现有工作记录
      const existingResult = await workRecordStorage.getAll()
      const existingRecords = existingResult.success ? existingResult.data : []
      const existingMap = new Map(existingRecords.map(wr => [
        `${wr.date}-${wr.startTime}-${wr.endTime}-${wr.workTypeId}`, wr
      ]))
      
      // 获取工作类型映射
      const workTypesResult = await workTypeStorage.getAll()
      const workTypes = workTypesResult.success ? workTypesResult.data : []
      const workTypeNameMap = new Map(workTypes.map(wt => [wt.name, wt.id]))
      
      for (const recordData of workRecords) {
        try {
          // 处理工作类型ID映射
          if (recordData.workTypeName && !recordData.workTypeId) {
            recordData.workTypeId = workTypeNameMap.get(recordData.workTypeName)
          }
          
          // 数据验证
          if (validateData) {
            const workRecord = new WorkRecord(recordData)
            const validation = workRecord.validate()
            if (!validation.isValid) {
              results.errors++
              results.errorDetails.push({
                item: `${recordData.date} ${recordData.startTime}-${recordData.endTime}`,
                error: validation.errors.join(', ')
              })
              continue
            }
          }
          
          // 检查是否已存在
          const key = `${recordData.date}-${recordData.startTime}-${recordData.endTime}-${recordData.workTypeId}`
          const existing = existingMap.get(key)
          if (existing && !overwrite) {
            results.skipped++
            continue
          }
          
          // 创建或更新工作记录
          const workRecord = new WorkRecord({
            ...recordData,
            id: existing ? existing.id : undefined,
            updatedAt: Date.now()
          })
          
          const saveResult = await workRecordStorage.save(workRecord.toStorage())
          if (saveResult.success) {
            results.imported++
          } else {
            results.errors++
            results.errorDetails.push({
              item: `${recordData.date} ${recordData.startTime}-${recordData.endTime}`,
              error: '保存失败'
            })
          }
        } catch (error) {
          results.errors++
          results.errorDetails.push({
            item: `${recordData.date || 'Unknown'} ${recordData.startTime || ''}-${recordData.endTime || ''}`,
            error: error.message
          })
        }
      }
    } catch (error) {
      console.error('导入工作记录失败:', error)
      throw error
    }
    
    return results
  }
  
  /**
   * 导入用户信息
   */
  static importUserInfo(userInfo) {
    try {
      // 验证用户信息格式
      if (!userInfo || typeof userInfo !== 'object') {
        return false
      }
      
      // 合并现有用户信息
      const existingUserInfo = wx.getStorageSync('userInfo') || {}
      const mergedUserInfo = {
        ...existingUserInfo,
        ...userInfo,
        updatedAt: Date.now()
      }
      
      wx.setStorageSync('userInfo', mergedUserInfo)
      return true
    } catch (error) {
      console.error('导入用户信息失败:', error)
      return false
    }
  }
  
  /**
   * 导入应用设置
   */
  static importAppSettings(settings) {
    try {
      // 验证设置格式
      if (!settings || typeof settings !== 'object') {
        return false
      }
      
      // 合并现有设置
      const existingSettings = wx.getStorageSync('appSettings') || {}
      const mergedSettings = {
        ...existingSettings,
        ...settings,
        updatedAt: Date.now()
      }
      
      wx.setStorageSync('appSettings', mergedSettings)
      return true
    } catch (error) {
      console.error('导入应用设置失败:', error)
      return false
    }
  }
  
  /**
   * 验证数据包格式
   */
  static validateDataPackage(dataPackage) {
    const errors = []
    
    // 检查基本结构
    if (!dataPackage || typeof dataPackage !== 'object') {
      errors.push('数据包格式无效')
      return { isValid: false, errors }
    }
    
    // 检查版本信息
    if (!dataPackage.version) {
      errors.push('缺少版本信息')
    }
    
    // 检查数据结构
    if (!dataPackage.data || typeof dataPackage.data !== 'object') {
      errors.push('缺少数据部分')
      return { isValid: false, errors }
    }
    
    // 检查工作类型数据
    if (dataPackage.data.workTypes && !Array.isArray(dataPackage.data.workTypes)) {
      errors.push('工作类型数据格式无效')
    }
    
    // 检查工作记录数据
    if (dataPackage.data.workRecords && !Array.isArray(dataPackage.data.workRecords)) {
      errors.push('工作记录数据格式无效')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 从CSV导入工作记录
   */
  static async importWorkRecordsFromCSV(csvContent, options = {}) {
    const { hasHeader = true, delimiter = ',', validateData = true } = options
    
    try {
      // 解析CSV内容
      const lines = csvContent.trim().split('\n')
      if (lines.length === 0) {
        throw new Error('CSV文件为空')
      }
      
      let dataLines = lines
      let headers = []
      
      if (hasHeader) {
        headers = this.parseCSVLine(lines[0], delimiter)
        dataLines = lines.slice(1)
      } else {
        // 使用默认列名
        headers = [
          'date', 'workTypeName', 'title', 'startTime', 'endTime',
          'hourlyRate', 'totalSalary', 'location', 'description', 'notes'
        ]
      }
      
      // 转换为工作记录对象
      const workRecords = []
      for (let i = 0; i < dataLines.length; i++) {
        try {
          const values = this.parseCSVLine(dataLines[i], delimiter)
          const record = {}
          
          headers.forEach((header, index) => {
            if (values[index] !== undefined) {
              record[header.trim()] = values[index].trim()
            }
          })
          
          // 数据类型转换
          if (record.hourlyRate) record.hourlyRate = parseFloat(record.hourlyRate) || 0
          if (record.totalSalary) record.totalSalary = parseFloat(record.totalSalary) || 0
          if (!record.status) record.status = 'completed'
          
          workRecords.push(record)
        } catch (error) {
          console.warn(`解析第${i + 1}行失败:`, error.message)
        }
      }
      
      // 导入工作记录
      return await this.importWorkRecords(workRecords, { validateData })
    } catch (error) {
      console.error('从CSV导入失败:', error)
      return {
        imported: 0,
        skipped: 0,
        errors: 1,
        errorDetails: [{ item: 'CSV文件', error: error.message }]
      }
    }
  }
  
  /**
   * 解析CSV行
   */
  static parseCSVLine(line, delimiter = ',') {
    const values = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === delimiter && !inQuotes) {
        values.push(current)
        current = ''
      } else {
        current += char
      }
    }
    
    values.push(current)
    return values
  }
  
  /**
   * 生成导入摘要
   */
  static generateImportSummary(results) {
    const summary = {
      totalImported: 0,
      totalSkipped: 0,
      totalErrors: 0,
      details: []
    }
    
    Object.entries(results).forEach(([key, result]) => {
      if (typeof result === 'object' && result.imported !== undefined) {
        summary.totalImported += result.imported
        summary.totalSkipped += result.skipped
        summary.totalErrors += result.errors
        
        summary.details.push({
          type: key,
          imported: result.imported,
          skipped: result.skipped,
          errors: result.errors
        })
      }
    })
    
    return summary
  }
}

export default DataImporter
