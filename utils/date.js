// utils/date.js
// 日期时间工具类

/**
 * 日期时间工具类
 */
export class DateUtils {
  /**
   * 格式化日期为 YYYY-MM-DD 格式
   */
  static formatDate(date) {
    if (!date) return ''
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  }

  /**
   * 格式化时间为 HH:MM 格式
   */
  static formatTime(date) {
    if (!date) return ''
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    
    return `${hours}:${minutes}`
  }

  /**
   * 格式化日期时间为 YYYY-MM-DD HH:MM 格式
   */
  static formatDateTime(date) {
    if (!date) return ''
    
    const dateStr = this.formatDate(date)
    const timeStr = this.formatTime(date)
    
    return `${dateStr} ${timeStr}`
  }

  /**
   * 解析日期字符串
   */
  static parseDate(dateStr) {
    if (!dateStr) return null
    
    // 支持多种格式
    const formats = [
      /^(\d{4})-(\d{2})-(\d{2})$/,           // YYYY-MM-DD
      /^(\d{4})\/(\d{2})\/(\d{2})$/,         // YYYY/MM/DD
      /^(\d{4})\.(\d{2})\.(\d{2})$/          // YYYY.MM.DD
    ]
    
    for (const format of formats) {
      const match = dateStr.match(format)
      if (match) {
        const year = parseInt(match[1])
        const month = parseInt(match[2]) - 1 // 月份从0开始
        const day = parseInt(match[3])
        
        const date = new Date(year, month, day)
        if (!isNaN(date.getTime())) {
          return date
        }
      }
    }
    
    // 尝试直接解析
    const date = new Date(dateStr)
    return isNaN(date.getTime()) ? null : date
  }

  /**
   * 解析时间字符串
   */
  static parseTime(timeStr) {
    if (!timeStr) return null
    
    const match = timeStr.match(/^(\d{1,2}):(\d{2})(?::(\d{2}))?$/)
    if (!match) return null
    
    const hours = parseInt(match[1])
    const minutes = parseInt(match[2])
    const seconds = parseInt(match[3] || '0')
    
    if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
      return null
    }
    
    return { hours, minutes, seconds }
  }

  /**
   * 获取今天的日期字符串
   */
  static getToday() {
    return this.formatDate(new Date())
  }

  /**
   * 获取昨天的日期字符串
   */
  static getYesterday() {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    return this.formatDate(yesterday)
  }

  /**
   * 获取明天的日期字符串
   */
  static getTomorrow() {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return this.formatDate(tomorrow)
  }

  /**
   * 获取本周的开始日期（周一）
   */
  static getWeekStart(date = new Date()) {
    const d = new Date(date)
    const day = d.getDay()
    const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
    d.setDate(diff)
    return this.formatDate(d)
  }

  /**
   * 获取本周的结束日期（周日）
   */
  static getWeekEnd(date = new Date()) {
    const d = new Date(date)
    const day = d.getDay()
    const diff = d.getDate() - day + (day === 0 ? 0 : 7) // 调整为周日结束
    d.setDate(diff)
    return this.formatDate(d)
  }

  /**
   * 获取本月的开始日期
   */
  static getMonthStart(date = new Date()) {
    const d = new Date(date)
    d.setDate(1)
    return this.formatDate(d)
  }

  /**
   * 获取本月的结束日期
   */
  static getMonthEnd(date = new Date()) {
    const d = new Date(date)
    d.setMonth(d.getMonth() + 1, 0) // 下个月的第0天就是本月最后一天
    return this.formatDate(d)
  }

  /**
   * 计算两个日期之间的天数差
   */
  static getDaysDiff(startDate, endDate) {
    const start = this.parseDate(startDate)
    const end = this.parseDate(endDate)
    
    if (!start || !end) return 0
    
    const timeDiff = end.getTime() - start.getTime()
    return Math.ceil(timeDiff / (1000 * 3600 * 24))
  }

  /**
   * 计算两个时间之间的分钟差
   */
  static getMinutesDiff(startTime, endTime) {
    const start = this.parseTime(startTime)
    const end = this.parseTime(endTime)
    
    if (!start || !end) return 0
    
    let startMinutes = start.hours * 60 + start.minutes
    let endMinutes = end.hours * 60 + end.minutes
    
    // 处理跨天情况
    if (endMinutes < startMinutes) {
      endMinutes += 24 * 60
    }
    
    return endMinutes - startMinutes
  }

  /**
   * 添加天数到日期
   */
  static addDays(dateStr, days) {
    const date = this.parseDate(dateStr)
    if (!date) return dateStr
    
    date.setDate(date.getDate() + days)
    return this.formatDate(date)
  }

  /**
   * 添加月份到日期
   */
  static addMonths(dateStr, months) {
    const date = this.parseDate(dateStr)
    if (!date) return dateStr
    
    date.setMonth(date.getMonth() + months)
    return this.formatDate(date)
  }

  /**
   * 获取日期的星期几
   */
  static getWeekday(dateStr) {
    const date = this.parseDate(dateStr)
    if (!date) return ''
    
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weekdays[date.getDay()]
  }

  /**
   * 获取日期的英文星期几
   */
  static getWeekdayEn(dateStr) {
    const date = this.parseDate(dateStr)
    if (!date) return ''
    
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    return weekdays[date.getDay()]
  }

  /**
   * 判断是否是今天
   */
  static isToday(dateStr) {
    return dateStr === this.getToday()
  }

  /**
   * 判断是否是昨天
   */
  static isYesterday(dateStr) {
    return dateStr === this.getYesterday()
  }

  /**
   * 判断是否是明天
   */
  static isTomorrow(dateStr) {
    return dateStr === this.getTomorrow()
  }

  /**
   * 判断是否是本周
   */
  static isThisWeek(dateStr) {
    const weekStart = this.getWeekStart()
    const weekEnd = this.getWeekEnd()
    return dateStr >= weekStart && dateStr <= weekEnd
  }

  /**
   * 判断是否是本月
   */
  static isThisMonth(dateStr) {
    const monthStart = this.getMonthStart()
    const monthEnd = this.getMonthEnd()
    return dateStr >= monthStart && dateStr <= monthEnd
  }

  /**
   * 判断是否是本年
   */
  static isThisYear(dateStr) {
    const date = this.parseDate(dateStr)
    if (!date) return false
    
    const currentYear = new Date().getFullYear()
    return date.getFullYear() === currentYear
  }

  /**
   * 获取相对时间描述
   */
  static getRelativeTime(dateStr) {
    if (this.isToday(dateStr)) return '今天'
    if (this.isYesterday(dateStr)) return '昨天'
    if (this.isTomorrow(dateStr)) return '明天'
    
    const date = this.parseDate(dateStr)
    if (!date) return dateStr
    
    const today = new Date()
    const diffDays = Math.abs(this.getDaysDiff(dateStr, this.getToday()))
    
    if (diffDays <= 7) {
      return this.getWeekday(dateStr)
    }
    
    if (this.isThisYear(dateStr)) {
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    }
    
    return dateStr
  }

  /**
   * 获取月份的所有日期
   */
  static getMonthDates(year, month) {
    const dates = []
    const daysInMonth = new Date(year, month, 0).getDate()
    
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day)
      dates.push(this.formatDate(date))
    }
    
    return dates
  }

  /**
   * 获取日历网格数据（包含上月和下月的日期）
   */
  static getCalendarGrid(year, month) {
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const startDate = new Date(firstDay)
    
    // 调整到周一开始
    const dayOfWeek = firstDay.getDay()
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1
    startDate.setDate(startDate.getDate() - daysToSubtract)
    
    const grid = []
    const currentDate = new Date(startDate)
    
    // 生成6周的日期（42天）
    for (let i = 0; i < 42; i++) {
      grid.push({
        date: this.formatDate(currentDate),
        day: currentDate.getDate(),
        isCurrentMonth: currentDate.getMonth() === month - 1,
        isToday: this.isToday(this.formatDate(currentDate))
      })
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return grid
  }

  /**
   * 验证日期格式
   */
  static isValidDate(dateStr) {
    return this.parseDate(dateStr) !== null
  }

  /**
   * 验证时间格式
   */
  static isValidTime(timeStr) {
    return this.parseTime(timeStr) !== null
  }

  /**
   * 格式化工作时长
   */
  static formatDuration(minutes) {
    if (minutes < 0) return '0分钟'
    
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours === 0) {
      return `${mins}分钟`
    } else if (mins === 0) {
      return `${hours}小时`
    } else {
      return `${hours}小时${mins}分钟`
    }
  }

  /**
   * 将分钟转换为小时（保留两位小数）
   */
  static minutesToHours(minutes) {
    return Math.round((minutes / 60) * 100) / 100
  }

  /**
   * 将小时转换为分钟
   */
  static hoursToMinutes(hours) {
    return Math.round(hours * 60)
  }

  /**
   * 获取时间戳
   */
  static getTimestamp(date = new Date()) {
    return new Date(date).getTime()
  }

  /**
   * 从时间戳创建日期
   */
  static fromTimestamp(timestamp) {
    return new Date(timestamp)
  }
}

// 导出默认实例
export default DateUtils
