// utils/error-handler.js
// 错误处理和日志系统

/**
 * 日志级别
 */
const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  FATAL: 4
}

/**
 * 日志管理器
 */
class Logger {
  
  constructor() {
    this.logs = []
    this.maxLogs = 1000
    this.logLevel = LogLevel.INFO
    this.enableConsole = true
    this.enableStorage = true
  }
  
  /**
   * 设置日志级别
   */
  setLevel(level) {
    this.logLevel = level
  }
  
  /**
   * 设置控制台输出
   */
  setConsoleOutput(enabled) {
    this.enableConsole = enabled
  }
  
  /**
   * 设置存储输出
   */
  setStorageOutput(enabled) {
    this.enableStorage = enabled
  }
  
  /**
   * 记录日志
   */
  log(level, message, data = null, category = 'general') {
    if (level < this.logLevel) return
    
    const logEntry = {
      id: this.generateId(),
      timestamp: Date.now(),
      level,
      levelName: this.getLevelName(level),
      message,
      data,
      category,
      stack: this.getStackTrace(),
      userAgent: this.getUserAgent(),
      url: this.getCurrentPage()
    }
    
    // 添加到内存日志
    this.addToMemory(logEntry)
    
    // 控制台输出
    if (this.enableConsole) {
      this.outputToConsole(logEntry)
    }
    
    // 存储到本地
    if (this.enableStorage) {
      this.saveToStorage(logEntry)
    }
    
    return logEntry
  }
  
  /**
   * Debug日志
   */
  debug(message, data, category) {
    return this.log(LogLevel.DEBUG, message, data, category)
  }
  
  /**
   * Info日志
   */
  info(message, data, category) {
    return this.log(LogLevel.INFO, message, data, category)
  }
  
  /**
   * Warning日志
   */
  warn(message, data, category) {
    return this.log(LogLevel.WARN, message, data, category)
  }
  
  /**
   * Error日志
   */
  error(message, data, category) {
    return this.log(LogLevel.ERROR, message, data, category)
  }
  
  /**
   * Fatal日志
   */
  fatal(message, data, category) {
    return this.log(LogLevel.FATAL, message, data, category)
  }
  
  /**
   * 添加到内存
   */
  addToMemory(logEntry) {
    this.logs.push(logEntry)
    
    // 限制内存中的日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }
  }
  
  /**
   * 输出到控制台
   */
  outputToConsole(logEntry) {
    const { levelName, message, data } = logEntry
    const prefix = `[${new Date(logEntry.timestamp).toISOString()}] [${levelName}]`
    
    switch (logEntry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, message, data)
        break
      case LogLevel.INFO:
        console.info(prefix, message, data)
        break
      case LogLevel.WARN:
        console.warn(prefix, message, data)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, message, data)
        break
    }
  }
  
  /**
   * 保存到本地存储
   */
  async saveToStorage(logEntry) {
    try {
      const logs = wx.getStorageSync('app_logs') || []
      logs.push(logEntry)
      
      // 只保留最近500条日志
      if (logs.length > 500) {
        logs.splice(0, logs.length - 500)
      }
      
      wx.setStorageSync('app_logs', logs)
    } catch (error) {
      console.error('保存日志失败:', error)
    }
  }
  
  /**
   * 获取日志级别名称
   */
  getLevelName(level) {
    const names = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL']
    return names[level] || 'UNKNOWN'
  }
  
  /**
   * 获取堆栈跟踪
   */
  getStackTrace() {
    try {
      throw new Error()
    } catch (e) {
      return e.stack
    }
  }
  
  /**
   * 获取用户代理
   */
  getUserAgent() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return `${systemInfo.platform} ${systemInfo.system} ${systemInfo.version}`
    } catch (error) {
      return 'Unknown'
    }
  }
  
  /**
   * 获取当前页面
   */
  getCurrentPage() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      return currentPage ? currentPage.route : 'Unknown'
    } catch (error) {
      return 'Unknown'
    }
  }
  
  /**
   * 生成ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
  
  /**
   * 获取内存中的日志
   */
  getLogs(filter = {}) {
    let logs = this.logs
    
    // 按级别过滤
    if (filter.level !== undefined) {
      logs = logs.filter(log => log.level >= filter.level)
    }
    
    // 按分类过滤
    if (filter.category) {
      logs = logs.filter(log => log.category === filter.category)
    }
    
    // 按时间范围过滤
    if (filter.startTime) {
      logs = logs.filter(log => log.timestamp >= filter.startTime)
    }
    
    if (filter.endTime) {
      logs = logs.filter(log => log.timestamp <= filter.endTime)
    }
    
    return logs
  }
  
  /**
   * 清除日志
   */
  clear() {
    this.logs = []
    try {
      wx.removeStorageSync('app_logs')
    } catch (error) {
      console.error('清除日志失败:', error)
    }
  }
  
  /**
   * 导出日志
   */
  export() {
    return {
      timestamp: Date.now(),
      logs: this.logs,
      systemInfo: this.getUserAgent(),
      appVersion: '1.0.0'
    }
  }
}

/**
 * 错误处理器
 */
class ErrorHandler {
  
  constructor(logger) {
    this.logger = logger
    this.errorCallbacks = []
    this.setupGlobalErrorHandling()
  }
  
  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandling() {
    // 监听小程序错误
    if (typeof wx !== 'undefined') {
      wx.onError((error) => {
        this.handleError(error, 'global')
      })
      
      wx.onUnhandledRejection((res) => {
        this.handleError(res.reason, 'unhandled_rejection')
      })
    }
    
    // 监听Promise错误
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(event.reason, 'unhandled_rejection')
      })
    }
  }
  
  /**
   * 处理错误
   */
  handleError(error, category = 'general', context = {}) {
    const errorInfo = this.parseError(error)
    
    // 记录错误日志
    this.logger.error(errorInfo.message, {
      ...errorInfo,
      context,
      category
    }, category)
    
    // 调用错误回调
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorInfo, category, context)
      } catch (callbackError) {
        console.error('错误回调执行失败:', callbackError)
      }
    })
    
    // 根据错误类型决定是否显示用户提示
    if (this.shouldShowUserMessage(errorInfo, category)) {
      this.showUserErrorMessage(errorInfo)
    }
    
    return errorInfo
  }
  
  /**
   * 解析错误信息
   */
  parseError(error) {
    if (typeof error === 'string') {
      return {
        message: error,
        stack: null,
        type: 'string'
      }
    }
    
    if (error instanceof Error) {
      return {
        message: error.message,
        stack: error.stack,
        name: error.name,
        type: 'Error'
      }
    }
    
    if (typeof error === 'object') {
      return {
        message: error.message || JSON.stringify(error),
        stack: error.stack || null,
        type: 'object',
        data: error
      }
    }
    
    return {
      message: String(error),
      stack: null,
      type: typeof error
    }
  }
  
  /**
   * 判断是否显示用户消息
   */
  shouldShowUserMessage(errorInfo, category) {
    // 网络错误显示给用户
    if (category === 'network') return true
    
    // 数据错误显示给用户
    if (category === 'data') return true
    
    // 用户操作错误显示给用户
    if (category === 'user_action') return true
    
    // 其他错误不显示给用户
    return false
  }
  
  /**
   * 显示用户错误消息
   */
  showUserErrorMessage(errorInfo) {
    const message = this.getUserFriendlyMessage(errorInfo)
    
    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })
    }
  }
  
  /**
   * 获取用户友好的错误消息
   */
  getUserFriendlyMessage(errorInfo) {
    const message = errorInfo.message.toLowerCase()
    
    if (message.includes('network') || message.includes('timeout')) {
      return '网络连接异常，请检查网络后重试'
    }
    
    if (message.includes('storage') || message.includes('quota')) {
      return '存储空间不足，请清理后重试'
    }
    
    if (message.includes('permission')) {
      return '权限不足，请检查应用权限设置'
    }
    
    return '操作失败，请稍后重试'
  }
  
  /**
   * 添加错误回调
   */
  addErrorCallback(callback) {
    this.errorCallbacks.push(callback)
  }
  
  /**
   * 移除错误回调
   */
  removeErrorCallback(callback) {
    const index = this.errorCallbacks.indexOf(callback)
    if (index > -1) {
      this.errorCallbacks.splice(index, 1)
    }
  }
  
  /**
   * 包装异步函数
   */
  wrapAsync(fn, category = 'async') {
    return async (...args) => {
      try {
        return await fn.apply(this, args)
      } catch (error) {
        this.handleError(error, category, { function: fn.name, args })
        throw error
      }
    }
  }
  
  /**
   * 包装同步函数
   */
  wrapSync(fn, category = 'sync') {
    return (...args) => {
      try {
        return fn.apply(this, args)
      } catch (error) {
        this.handleError(error, category, { function: fn.name, args })
        throw error
      }
    }
  }
}

/**
 * 性能监控装饰器
 */
function performanceMonitor(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      const startTime = Date.now()
      
      try {
        const result = originalMethod.apply(this, args)
        
        // 如果是Promise，监控异步执行
        if (result && typeof result.then === 'function') {
          return result.finally(() => {
            const duration = Date.now() - startTime
            logger.debug(`${name || propertyKey} 执行完成`, { duration })
          })
        }
        
        const duration = Date.now() - startTime
        logger.debug(`${name || propertyKey} 执行完成`, { duration })
        
        return result
      } catch (error) {
        const duration = Date.now() - startTime
        logger.error(`${name || propertyKey} 执行失败`, { duration, error })
        throw error
      }
    }
    
    return descriptor
  }
}

/**
 * 错误捕获装饰器
 */
function errorCatch(category = 'method') {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      try {
        const result = originalMethod.apply(this, args)
        
        // 如果是Promise，捕获异步错误
        if (result && typeof result.then === 'function') {
          return result.catch(error => {
            errorHandler.handleError(error, category, {
              method: propertyKey,
              args
            })
            throw error
          })
        }
        
        return result
      } catch (error) {
        errorHandler.handleError(error, category, {
          method: propertyKey,
          args
        })
        throw error
      }
    }
    
    return descriptor
  }
}

// 创建全局实例
const logger = new Logger()
const errorHandler = new ErrorHandler(logger)

export {
  Logger,
  ErrorHandler,
  LogLevel,
  performanceMonitor,
  errorCatch,
  logger,
  errorHandler
}
