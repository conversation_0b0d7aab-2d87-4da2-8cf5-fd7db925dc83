/**
 * 统一错误处理工具类
 * 提供项目中所有模块统一的错误处理方法
 */

import { ErrorHandler } from './error-handler.js';

/**
 * 通用错误处理器
 */
class CommonErrorHandler {
  /**
   * 处理异步操作错误
   * @param {Function} asyncFunction - 异步函数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 统一的结果格式
   */
  static async handleAsync(asyncFunction, options = {}) {
    const {
      context = '异步操作',
      showToast = true,
      logError = true,
      defaultValue = null
    } = options;

    try {
      const result = await asyncFunction();
      return {
        success: true,
        data: result,
        error: null
      };
    } catch (error) {
      const errorInfo = {
        context,
        error: error.message || error,
        timestamp: new Date().toISOString(),
        stack: error.stack
      };

      if (logError) {
        ErrorHandler.logError(errorInfo);
      }

      if (showToast) {
        wx.showToast({
          title: `${context}失败`,
          icon: 'none',
          duration: 2000
        });
      }

      return {
        success: false,
        data: defaultValue,
        error: errorInfo
      };
    }
  }

  /**
   * 处理存储操作错误
   * @param {Function} storageFunction - 存储操作函数
   * @param {string} operation - 操作类型
   * @returns {Promise<Object>} 统一的结果格式
   */
  static async handleStorage(storageFunction, operation = '存储操作') {
    return this.handleAsync(storageFunction, {
      context: operation,
      showToast: true,
      logError: true,
      defaultValue: []
    });
  }

  /**
   * 处理页面操作错误
   * @param {Function} pageFunction - 页面操作函数
   * @param {string} pageName - 页面名称
   * @returns {Promise<Object>} 统一的结果格式
   */
  static async handlePageOperation(pageFunction, pageName = '页面操作') {
    return this.handleAsync(pageFunction, {
      context: `${pageName}操作`,
      showToast: true,
      logError: true,
      defaultValue: {}
    });
  }

  /**
   * 处理数据验证错误
   * @param {Function} validationFunction - 验证函数
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果
   */
  static handleValidation(validationFunction, data) {
    try {
      const result = validationFunction(data);
      return {
        isValid: true,
        errors: [],
        data: result
      };
    } catch (error) {
      ErrorHandler.logError({
        context: '数据验证',
        error: error.message || error,
        data,
        timestamp: new Date().toISOString()
      });

      return {
        isValid: false,
        errors: [error.message || '数据验证失败'],
        data: null
      };
    }
  }

  /**
   * 处理网络请求错误
   * @param {Function} requestFunction - 网络请求函数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 统一的结果格式
   */
  static async handleNetworkRequest(requestFunction, options = {}) {
    const {
      timeout = 10000,
      retries = 3,
      showLoading = true
    } = options;

    if (showLoading) {
      wx.showLoading({ title: '请求中...' });
    }

    let lastError = null;
    
    for (let i = 0; i < retries; i++) {
      try {
        const result = await Promise.race([
          requestFunction(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('请求超时')), timeout)
          )
        ]);

        if (showLoading) {
          wx.hideLoading();
        }

        return {
          success: true,
          data: result,
          error: null
        };
      } catch (error) {
        lastError = error;
        
        if (i < retries - 1) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
      }
    }

    if (showLoading) {
      wx.hideLoading();
    }

    const errorInfo = {
      context: '网络请求',
      error: lastError.message || lastError,
      retries,
      timestamp: new Date().toISOString()
    };

    ErrorHandler.logError(errorInfo);

    wx.showToast({
      title: '网络请求失败',
      icon: 'none',
      duration: 2000
    });

    return {
      success: false,
      data: null,
      error: errorInfo
    };
  }

  /**
   * 处理用户输入错误
   * @param {Object} formData - 表单数据
   * @param {Object} rules - 验证规则
   * @returns {Object} 验证结果
   */
  static handleFormValidation(formData, rules) {
    const errors = [];
    
    try {
      Object.keys(rules).forEach(field => {
        const rule = rules[field];
        const value = formData[field];
        
        // 必填验证
        if (rule.required && (!value || value.toString().trim() === '')) {
          errors.push(`${rule.label || field}不能为空`);
          return;
        }
        
        // 类型验证
        if (value && rule.type) {
          switch (rule.type) {
            case 'number':
              if (isNaN(Number(value))) {
                errors.push(`${rule.label || field}必须是数字`);
              }
              break;
            case 'email':
              if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                errors.push(`${rule.label || field}格式不正确`);
              }
              break;
            case 'date':
              if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
                errors.push(`${rule.label || field}日期格式不正确`);
              }
              break;
            case 'time':
              if (!/^\d{2}:\d{2}$/.test(value)) {
                errors.push(`${rule.label || field}时间格式不正确`);
              }
              break;
          }
        }
        
        // 长度验证
        if (value && rule.minLength && value.length < rule.minLength) {
          errors.push(`${rule.label || field}长度不能少于${rule.minLength}个字符`);
        }
        
        if (value && rule.maxLength && value.length > rule.maxLength) {
          errors.push(`${rule.label || field}长度不能超过${rule.maxLength}个字符`);
        }
        
        // 数值范围验证
        if (value && rule.min && Number(value) < rule.min) {
          errors.push(`${rule.label || field}不能小于${rule.min}`);
        }
        
        if (value && rule.max && Number(value) > rule.max) {
          errors.push(`${rule.label || field}不能大于${rule.max}`);
        }
        
        // 自定义验证
        if (value && rule.validator && typeof rule.validator === 'function') {
          const customResult = rule.validator(value);
          if (customResult !== true) {
            errors.push(customResult || `${rule.label || field}验证失败`);
          }
        }
      });
      
      return {
        isValid: errors.length === 0,
        errors,
        data: errors.length === 0 ? formData : null
      };
    } catch (error) {
      ErrorHandler.logError({
        context: '表单验证',
        error: error.message || error,
        formData,
        rules,
        timestamp: new Date().toISOString()
      });
      
      return {
        isValid: false,
        errors: ['表单验证过程中发生错误'],
        data: null
      };
    }
  }

  /**
   * 显示错误信息
   * @param {string|Array} errors - 错误信息
   * @param {Object} options - 选项
   */
  static showError(errors, options = {}) {
    const {
      title = '操作失败',
      duration = 3000,
      showModal = false
    } = options;

    const errorMessage = Array.isArray(errors) ? errors.join('\n') : errors;

    if (showModal) {
      wx.showModal({
        title,
        content: errorMessage,
        showCancel: false,
        confirmText: '确定'
      });
    } else {
      wx.showToast({
        title: errorMessage.length > 20 ? errorMessage.substring(0, 20) + '...' : errorMessage,
        icon: 'none',
        duration
      });
    }
  }

  /**
   * 显示成功信息
   * @param {string} message - 成功信息
   * @param {Object} options - 选项
   */
  static showSuccess(message, options = {}) {
    const {
      icon = 'success',
      duration = 2000
    } = options;

    wx.showToast({
      title: message,
      icon,
      duration
    });
  }
}

export default CommonErrorHandler;
