// utils/data-export.js
// 数据导入导出工具类

import DateUtils from './date.js'
import SalaryCalculator from './salary.js'
import { workRecordStorage, workTypeStorage } from './storage.js'

/**
 * 数据导出工具类
 */
class DataExporter {
  
  /**
   * 导出所有数据为JSON格式
   */
  static async exportAllData() {
    try {
      const [recordsResult, workTypesResult] = await Promise.all([
        workRecordStorage.getAll(),
        workTypeStorage.getAll()
      ])
      
      const exportData = {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        appInfo: {
          name: '兼职工作管理',
          version: '1.0.0'
        },
        data: {
          workRecords: recordsResult.success ? recordsResult.data : [],
          workTypes: workTypesResult.success ? workTypesResult.data : [],
          userInfo: this.getUserInfo(),
          settings: this.getAppSettings()
        },
        statistics: await this.generateStatistics()
      }
      
      return {
        success: true,
        data: exportData,
        filename: `work-data-${DateUtils.formatDate(new Date(), 'YYYY-MM-DD')}.json`
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 导出工作记录为CSV格式
   */
  static async exportWorkRecordsCSV(startDate = null, endDate = null) {
    try {
      const result = await workRecordStorage.getAll()
      if (!result.success) {
        throw new Error('获取工作记录失败')
      }
      
      let records = result.data || []
      
      // 时间范围过滤
      if (startDate && endDate) {
        records = records.filter(record => 
          record.date >= startDate && record.date <= endDate
        )
      }
      
      // 获取工作类型信息
      const workTypesResult = await workTypeStorage.getAll()
      const workTypes = workTypesResult.success ? workTypesResult.data : []
      const workTypeMap = new Map(workTypes.map(wt => [wt.id, wt]))
      
      // 生成CSV内容
      const csvHeaders = [
        '日期', '工作类型', '工作标题', '开始时间', '结束时间', 
        '工作时长(小时)', '时薪(元)', '总收入(元)', '实际收入(元)', 
        '工作地点', '工作描述', '备注', '状态', '创建时间'
      ]
      
      const csvRows = records.map(record => {
        const workType = workTypeMap.get(record.workTypeId)
        const duration = DateUtils.minutesToHours(record.duration)
        
        return [
          record.date,
          workType?.name || '未知类型',
          record.title || '',
          record.startTime,
          record.endTime,
          duration.toFixed(2),
          record.hourlyRate,
          record.totalSalary,
          record.actualSalary || '',
          record.location || '',
          record.description || '',
          record.notes || '',
          this.getStatusText(record.status),
          DateUtils.formatDateTime(new Date(record.createdAt))
        ]
      })
      
      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n')
      
      return {
        success: true,
        data: csvContent,
        filename: `work-records-${DateUtils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`,
        recordCount: records.length
      }
    } catch (error) {
      console.error('导出CSV失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 导出统计报告
   */
  static async exportStatisticsReport(timeRange = 'month') {
    try {
      const result = await workRecordStorage.getAll()
      if (!result.success) {
        throw new Error('获取工作记录失败')
      }
      
      const records = result.data || []
      const filteredRecords = this.filterRecordsByTimeRange(records, timeRange)
      
      // 生成统计数据
      const stats = SalaryCalculator.calculateIncomeStats(filteredRecords)
      const workTypeStats = await this.calculateWorkTypeStats(filteredRecords)
      const timeStats = this.calculateTimeStats(filteredRecords)
      
      const report = {
        reportInfo: {
          title: '工作统计报告',
          timeRange: this.getTimeRangeText(timeRange),
          generateTime: DateUtils.formatDateTime(new Date()),
          recordCount: filteredRecords.length
        },
        summary: {
          totalIncome: stats.totalIncome,
          totalHours: stats.totalHours,
          averageHourlyRate: stats.averageHourlyRate,
          workDays: new Set(filteredRecords.map(r => r.date)).size
        },
        workTypeAnalysis: workTypeStats,
        timeAnalysis: timeStats,
        detailRecords: filteredRecords.map(record => ({
          date: record.date,
          workType: record.workTypeName || '未知类型',
          duration: DateUtils.formatDuration(record.duration),
          income: record.totalSalary,
          status: this.getStatusText(record.status)
        }))
      }
      
      return {
        success: true,
        data: report,
        filename: `statistics-report-${DateUtils.formatDate(new Date(), 'YYYY-MM-DD')}.json`
      }
    } catch (error) {
      console.error('导出统计报告失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 根据时间范围过滤记录
   */
  static filterRecordsByTimeRange(records, timeRange) {
    const now = new Date()
    let startDate, endDate
    
    switch (timeRange) {
      case 'week':
        startDate = DateUtils.getWeekStart(now)
        endDate = DateUtils.getWeekEnd(now)
        break
      case 'month':
        startDate = DateUtils.getMonthStart(now)
        endDate = DateUtils.getMonthEnd(now)
        break
      case 'quarter':
        startDate = DateUtils.getQuarterStart(now)
        endDate = DateUtils.getQuarterEnd(now)
        break
      case 'year':
        startDate = DateUtils.getYearStart(now)
        endDate = DateUtils.getYearEnd(now)
        break
      default:
        return records
    }
    
    return records.filter(record => 
      record.date >= startDate && record.date <= endDate
    )
  }
  
  /**
   * 计算工作类型统计
   */
  static async calculateWorkTypeStats(records) {
    const workTypesResult = await workTypeStorage.getAll()
    const workTypes = workTypesResult.success ? workTypesResult.data : []
    const workTypeMap = new Map(workTypes.map(wt => [wt.id, wt]))
    
    const stats = {}
    
    records.forEach(record => {
      const typeId = record.workTypeId
      const workType = workTypeMap.get(typeId)
      
      if (!stats[typeId]) {
        stats[typeId] = {
          name: workType?.name || '未知类型',
          count: 0,
          totalIncome: 0,
          totalHours: 0,
          averageHourlyRate: 0
        }
      }
      
      stats[typeId].count += 1
      stats[typeId].totalIncome += record.totalSalary
      stats[typeId].totalHours += DateUtils.minutesToHours(record.duration)
    })
    
    // 计算平均时薪
    Object.values(stats).forEach(stat => {
      stat.averageHourlyRate = stat.totalHours > 0 ? 
        stat.totalIncome / stat.totalHours : 0
    })
    
    return Object.values(stats)
  }
  
  /**
   * 计算时间统计
   */
  static calculateTimeStats(records) {
    const dayStats = {}
    const monthStats = {}
    
    records.forEach(record => {
      const date = new Date(record.date)
      const dayOfWeek = DateUtils.getDayOfWeek(record.date)
      const month = DateUtils.formatDate(date, 'YYYY-MM')
      
      // 按星期统计
      if (!dayStats[dayOfWeek]) {
        dayStats[dayOfWeek] = { count: 0, income: 0, hours: 0 }
      }
      dayStats[dayOfWeek].count += 1
      dayStats[dayOfWeek].income += record.totalSalary
      dayStats[dayOfWeek].hours += DateUtils.minutesToHours(record.duration)
      
      // 按月份统计
      if (!monthStats[month]) {
        monthStats[month] = { count: 0, income: 0, hours: 0 }
      }
      monthStats[month].count += 1
      monthStats[month].income += record.totalSalary
      monthStats[month].hours += DateUtils.minutesToHours(record.duration)
    })
    
    return {
      byDayOfWeek: dayStats,
      byMonth: monthStats
    }
  }
  
  /**
   * 生成统计数据
   */
  static async generateStatistics() {
    try {
      const result = await workRecordStorage.getAll()
      const records = result.success ? result.data : []
      
      if (records.length === 0) {
        return null
      }
      
      const stats = SalaryCalculator.calculateIncomeStats(records)
      
      return {
        totalRecords: records.length,
        totalIncome: stats.totalIncome,
        totalHours: stats.totalHours,
        averageHourlyRate: stats.averageHourlyRate,
        workDays: new Set(records.map(r => r.date)).size,
        dateRange: {
          start: Math.min(...records.map(r => new Date(r.date).getTime())),
          end: Math.max(...records.map(r => new Date(r.date).getTime()))
        }
      }
    } catch (error) {
      console.error('生成统计数据失败:', error)
      return null
    }
  }
  
  /**
   * 获取用户信息
   */
  static getUserInfo() {
    try {
      return wx.getStorageSync('userInfo') || {}
    } catch (error) {
      return {}
    }
  }
  
  /**
   * 获取应用设置
   */
  static getAppSettings() {
    try {
      return wx.getStorageSync('appSettings') || {}
    } catch (error) {
      return {}
    }
  }
  
  /**
   * 获取状态文本
   */
  static getStatusText(status) {
    const statusMap = {
      planned: '计划中',
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消'
    }
    return statusMap[status] || '未知'
  }
  
  /**
   * 获取时间范围文本
   */
  static getTimeRangeText(timeRange) {
    const rangeMap = {
      week: '本周',
      month: '本月',
      quarter: '本季度',
      year: '本年'
    }
    return rangeMap[timeRange] || '全部'
  }
}

export default DataExporter
