// utils/ux-optimizer.js
// 用户体验优化工具

/**
 * 加载状态管理器
 */
class LoadingManager {
  
  constructor() {
    this.loadingStates = new Map()
    this.globalLoading = false
  }
  
  /**
   * 显示加载状态
   */
  show(key = 'global', options = {}) {
    const {
      title = '加载中...',
      mask = true,
      duration = 0
    } = options
    
    this.loadingStates.set(key, {
      title,
      mask,
      startTime: Date.now(),
      duration
    })
    
    if (key === 'global') {
      this.globalLoading = true
      wx.showLoading({ title, mask })
      
      // 自动隐藏
      if (duration > 0) {
        setTimeout(() => this.hide(key), duration)
      }
    }
  }
  
  /**
   * 隐藏加载状态
   */
  hide(key = 'global') {
    if (this.loadingStates.has(key)) {
      const loadingState = this.loadingStates.get(key)
      const duration = Date.now() - loadingState.startTime
      
      this.loadingStates.delete(key)
      
      if (key === 'global') {
        this.globalLoading = false
        wx.hideLoading()
      }
      
      return duration
    }
  }
  
  /**
   * 检查是否正在加载
   */
  isLoading(key = 'global') {
    return this.loadingStates.has(key)
  }
  
  /**
   * 获取所有加载状态
   */
  getAllStates() {
    return Object.fromEntries(this.loadingStates)
  }
  
  /**
   * 清除所有加载状态
   */
  clear() {
    this.loadingStates.clear()
    this.globalLoading = false
    wx.hideLoading()
  }
}

/**
 * 反馈管理器
 */
class FeedbackManager {
  
  /**
   * 显示成功消息
   */
  static success(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration,
      mask: false
    })
  }
  
  /**
   * 显示错误消息
   */
  static error(message, duration = 3000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration,
      mask: false
    })
  }
  
  /**
   * 显示警告消息
   */
  static warning(message, duration = 2500) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration,
      mask: false
    })
  }
  
  /**
   * 显示信息消息
   */
  static info(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration,
      mask: false
    })
  }
  
  /**
   * 显示确认对话框
   */
  static confirm(options = {}) {
    const {
      title = '确认',
      content = '',
      confirmText = '确定',
      cancelText = '取消',
      confirmColor = '#007AFF'
    } = options
    
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        confirmText,
        cancelText,
        confirmColor,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }
  
  /**
   * 显示操作表
   */
  static actionSheet(itemList, title = '') {
    return new Promise((resolve) => {
      wx.showActionSheet({
        itemList,
        title,
        success: (res) => {
          resolve(res.tapIndex)
        },
        fail: () => {
          resolve(-1)
        }
      })
    })
  }
  
  /**
   * 触觉反馈
   */
  static haptic(type = 'light') {
    try {
      switch (type) {
        case 'light':
          wx.vibrateShort({ type: 'light' })
          break
        case 'medium':
          wx.vibrateShort({ type: 'medium' })
          break
        case 'heavy':
          wx.vibrateShort({ type: 'heavy' })
          break
        case 'long':
          wx.vibrateLong()
          break
      }
    } catch (error) {
      console.warn('触觉反馈不支持:', error)
    }
  }
}

/**
 * 动画管理器
 */
class AnimationManager {
  
  /**
   * 创建动画
   */
  static createAnimation(options = {}) {
    const {
      duration = 300,
      timingFunction = 'ease',
      delay = 0,
      transformOrigin = '50% 50% 0'
    } = options
    
    return wx.createAnimation({
      duration,
      timingFunction,
      delay,
      transformOrigin
    })
  }
  
  /**
   * 淡入动画
   */
  static fadeIn(duration = 300) {
    const animation = this.createAnimation({ duration })
    animation.opacity(1).step()
    return animation.export()
  }
  
  /**
   * 淡出动画
   */
  static fadeOut(duration = 300) {
    const animation = this.createAnimation({ duration })
    animation.opacity(0).step()
    return animation.export()
  }
  
  /**
   * 滑入动画
   */
  static slideIn(direction = 'up', distance = 100, duration = 300) {
    const animation = this.createAnimation({ duration })
    
    switch (direction) {
      case 'up':
        animation.translateY(0).step()
        break
      case 'down':
        animation.translateY(0).step()
        break
      case 'left':
        animation.translateX(0).step()
        break
      case 'right':
        animation.translateX(0).step()
        break
    }
    
    return animation.export()
  }
  
  /**
   * 滑出动画
   */
  static slideOut(direction = 'down', distance = 100, duration = 300) {
    const animation = this.createAnimation({ duration })
    
    switch (direction) {
      case 'up':
        animation.translateY(-distance).step()
        break
      case 'down':
        animation.translateY(distance).step()
        break
      case 'left':
        animation.translateX(-distance).step()
        break
      case 'right':
        animation.translateX(distance).step()
        break
    }
    
    return animation.export()
  }
  
  /**
   * 缩放动画
   */
  static scale(scale = 1, duration = 300) {
    const animation = this.createAnimation({ duration })
    animation.scale(scale).step()
    return animation.export()
  }
  
  /**
   * 旋转动画
   */
  static rotate(angle = 360, duration = 300) {
    const animation = this.createAnimation({ duration })
    animation.rotate(angle).step()
    return animation.export()
  }
  
  /**
   * 弹跳动画
   */
  static bounce(duration = 600) {
    const animation = this.createAnimation({ 
      duration: duration / 3,
      timingFunction: 'ease-out'
    })
    
    animation.scale(1.1).step()
    animation.scale(0.95).step()
    animation.scale(1).step()
    
    return animation.export()
  }
  
  /**
   * 摇摆动画
   */
  static shake(duration = 500) {
    const animation = this.createAnimation({ 
      duration: duration / 6,
      timingFunction: 'ease-in-out'
    })
    
    animation.translateX(10).step()
    animation.translateX(-10).step()
    animation.translateX(8).step()
    animation.translateX(-8).step()
    animation.translateX(5).step()
    animation.translateX(0).step()
    
    return animation.export()
  }
}

/**
 * 交互优化器
 */
class InteractionOptimizer {
  
  /**
   * 防止重复点击
   */
  static preventDoubleClick(func, delay = 500) {
    let lastClickTime = 0
    
    return function(...args) {
      const now = Date.now()
      if (now - lastClickTime > delay) {
        lastClickTime = now
        return func.apply(this, args)
      }
    }
  }
  
  /**
   * 长按检测
   */
  static longPress(element, callback, duration = 500) {
    let timer = null
    let startTime = 0
    
    const start = () => {
      startTime = Date.now()
      timer = setTimeout(() => {
        callback()
        FeedbackManager.haptic('medium')
      }, duration)
    }
    
    const end = () => {
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
    }
    
    return { start, end }
  }
  
  /**
   * 滑动手势检测
   */
  static swipeGesture(options = {}) {
    const {
      minDistance = 50,
      maxTime = 300,
      onSwipeLeft,
      onSwipeRight,
      onSwipeUp,
      onSwipeDown
    } = options
    
    let startX = 0
    let startY = 0
    let startTime = 0
    
    const start = (e) => {
      const touch = e.touches[0]
      startX = touch.clientX
      startY = touch.clientY
      startTime = Date.now()
    }
    
    const end = (e) => {
      const touch = e.changedTouches[0]
      const endX = touch.clientX
      const endY = touch.clientY
      const endTime = Date.now()
      
      const deltaX = endX - startX
      const deltaY = endY - startY
      const deltaTime = endTime - startTime
      
      if (deltaTime > maxTime) return
      
      const absX = Math.abs(deltaX)
      const absY = Math.abs(deltaY)
      
      if (absX > minDistance && absX > absY) {
        // 水平滑动
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight()
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft()
        }
      } else if (absY > minDistance && absY > absX) {
        // 垂直滑动
        if (deltaY > 0 && onSwipeDown) {
          onSwipeDown()
        } else if (deltaY < 0 && onSwipeUp) {
          onSwipeUp()
        }
      }
    }
    
    return { start, end }
  }
  
  /**
   * 平滑滚动
   */
  static smoothScroll(scrollTop, duration = 300) {
    return new Promise((resolve) => {
      wx.pageScrollTo({
        scrollTop,
        duration,
        success: resolve,
        fail: resolve
      })
    })
  }
  
  /**
   * 元素可见性检测
   */
  static observeVisibility(selector, callback) {
    const observer = wx.createIntersectionObserver()
    
    observer.observe(selector, (res) => {
      callback(res.intersectionRatio > 0)
    })
    
    return observer
  }
}

/**
 * 性能优化器
 */
class UXPerformanceOptimizer {
  
  /**
   * 图片懒加载
   */
  static lazyLoadImages(selector = '.lazy-image') {
    const observer = wx.createIntersectionObserver({
      rootMargin: '50px'
    })
    
    observer.observe(selector, (res) => {
      if (res.intersectionRatio > 0) {
        // 触发图片加载
        const dataset = res.dataset
        if (dataset.src) {
          // 这里需要在页面中实现具体的图片加载逻辑
          console.log('加载图片:', dataset.src)
        }
      }
    })
    
    return observer
  }
  
  /**
   * 虚拟列表优化
   */
  static virtualList(options = {}) {
    const {
      itemHeight = 100,
      containerHeight = 600,
      overscan = 5
    } = options
    
    return {
      getVisibleRange(scrollTop, totalItems) {
        const startIndex = Math.floor(scrollTop / itemHeight)
        const endIndex = Math.min(
          startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
          totalItems
        )
        
        return {
          startIndex: Math.max(0, startIndex - overscan),
          endIndex,
          offsetY: startIndex * itemHeight
        }
      }
    }
  }
  
  /**
   * 预加载关键资源
   */
  static preloadResources(resources) {
    resources.forEach(resource => {
      if (resource.type === 'image') {
        const img = new Image()
        img.src = resource.url
      } else if (resource.type === 'data') {
        // 预加载数据
        wx.request({
          url: resource.url,
          method: 'GET',
          success: () => {
            console.log('预加载数据成功:', resource.url)
          }
        })
      }
    })
  }
}

// 创建全局实例
const loadingManager = new LoadingManager()

export {
  LoadingManager,
  FeedbackManager,
  AnimationManager,
  InteractionOptimizer,
  UXPerformanceOptimizer,
  loadingManager
}
