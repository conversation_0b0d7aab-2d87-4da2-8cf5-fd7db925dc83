// utils/debug.js
// 调试工具和开发辅助

import { logger } from "./error-handler.js";
import { performanceManager } from "./performance.js";

/**
 * 调试管理器
 */
class DebugManager {
  constructor() {
    this.isDebugMode = false;
    this.debugLevel = 0;
    this.debugFilters = [];
    this.debugCallbacks = [];
    this.init();
  }

  /**
   * 初始化调试系统
   */
  init() {
    // 检查是否为开发环境
    this.isDebugMode = this.checkDebugMode();

    if (this.isDebugMode) {
      this.enableDebugMode();
    }
  }

  /**
   * 检查是否为调试模式
   */
  checkDebugMode() {
    try {
      // 检查开发者工具
      const accountInfo = wx.getAccountInfoSync();
      if (accountInfo.miniProgram.envVersion === "develop") {
        return true;
      }

      // 检查本地存储中的调试标志
      const debugFlag = wx.getStorageSync("debug_mode");
      if (debugFlag) {
        return true;
      }

      // 检查系统信息
      const systemInfo = wx.getSystemInfoSync();
      if (systemInfo.platform === "devtools") {
        return true;
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 启用调试模式
   */
  enableDebugMode() {
    console.log("🐛 调试模式已启用");

    // 设置控制台样式
    this.setupConsoleStyles();

    // 添加全局调试方法
    this.addGlobalDebugMethods();

    // 监听页面生命周期
    this.monitorPageLifecycle();

    // 监听网络请求
    this.monitorNetworkRequests();

    // 添加调试面板
    this.addDebugPanel();
  }

  /**
   * 设置控制台样式
   */
  setupConsoleStyles() {
    this.styles = {
      info: "color: #007AFF; font-weight: bold;",
      warn: "color: #FF9500; font-weight: bold;",
      error: "color: #FF3B30; font-weight: bold;",
      success: "color: #34C759; font-weight: bold;",
      debug: "color: #666; font-style: italic;",
    };
  }

  /**
   * 添加全局调试方法
   */
  addGlobalDebugMethods() {
    // 添加到全局对象
    if (typeof global !== "undefined") {
      global.debug = this;
    }

    // 添加快捷方法
    this.addShortcutMethods();
  }

  /**
   * 添加快捷方法
   */
  addShortcutMethods() {
    // 性能监控快捷方法
    global.perf = {
      start: (name) => performanceManager.startTimer(name),
      end: (name) => performanceManager.endTimer(name),
      report: () => performanceManager.getMetrics(),
    };

    // 存储快捷方法
    global.storage = {
      get: (key) => wx.getStorageSync(key),
      set: (key, value) => wx.setStorageSync(key, value),
      remove: (key) => wx.removeStorageSync(key),
      clear: () => wx.clearStorageSync(),
      info: () => wx.getStorageInfoSync(),
    };

    // 页面快捷方法
    global.page = {
      current: () => {
        const pages = getCurrentPages();
        return pages[pages.length - 1];
      },
      data: () => {
        const page = global.page.current();
        return page ? page.data : null;
      },
      setData: (data) => {
        const page = global.page.current();
        if (page) page.setData(data);
      },
    };
  }

  /**
   * 监听页面生命周期
   */
  monitorPageLifecycle() {
    const originalPage = Page;

    Page = (options) => {
      const originalOnLoad = options.onLoad;
      const originalOnShow = options.onShow;
      const originalOnHide = options.onHide;
      const originalOnUnload = options.onUnload;

      options.onLoad = function (query) {
        this.debugInfo = {
          route: this.route,
          loadTime: Date.now(),
          query,
        };

        console.log(`📄 页面加载: ${this.route}`, query);

        if (originalOnLoad) {
          return originalOnLoad.call(this, query);
        }
      };

      options.onShow = function () {
        console.log(`👁️ 页面显示: ${this.route}`);

        if (originalOnShow) {
          return originalOnShow.call(this);
        }
      };

      options.onHide = function () {
        console.log(`🙈 页面隐藏: ${this.route}`);

        if (originalOnHide) {
          return originalOnHide.call(this);
        }
      };

      options.onUnload = function () {
        const duration = Date.now() - this.debugInfo.loadTime;
        console.log(`🗑️ 页面卸载: ${this.route} (停留${duration}ms)`);

        // 清理调试相关的资源
        if (this.debugTimer) {
          clearInterval(this.debugTimer);
          this.debugTimer = null;
        }

        if (originalOnUnload) {
          return originalOnUnload.call(this);
        }
      };

      return originalPage(options);
    };
  }

  /**
   * 监听网络请求
   */
  monitorNetworkRequests() {
    const originalRequest = wx.request;

    wx.request = (options) => {
      const startTime = Date.now();
      const requestId = this.generateId();

      console.log(`🌐 网络请求开始 [${requestId}]:`, {
        url: options.url,
        method: options.method || "GET",
        data: options.data,
      });

      const originalSuccess = options.success;
      const originalFail = options.fail;
      const originalComplete = options.complete;

      options.success = (res) => {
        const duration = Date.now() - startTime;
        console.log(`✅ 网络请求成功 [${requestId}] (${duration}ms):`, res);

        if (originalSuccess) {
          originalSuccess(res);
        }
      };

      options.fail = (error) => {
        const duration = Date.now() - startTime;
        console.error(`❌ 网络请求失败 [${requestId}] (${duration}ms):`, error);

        if (originalFail) {
          originalFail(error);
        }
      };

      options.complete = (res) => {
        if (originalComplete) {
          originalComplete(res);
        }
      };

      return originalRequest(options);
    };
  }

  /**
   * 添加调试面板
   */
  addDebugPanel() {
    // 在开发环境中可以添加浮动调试面板
    console.log("🔧 调试面板已准备就绪");
    console.log("可用的调试命令:");
    console.log("- debug.showLogs() - 显示日志");
    console.log("- debug.showPerformance() - 显示性能数据");
    console.log("- debug.showStorage() - 显示存储信息");
    console.log("- debug.clearData() - 清空所有数据");
    console.log("- perf.start(name) - 开始性能监控");
    console.log("- perf.end(name) - 结束性能监控");
    console.log("- storage.get(key) - 获取存储");
    console.log("- page.current() - 获取当前页面");
  }

  /**
   * 显示日志
   */
  showLogs(filter = {}) {
    const logs = logger.getLogs(filter);
    console.table(
      logs.map((log) => ({
        时间: new Date(log.timestamp).toLocaleString(),
        级别: log.levelName,
        消息: log.message,
        分类: log.category,
      }))
    );
  }

  /**
   * 显示性能数据
   */
  showPerformance() {
    const metrics = performanceManager.getMetrics();
    console.table(
      Object.entries(metrics).map(([name, data]) => ({
        名称: name,
        次数: data.count,
        平均耗时: `${data.avgDuration.toFixed(2)}ms`,
        最小耗时: `${data.minDuration}ms`,
        最大耗时: `${data.maxDuration}ms`,
      }))
    );
  }

  /**
   * 显示存储信息
   */
  showStorage() {
    try {
      const info = wx.getStorageInfoSync();
      console.log("📦 存储信息:", info);

      const keys = info.keys;
      const data = {};

      keys.forEach((key) => {
        try {
          const value = wx.getStorageSync(key);
          data[key] = {
            类型: typeof value,
            大小: JSON.stringify(value).length + " 字符",
            值: value,
          };
        } catch (error) {
          data[key] = { 错误: error.message };
        }
      });

      console.table(data);
    } catch (error) {
      console.error("获取存储信息失败:", error);
    }
  }

  /**
   * 清空所有数据
   */
  clearData() {
    wx.showModal({
      title: "确认清空",
      content: "确定要清空所有数据吗？此操作不可恢复！",
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync();
          console.log("🗑️ 所有数据已清空");
        }
      },
    });
  }

  /**
   * 生成ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  /**
   * 格式化输出
   */
  formatOutput(data) {
    if (typeof data === "object") {
      return JSON.stringify(data, null, 2);
    }
    return String(data);
  }

  /**
   * 添加调试过滤器
   */
  addFilter(filter) {
    this.debugFilters.push(filter);
  }

  /**
   * 移除调试过滤器
   */
  removeFilter(filter) {
    const index = this.debugFilters.indexOf(filter);
    if (index > -1) {
      this.debugFilters.splice(index, 1);
    }
  }

  /**
   * 检查是否应该输出
   */
  shouldOutput(category, level) {
    if (level < this.debugLevel) {
      return false;
    }

    if (this.debugFilters.length > 0) {
      return this.debugFilters.some((filter) => filter(category, level));
    }

    return true;
  }
}

/**
 * 断言工具
 */
class AssertionTool {
  /**
   * 断言为真
   */
  static assertTrue(condition, message = "断言失败") {
    if (!condition) {
      const error = new Error(message);
      logger.error(
        "断言失败",
        { condition, message, stack: error.stack },
        "assertion"
      );
      throw error;
    }
  }

  /**
   * 断言为假
   */
  static assertFalse(condition, message = "断言失败") {
    this.assertTrue(!condition, message);
  }

  /**
   * 断言相等
   */
  static assertEqual(actual, expected, message = "断言失败：值不相等") {
    if (actual !== expected) {
      const error = new Error(`${message}. 期望: ${expected}, 实际: ${actual}`);
      logger.error("断言失败", { actual, expected, message }, "assertion");
      throw error;
    }
  }

  /**
   * 断言不相等
   */
  static assertNotEqual(actual, expected, message = "断言失败：值相等") {
    if (actual === expected) {
      const error = new Error(`${message}. 值: ${actual}`);
      logger.error("断言失败", { actual, expected, message }, "assertion");
      throw error;
    }
  }

  /**
   * 断言为null
   */
  static assertNull(value, message = "断言失败：值不为null") {
    this.assertEqual(value, null, message);
  }

  /**
   * 断言不为null
   */
  static assertNotNull(value, message = "断言失败：值为null") {
    this.assertNotEqual(value, null, message);
  }

  /**
   * 断言类型
   */
  static assertType(value, type, message = "断言失败：类型不匹配") {
    const actualType = typeof value;
    if (actualType !== type) {
      const error = new Error(
        `${message}. 期望类型: ${type}, 实际类型: ${actualType}`
      );
      logger.error(
        "断言失败",
        { value, expectedType: type, actualType, message },
        "assertion"
      );
      throw error;
    }
  }

  /**
   * 断言数组包含
   */
  static assertContains(array, item, message = "断言失败：数组不包含指定项") {
    if (!Array.isArray(array) || !array.includes(item)) {
      const error = new Error(
        `${message}. 数组: ${JSON.stringify(array)}, 项: ${item}`
      );
      logger.error("断言失败", { array, item, message }, "assertion");
      throw error;
    }
  }

  /**
   * 断言抛出异常
   */
  static async assertThrows(fn, message = "断言失败：未抛出异常") {
    try {
      await fn();
      const error = new Error(message);
      logger.error("断言失败", { message }, "assertion");
      throw error;
    } catch (error) {
      // 预期的异常
      return error;
    }
  }
}

// 创建全局实例
const debugManager = new DebugManager();

export { DebugManager, AssertionTool, debugManager };
