// utils/compatibility.js
// 兼容性检测和测试工具

/**
 * 设备兼容性检测器
 */
class DeviceCompatibility {
  
  constructor() {
    this.systemInfo = null
    this.capabilities = null
    this.init()
  }
  
  /**
   * 初始化设备信息
   */
  init() {
    try {
      this.systemInfo = wx.getSystemInfoSync()
      this.detectCapabilities()
    } catch (error) {
      console.error('获取系统信息失败:', error)
    }
  }
  
  /**
   * 检测设备能力
   */
  detectCapabilities() {
    this.capabilities = {
      // 基础能力
      storage: this.checkStorageSupport(),
      camera: this.checkCameraSupport(),
      location: this.checkLocationSupport(),
      bluetooth: this.checkBluetoothSupport(),
      nfc: this.checkNFCSupport(),
      
      // 传感器
      accelerometer: this.checkAccelerometerSupport(),
      gyroscope: this.checkGyroscopeSupport(),
      compass: this.checkCompassSupport(),
      
      // 网络
      wifi: this.checkWiFiSupport(),
      cellular: this.checkCellularSupport(),
      
      // 音频视频
      audio: this.checkAudioSupport(),
      video: this.checkVideoSupport(),
      
      // 支付
      payment: this.checkPaymentSupport(),
      
      // 生物识别
      fingerprint: this.checkFingerprintSupport(),
      faceId: this.checkFaceIdSupport()
    }
  }
  
  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    if (!this.systemInfo) return null
    
    return {
      platform: this.systemInfo.platform,
      system: this.systemInfo.system,
      version: this.systemInfo.version,
      model: this.systemInfo.model,
      brand: this.systemInfo.brand,
      screenWidth: this.systemInfo.screenWidth,
      screenHeight: this.systemInfo.screenHeight,
      pixelRatio: this.systemInfo.pixelRatio,
      windowWidth: this.systemInfo.windowWidth,
      windowHeight: this.systemInfo.windowHeight,
      statusBarHeight: this.systemInfo.statusBarHeight,
      safeArea: this.systemInfo.safeArea,
      language: this.systemInfo.language,
      fontSizeSetting: this.systemInfo.fontSizeSetting,
      SDKVersion: this.systemInfo.SDKVersion,
      benchmarkLevel: this.systemInfo.benchmarkLevel
    }
  }
  
  /**
   * 检查存储支持
   */
  checkStorageSupport() {
    try {
      wx.setStorageSync('test_key', 'test_value')
      const value = wx.getStorageSync('test_key')
      wx.removeStorageSync('test_key')
      return value === 'test_value'
    } catch (error) {
      return false
    }
  }
  
  /**
   * 检查相机支持
   */
  checkCameraSupport() {
    return typeof wx.chooseImage === 'function' || typeof wx.chooseMedia === 'function'
  }
  
  /**
   * 检查定位支持
   */
  checkLocationSupport() {
    return typeof wx.getLocation === 'function'
  }
  
  /**
   * 检查蓝牙支持
   */
  checkBluetoothSupport() {
    return typeof wx.openBluetoothAdapter === 'function'
  }
  
  /**
   * 检查NFC支持
   */
  checkNFCSupport() {
    return typeof wx.startHCE === 'function'
  }
  
  /**
   * 检查加速度计支持
   */
  checkAccelerometerSupport() {
    return typeof wx.startAccelerometer === 'function'
  }
  
  /**
   * 检查陀螺仪支持
   */
  checkGyroscopeSupport() {
    return typeof wx.startGyroscope === 'function'
  }
  
  /**
   * 检查指南针支持
   */
  checkCompassSupport() {
    return typeof wx.startCompass === 'function'
  }
  
  /**
   * 检查WiFi支持
   */
  checkWiFiSupport() {
    return typeof wx.startWifi === 'function'
  }
  
  /**
   * 检查蜂窝网络支持
   */
  checkCellularSupport() {
    return this.systemInfo && this.systemInfo.platform !== 'devtools'
  }
  
  /**
   * 检查音频支持
   */
  checkAudioSupport() {
    return typeof wx.createAudioContext === 'function' || typeof wx.createInnerAudioContext === 'function'
  }
  
  /**
   * 检查视频支持
   */
  checkVideoSupport() {
    return typeof wx.createVideoContext === 'function'
  }
  
  /**
   * 检查支付支持
   */
  checkPaymentSupport() {
    return typeof wx.requestPayment === 'function'
  }
  
  /**
   * 检查指纹支持
   */
  checkFingerprintSupport() {
    return typeof wx.startSoterAuthentication === 'function'
  }
  
  /**
   * 检查面容ID支持
   */
  checkFaceIdSupport() {
    return typeof wx.startSoterAuthentication === 'function'
  }
  
  /**
   * 检查是否为低端设备
   */
  isLowEndDevice() {
    if (!this.systemInfo) return false
    
    // 基于benchmarkLevel判断
    if (this.systemInfo.benchmarkLevel !== undefined) {
      return this.systemInfo.benchmarkLevel < 30
    }
    
    // 基于内存判断
    if (this.systemInfo.memorySize !== undefined) {
      return this.systemInfo.memorySize < 3000 // 小于3GB
    }
    
    // 基于屏幕分辨率判断
    const totalPixels = this.systemInfo.screenWidth * this.systemInfo.screenHeight
    return totalPixels < 1920 * 1080
  }
  
  /**
   * 获取性能等级
   */
  getPerformanceLevel() {
    if (!this.systemInfo) return 'unknown'
    
    if (this.systemInfo.benchmarkLevel !== undefined) {
      if (this.systemInfo.benchmarkLevel >= 50) return 'high'
      if (this.systemInfo.benchmarkLevel >= 30) return 'medium'
      return 'low'
    }
    
    // 基于其他指标判断
    if (this.isLowEndDevice()) return 'low'
    return 'medium'
  }
  
  /**
   * 检查API版本兼容性
   */
  checkAPICompatibility(apiName, requiredVersion) {
    if (!this.systemInfo || !this.systemInfo.SDKVersion) return false
    
    const currentVersion = this.systemInfo.SDKVersion
    return this.compareVersion(currentVersion, requiredVersion) >= 0
  }
  
  /**
   * 版本比较
   */
  compareVersion(version1, version2) {
    const v1 = version1.split('.').map(Number)
    const v2 = version2.split('.').map(Number)
    
    for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
      const num1 = v1[i] || 0
      const num2 = v2[i] || 0
      
      if (num1 > num2) return 1
      if (num1 < num2) return -1
    }
    
    return 0
  }
  
  /**
   * 生成兼容性报告
   */
  generateReport() {
    return {
      deviceInfo: this.getDeviceInfo(),
      capabilities: this.capabilities,
      performanceLevel: this.getPerformanceLevel(),
      isLowEndDevice: this.isLowEndDevice(),
      timestamp: Date.now()
    }
  }
}

/**
 * 功能测试器
 */
class FeatureTester {
  
  constructor() {
    this.testResults = new Map()
  }
  
  /**
   * 运行所有测试
   */
  async runAllTests() {
    const tests = [
      { name: 'storage', test: this.testStorage },
      { name: 'network', test: this.testNetwork },
      { name: 'ui', test: this.testUI },
      { name: 'performance', test: this.testPerformance },
      { name: 'data', test: this.testDataProcessing }
    ]
    
    const results = {}
    
    for (const { name, test } of tests) {
      try {
        console.log(`开始测试: ${name}`)
        const result = await test.call(this)
        results[name] = { success: true, result }
        console.log(`测试通过: ${name}`)
      } catch (error) {
        results[name] = { success: false, error: error.message }
        console.error(`测试失败: ${name}`, error)
      }
    }
    
    return results
  }
  
  /**
   * 测试存储功能
   */
  async testStorage() {
    const testData = {
      string: 'test string',
      number: 12345,
      object: { key: 'value', nested: { data: true } },
      array: [1, 2, 3, 'test']
    }
    
    // 测试同步存储
    for (const [key, value] of Object.entries(testData)) {
      wx.setStorageSync(`test_${key}`, value)
      const retrieved = wx.getStorageSync(`test_${key}`)
      
      if (JSON.stringify(retrieved) !== JSON.stringify(value)) {
        throw new Error(`同步存储测试失败: ${key}`)
      }
      
      wx.removeStorageSync(`test_${key}`)
    }
    
    // 测试异步存储
    await new Promise((resolve, reject) => {
      wx.setStorage({
        key: 'test_async',
        data: testData.object,
        success: resolve,
        fail: reject
      })
    })
    
    const asyncData = await new Promise((resolve, reject) => {
      wx.getStorage({
        key: 'test_async',
        success: (res) => resolve(res.data),
        fail: reject
      })
    })
    
    if (JSON.stringify(asyncData) !== JSON.stringify(testData.object)) {
      throw new Error('异步存储测试失败')
    }
    
    wx.removeStorageSync('test_async')
    
    return { message: '存储功能测试通过' }
  }
  
  /**
   * 测试网络功能
   */
  async testNetwork() {
    // 测试网络状态
    const networkType = await new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => resolve(res.networkType),
        fail: () => resolve('unknown')
      })
    })
    
    if (networkType === 'none') {
      throw new Error('无网络连接')
    }
    
    // 测试简单请求（这里使用一个公共API）
    try {
      await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://httpbin.org/get',
          method: 'GET',
          timeout: 5000,
          success: resolve,
          fail: reject
        })
      })
    } catch (error) {
      throw new Error('网络请求测试失败')
    }
    
    return { networkType, message: '网络功能测试通过' }
  }
  
  /**
   * 测试UI功能
   */
  async testUI() {
    // 测试Toast
    wx.showToast({
      title: 'UI测试',
      icon: 'none',
      duration: 1000
    })
    
    await new Promise(resolve => setTimeout(resolve, 1100))
    
    // 测试Loading
    wx.showLoading({ title: '测试中...' })
    await new Promise(resolve => setTimeout(resolve, 500))
    wx.hideLoading()
    
    return { message: 'UI功能测试通过' }
  }
  
  /**
   * 测试性能
   */
  async testPerformance() {
    const startTime = Date.now()
    
    // CPU密集型测试
    let sum = 0
    for (let i = 0; i < 100000; i++) {
      sum += Math.sqrt(i)
    }
    
    const cpuTime = Date.now() - startTime
    
    // 内存测试
    const largeArray = new Array(10000).fill(0).map((_, i) => ({ id: i, data: Math.random() }))
    const memoryTime = Date.now() - startTime - cpuTime
    
    // 清理
    largeArray.length = 0
    
    return {
      cpuTime,
      memoryTime,
      message: '性能测试通过'
    }
  }
  
  /**
   * 测试数据处理
   */
  async testDataProcessing() {
    const testData = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random() * 100,
      category: i % 5
    }))
    
    // 测试排序
    const sorted = [...testData].sort((a, b) => a.value - b.value)
    
    // 测试过滤
    const filtered = testData.filter(item => item.value > 50)
    
    // 测试分组
    const grouped = testData.reduce((groups, item) => {
      const key = item.category
      if (!groups[key]) groups[key] = []
      groups[key].push(item)
      return groups
    }, {})
    
    // 测试JSON序列化
    const serialized = JSON.stringify(testData)
    const deserialized = JSON.parse(serialized)
    
    if (deserialized.length !== testData.length) {
      throw new Error('JSON序列化测试失败')
    }
    
    return {
      originalCount: testData.length,
      sortedCount: sorted.length,
      filteredCount: filtered.length,
      groupCount: Object.keys(grouped).length,
      message: '数据处理测试通过'
    }
  }
  
  /**
   * 边界测试
   */
  async testBoundaryConditions() {
    const tests = []
    
    // 测试空数据
    tests.push(this.testEmptyData())
    
    // 测试大数据
    tests.push(this.testLargeData())
    
    // 测试特殊字符
    tests.push(this.testSpecialCharacters())
    
    // 测试网络异常
    tests.push(this.testNetworkError())
    
    const results = await Promise.allSettled(tests)
    
    return results.map((result, index) => ({
      test: ['empty_data', 'large_data', 'special_chars', 'network_error'][index],
      success: result.status === 'fulfilled',
      result: result.status === 'fulfilled' ? result.value : result.reason.message
    }))
  }
  
  /**
   * 测试空数据
   */
  async testEmptyData() {
    const emptyTests = [
      { data: null, name: 'null' },
      { data: undefined, name: 'undefined' },
      { data: '', name: 'empty string' },
      { data: [], name: 'empty array' },
      { data: {}, name: 'empty object' }
    ]
    
    for (const test of emptyTests) {
      try {
        wx.setStorageSync('empty_test', test.data)
        const retrieved = wx.getStorageSync('empty_test')
        wx.removeStorageSync('empty_test')
      } catch (error) {
        throw new Error(`空数据测试失败: ${test.name}`)
      }
    }
    
    return { message: '空数据测试通过' }
  }
  
  /**
   * 测试大数据
   */
  async testLargeData() {
    const largeData = Array.from({ length: 10000 }, (_, i) => ({
      id: i,
      data: 'x'.repeat(100) // 每项100字符
    }))
    
    try {
      wx.setStorageSync('large_test', largeData)
      const retrieved = wx.getStorageSync('large_test')
      
      if (retrieved.length !== largeData.length) {
        throw new Error('大数据存储不完整')
      }
      
      wx.removeStorageSync('large_test')
    } catch (error) {
      throw new Error(`大数据测试失败: ${error.message}`)
    }
    
    return { message: '大数据测试通过' }
  }
  
  /**
   * 测试特殊字符
   */
  async testSpecialCharacters() {
    const specialChars = [
      '中文测试',
      'emoji 🎉🚀💻',
      'special chars: !@#$%^&*()',
      'quotes: "double" \'single\'',
      'newlines:\ntest\r\ntest',
      'unicode: \u4e2d\u6587'
    ]
    
    for (const char of specialChars) {
      try {
        wx.setStorageSync('special_test', char)
        const retrieved = wx.getStorageSync('special_test')
        
        if (retrieved !== char) {
          throw new Error(`特殊字符测试失败: ${char}`)
        }
        
        wx.removeStorageSync('special_test')
      } catch (error) {
        throw new Error(`特殊字符处理失败: ${char}`)
      }
    }
    
    return { message: '特殊字符测试通过' }
  }
  
  /**
   * 测试网络错误
   */
  async testNetworkError() {
    try {
      await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://invalid-domain-for-testing.com',
          timeout: 1000,
          success: resolve,
          fail: reject
        })
      })
      
      throw new Error('网络错误测试失败：应该失败但成功了')
    } catch (error) {
      // 预期的错误
      return { message: '网络错误处理测试通过' }
    }
  }
}

// 创建全局实例
const deviceCompatibility = new DeviceCompatibility()
const featureTester = new FeatureTester()

export {
  DeviceCompatibility,
  FeatureTester,
  deviceCompatibility,
  featureTester
}
