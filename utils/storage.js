// utils/storage.js
// 本地存储工具类

import { STORAGE_KEYS, ERROR_CODES, ERROR_MESSAGES } from '../data/constants.js'

/**
 * 存储操作结果类
 */
class StorageResult {
  constructor(success = false, data = null, error = null) {
    this.success = success
    this.data = data
    this.error = error
  }

  static success(data) {
    return new StorageResult(true, data, null)
  }

  static error(error) {
    return new StorageResult(false, null, error)
  }
}

/**
 * 本地存储工具类
 */
export class StorageManager {
  constructor() {
    this.isAvailable = this.checkAvailability()
  }

  /**
   * 检查存储是否可用
   */
  checkAvailability() {
    try {
      const testKey = '__storage_test__'
      wx.setStorageSync(testKey, 'test')
      wx.removeStorageSync(testKey)
      return true
    } catch (error) {
      console.error('Storage not available:', error)
      return false
    }
  }

  /**
   * 存储数据
   */
  set(key, value) {
    try {
      if (!this.isAvailable) {
        throw new Error(ERROR_MESSAGES.STORAGE_FAILED)
      }

      // 数据验证
      if (key === null || key === undefined || key === '') {
        throw new Error('存储键不能为空')
      }

      // 序列化数据
      const serializedData = {
        value: value,
        timestamp: Date.now(),
        version: '1.0'
      }

      wx.setStorageSync(key, serializedData)
      return StorageResult.success(value)
    } catch (error) {
      console.error('Storage set error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || ERROR_MESSAGES.STORAGE_FAILED
      })
    }
  }

  /**
   * 获取数据
   */
  get(key, defaultValue = null) {
    try {
      if (!this.isAvailable) {
        throw new Error(ERROR_MESSAGES.STORAGE_FAILED)
      }

      const data = wx.getStorageSync(key)
      
      if (!data) {
        return StorageResult.success(defaultValue)
      }

      // 检查数据格式
      if (typeof data === 'object' && data.value !== undefined) {
        return StorageResult.success(data.value)
      }

      // 兼容旧格式数据
      return StorageResult.success(data)
    } catch (error) {
      console.error('Storage get error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || ERROR_MESSAGES.STORAGE_FAILED
      })
    }
  }

  /**
   * 删除数据
   */
  remove(key) {
    try {
      if (!this.isAvailable) {
        throw new Error(ERROR_MESSAGES.STORAGE_FAILED)
      }

      wx.removeStorageSync(key)
      return StorageResult.success(true)
    } catch (error) {
      console.error('Storage remove error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || ERROR_MESSAGES.STORAGE_FAILED
      })
    }
  }

  /**
   * 清空所有数据
   */
  clear() {
    try {
      if (!this.isAvailable) {
        throw new Error(ERROR_MESSAGES.STORAGE_FAILED)
      }

      wx.clearStorageSync()
      return StorageResult.success(true)
    } catch (error) {
      console.error('Storage clear error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || ERROR_MESSAGES.STORAGE_FAILED
      })
    }
  }

  /**
   * 获取存储信息
   */
  getInfo() {
    try {
      const info = wx.getStorageInfoSync()
      return StorageResult.success(info)
    } catch (error) {
      console.error('Storage getInfo error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || ERROR_MESSAGES.STORAGE_FAILED
      })
    }
  }

  /**
   * 检查键是否存在
   */
  has(key) {
    const result = this.get(key)
    return result.success && result.data !== null
  }

  /**
   * 批量设置数据
   */
  setBatch(dataMap) {
    const results = {}
    const errors = []

    for (const [key, value] of Object.entries(dataMap)) {
      const result = this.set(key, value)
      results[key] = result
      
      if (!result.success) {
        errors.push({ key, error: result.error })
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors
    }
  }

  /**
   * 批量获取数据
   */
  getBatch(keys, defaultValues = {}) {
    const results = {}
    const errors = []

    keys.forEach(key => {
      const defaultValue = defaultValues[key] || null
      const result = this.get(key, defaultValue)
      results[key] = result
      
      if (!result.success) {
        errors.push({ key, error: result.error })
      }
    })

    return {
      success: errors.length === 0,
      results,
      errors
    }
  }

  /**
   * 数据备份
   */
  backup() {
    try {
      const info = this.getInfo()
      if (!info.success) {
        throw new Error('无法获取存储信息')
      }

      const backup = {
        timestamp: Date.now(),
        version: '1.0',
        data: {}
      }

      // 获取所有数据
      info.data.keys.forEach(key => {
        const result = this.get(key)
        if (result.success) {
          backup.data[key] = result.data
        }
      })

      return StorageResult.success(backup)
    } catch (error) {
      console.error('Storage backup error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || '备份失败'
      })
    }
  }

  /**
   * 数据恢复
   */
  restore(backupData) {
    try {
      if (!backupData || !backupData.data) {
        throw new Error('备份数据格式不正确')
      }

      const results = this.setBatch(backupData.data)
      
      if (results.success) {
        return StorageResult.success(true)
      } else {
        throw new Error('部分数据恢复失败')
      }
    } catch (error) {
      console.error('Storage restore error:', error)
      return StorageResult.error({
        code: ERROR_CODES.STORAGE_ERROR,
        message: error.message || '恢复失败'
      })
    }
  }
}

/**
 * 工作记录存储管理器
 */
export class WorkRecordStorage {
  constructor() {
    this.storage = new StorageManager()
    this.key = STORAGE_KEYS.WORK_RECORDS
  }

  /**
   * 获取所有工作记录
   */
  getAll() {
    const result = this.storage.get(this.key, [])
    if (result.success) {
      return StorageResult.success(result.data || [])
    }
    return result
  }

  /**
   * 保存工作记录
   */
  save(record) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const records = result.data
    const existingIndex = records.findIndex(r => r.id === record.id)
    
    if (existingIndex >= 0) {
      records[existingIndex] = record
    } else {
      records.push(record)
    }

    return this.storage.set(this.key, records)
  }

  /**
   * 删除工作记录
   */
  delete(recordId) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const records = result.data.filter(r => r.id !== recordId)
    return this.storage.set(this.key, records)
  }

  /**
   * 根据ID获取工作记录
   */
  getById(recordId) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const record = result.data.find(r => r.id === recordId)
    if (record) {
      return StorageResult.success(record)
    } else {
      return StorageResult.error({
        code: ERROR_CODES.DATA_NOT_FOUND,
        message: ERROR_MESSAGES.WORK_RECORD_NOT_FOUND
      })
    }
  }

  /**
   * 根据日期获取工作记录
   */
  getByDate(date) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const records = result.data.filter(r => r.date === date)
    return StorageResult.success(records)
  }

  /**
   * 根据工作类型获取工作记录
   */
  getByWorkType(workTypeId) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const records = result.data.filter(r => r.workTypeId === workTypeId)
    return StorageResult.success(records)
  }

  /**
   * 批量保存工作记录
   */
  saveBatch(records) {
    return this.storage.set(this.key, records)
  }

  /**
   * 清空所有工作记录
   */
  clear() {
    return this.storage.remove(this.key)
  }
}

/**
 * 工作类型存储管理器
 */
export class WorkTypeStorage {
  constructor() {
    this.storage = new StorageManager()
    this.key = STORAGE_KEYS.WORK_TYPES
  }

  /**
   * 获取所有工作类型
   */
  getAll() {
    const result = this.storage.get(this.key, [])
    if (result.success) {
      return StorageResult.success(result.data || [])
    }
    return result
  }

  /**
   * 保存工作类型
   */
  save(workType) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const workTypes = result.data
    const existingIndex = workTypes.findIndex(wt => wt.id === workType.id)
    
    if (existingIndex >= 0) {
      workTypes[existingIndex] = workType
    } else {
      workTypes.push(workType)
    }

    return this.storage.set(this.key, workTypes)
  }

  /**
   * 删除工作类型
   */
  delete(workTypeId) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const workTypes = result.data.filter(wt => wt.id !== workTypeId)
    return this.storage.set(this.key, workTypes)
  }

  /**
   * 根据ID获取工作类型
   */
  getById(workTypeId) {
    const result = this.getAll()
    if (!result.success) {
      return result
    }

    const workType = result.data.find(wt => wt.id === workTypeId)
    if (workType) {
      return StorageResult.success(workType)
    } else {
      return StorageResult.error({
        code: ERROR_CODES.DATA_NOT_FOUND,
        message: ERROR_MESSAGES.WORK_TYPE_NOT_FOUND
      })
    }
  }

  /**
   * 批量保存工作类型
   */
  saveBatch(workTypes) {
    return this.storage.set(this.key, workTypes)
  }

  /**
   * 清空所有工作类型
   */
  clear() {
    return this.storage.remove(this.key)
  }
}

// 导出单例实例
export const storageManager = new StorageManager()
export const workRecordStorage = new WorkRecordStorage()
export const workTypeStorage = new WorkTypeStorage()
