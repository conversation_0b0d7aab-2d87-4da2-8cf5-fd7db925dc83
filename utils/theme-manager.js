/**
 * 主题管理器
 * 负责应用主题的切换和管理
 */

/**
 * 主题配置
 */
const THEMES = {
  light: {
    name: '浅色模式',
    icon: '☀️',
    colors: {
      primary: '#007AFF',
      secondary: '#5AC8FA',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#333333',
      textSecondary: '#666666',
      border: '#e5e5e5',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30'
    }
  },
  dark: {
    name: '深色模式',
    icon: '🌙',
    colors: {
      primary: '#0A84FF',
      secondary: '#64D2FF',
      background: '#000000',
      surface: '#1c1c1e',
      text: '#ffffff',
      textSecondary: '#8e8e93',
      border: '#38383a',
      success: '#30D158',
      warning: '#FF9F0A',
      error: '#FF453A'
    }
  },
  auto: {
    name: '跟随系统',
    icon: '🔄',
    colors: null // 动态获取
  }
}

/**
 * 主题管理器类
 */
class ThemeManager {
  constructor() {
    this.currentTheme = 'auto'
    this.systemTheme = 'light'
    this.listeners = []
    
    this.init()
  }

  /**
   * 初始化主题管理器
   */
  init() {
    // 获取系统主题
    this.detectSystemTheme()
    
    // 加载保存的主题设置
    this.loadThemeSettings()
    
    // 应用主题
    this.applyTheme()
    
    // 监听系统主题变化
    this.watchSystemTheme()
  }

  /**
   * 检测系统主题
   */
  detectSystemTheme() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.systemTheme = systemInfo.theme || 'light'
    } catch (error) {
      console.warn('获取系统主题失败:', error)
      this.systemTheme = 'light'
    }
  }

  /**
   * 监听系统主题变化
   */
  watchSystemTheme() {
    wx.onThemeChange && wx.onThemeChange((res) => {
      this.systemTheme = res.theme
      if (this.currentTheme === 'auto') {
        this.applyTheme()
        this.notifyListeners()
      }
    })
  }

  /**
   * 加载主题设置
   */
  loadThemeSettings() {
    try {
      const settings = wx.getStorageSync('appSettings') || {}
      this.currentTheme = settings.theme || 'auto'
    } catch (error) {
      console.warn('加载主题设置失败:', error)
      this.currentTheme = 'auto'
    }
  }

  /**
   * 保存主题设置
   */
  saveThemeSettings() {
    try {
      const settings = wx.getStorageSync('appSettings') || {}
      settings.theme = this.currentTheme
      wx.setStorageSync('appSettings', settings)
    } catch (error) {
      console.error('保存主题设置失败:', error)
    }
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称 ('light' | 'dark' | 'auto')
   */
  setTheme(theme) {
    if (!THEMES[theme]) {
      console.warn('不支持的主题:', theme)
      return false
    }

    this.currentTheme = theme
    this.saveThemeSettings()
    this.applyTheme()
    this.notifyListeners()
    
    return true
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题名称
   */
  getCurrentTheme() {
    return this.currentTheme
  }

  /**
   * 获取实际应用的主题
   * @returns {string} 实际主题名称 ('light' | 'dark')
   */
  getActiveTheme() {
    if (this.currentTheme === 'auto') {
      return this.systemTheme
    }
    return this.currentTheme
  }

  /**
   * 获取主题配置
   * @param {string} theme - 主题名称，不传则获取当前主题
   * @returns {Object} 主题配置
   */
  getThemeConfig(theme = null) {
    const targetTheme = theme || this.getActiveTheme()
    return THEMES[targetTheme] || THEMES.light
  }

  /**
   * 获取主题颜色
   * @param {string} colorName - 颜色名称
   * @returns {string} 颜色值
   */
  getThemeColor(colorName) {
    const config = this.getThemeConfig()
    return config.colors[colorName] || '#000000'
  }

  /**
   * 应用主题
   */
  applyTheme() {
    const activeTheme = this.getActiveTheme()
    const config = this.getThemeConfig(activeTheme)
    
    // 设置页面样式变量
    this.setCSSVariables(config.colors)
    
    // 设置状态栏样式
    this.setStatusBarStyle(activeTheme)
  }

  /**
   * 设置CSS变量
   * @param {Object} colors - 颜色配置
   */
  setCSSVariables(colors) {
    if (!colors) return
    
    try {
      // 在小程序中，我们通过全局样式类来实现主题切换
      const app = getApp()
      if (app.globalData) {
        app.globalData.themeColors = colors
      }
    } catch (error) {
      console.warn('设置主题变量失败:', error)
    }
  }

  /**
   * 设置状态栏样式
   * @param {string} theme - 主题名称
   */
  setStatusBarStyle(theme) {
    try {
      wx.setNavigationBarColor({
        frontColor: theme === 'dark' ? '#ffffff' : '#000000',
        backgroundColor: theme === 'dark' ? '#000000' : '#ffffff',
        animation: {
          duration: 300,
          timingFunc: 'easeInOut'
        }
      })
    } catch (error) {
      console.warn('设置状态栏样式失败:', error)
    }
  }

  /**
   * 添加主题变化监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    if (typeof listener === 'function') {
      this.listeners.push(listener)
    }
  }

  /**
   * 移除主题变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知监听器
   */
  notifyListeners() {
    const activeTheme = this.getActiveTheme()
    const config = this.getThemeConfig(activeTheme)
    
    this.listeners.forEach(listener => {
      try {
        listener({
          theme: activeTheme,
          config,
          colors: config.colors
        })
      } catch (error) {
        console.warn('主题监听器执行失败:', error)
      }
    })
  }

  /**
   * 获取所有可用主题
   * @returns {Array} 主题列表
   */
  getAvailableThemes() {
    return Object.keys(THEMES).map(key => ({
      value: key,
      name: THEMES[key].name,
      icon: THEMES[key].icon
    }))
  }

  /**
   * 切换主题（在当前主题和另一个主题之间切换）
   * @param {string} alternateTheme - 备选主题，默认为 'dark'
   */
  toggleTheme(alternateTheme = 'dark') {
    const currentActive = this.getActiveTheme()
    const newTheme = currentActive === 'light' ? alternateTheme : 'light'
    this.setTheme(newTheme)
  }

  /**
   * 重置为默认主题
   */
  resetTheme() {
    this.setTheme('auto')
  }
}

// 创建全局实例
const themeManager = new ThemeManager()

export default themeManager
