// utils/performance.js
// 性能监控和优化工具

/**
 * 性能监控管理器
 */
class PerformanceManager {
  constructor() {
    this.metrics = new Map();
    this.timers = new Map();
    this.memoryUsage = [];
    this.isEnabled = true;
  }

  /**
   * 启用性能监控
   */
  enable() {
    this.isEnabled = true;
    this.startMemoryMonitoring();
  }

  /**
   * 禁用性能监控
   */
  disable() {
    this.isEnabled = false;
    this.stopMemoryMonitoring();
  }

  /**
   * 开始计时
   */
  startTimer(name) {
    if (!this.isEnabled) return;

    this.timers.set(name, {
      startTime: Date.now(),
      startMemory: this.getCurrentMemoryUsage(),
    });
  }

  /**
   * 结束计时并记录
   */
  endTimer(name) {
    if (!this.isEnabled || !this.timers.has(name)) return;

    const timer = this.timers.get(name);
    const endTime = Date.now();
    const endMemory = this.getCurrentMemoryUsage();

    const metric = {
      name,
      duration: endTime - timer.startTime,
      memoryDelta: endMemory - timer.startMemory,
      timestamp: endTime,
    };

    this.recordMetric(metric);
    this.timers.delete(name);

    return metric;
  }

  /**
   * 记录性能指标
   */
  recordMetric(metric) {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const metrics = this.metrics.get(metric.name);
    metrics.push(metric);

    // 只保留最近100条记录
    if (metrics.length > 100) {
      metrics.shift();
    }

    // 如果性能较差，输出警告
    if (metric.duration > 1000) {
      console.warn(`性能警告: ${metric.name} 耗时 ${metric.duration}ms`);
    }
  }

  /**
   * 获取性能统计
   */
  getMetrics(name) {
    if (name) {
      return this.metrics.get(name) || [];
    }

    const summary = {};
    for (const [metricName, metrics] of this.metrics) {
      summary[metricName] = this.calculateSummary(metrics);
    }

    return summary;
  }

  /**
   * 计算性能摘要
   */
  calculateSummary(metrics) {
    if (metrics.length === 0) return null;

    const durations = metrics.map((m) => m.duration);
    const memoryDeltas = metrics.map((m) => m.memoryDelta);

    return {
      count: metrics.length,
      avgDuration: this.average(durations),
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      avgMemoryDelta: this.average(memoryDeltas),
      lastExecution: metrics[metrics.length - 1].timestamp,
    };
  }

  /**
   * 计算平均值
   */
  average(arr) {
    return arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
  }

  /**
   * 获取当前内存使用情况
   */
  getCurrentMemoryUsage() {
    try {
      // 微信小程序中获取内存信息
      const systemInfo = wx.getSystemInfoSync();
      return systemInfo.memorySize || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 开始内存监控
   */
  startMemoryMonitoring() {
    if (this.memoryInterval) return;

    this.memoryInterval = setInterval(() => {
      const usage = this.getCurrentMemoryUsage();
      this.memoryUsage.push({
        usage,
        timestamp: Date.now(),
      });

      // 只保留最近50条记录
      if (this.memoryUsage.length > 50) {
        this.memoryUsage.shift();
      }

      // 内存使用过高警告
      if (usage > 500) {
        // 500MB
        console.warn(`内存使用过高: ${usage}MB`);
      }
    }, 10000); // 每10秒检查一次
  }

  /**
   * 停止内存监控
   */
  stopMemoryMonitoring() {
    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
      this.memoryInterval = null;
    }
  }

  /**
   * 获取内存使用趋势
   */
  getMemoryTrend() {
    return this.memoryUsage.slice();
  }

  /**
   * 清除所有性能数据
   */
  clear() {
    this.metrics.clear();
    this.timers.clear();
    this.memoryUsage = [];
  }

  /**
   * 导出性能报告
   */
  exportReport() {
    return {
      timestamp: Date.now(),
      metrics: Object.fromEntries(this.metrics),
      memoryTrend: this.memoryUsage,
      summary: this.getMetrics(),
    };
  }
}

/**
 * 性能优化工具
 */
class PerformanceOptimizer {
  /**
   * 防抖函数
   */
  static debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func.apply(this, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(this, args);
    };
  }

  /**
   * 节流函数
   */
  static throttle(func, limit) {
    let inThrottle;
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * 延迟执行
   */
  static defer(func) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const result = func();
        resolve(result);
      }, 0);
    });
  }

  /**
   * 批量处理
   */
  static async batch(items, batchSize = 10, processor, delay = 0) {
    const results = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map((item) => processor(item))
      );
      results.push(...batchResults);

      // 批次间延迟，避免阻塞UI
      if (delay > 0 && i + batchSize < items.length) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    return results;
  }

  /**
   * 内存清理
   */
  static cleanupMemory() {
    // 清理可能的内存泄漏
    if (typeof wx !== "undefined") {
      // 清理定时器
      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }

      // 清理事件监听器（如果有的话）
      // 这里可以添加具体的清理逻辑
    }
  }

  /**
   * 图片预加载
   */
  static preloadImages(urls) {
    return Promise.all(
      urls.map((url) => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(url);
          img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
          img.src = url;
        });
      })
    );
  }

  /**
   * 数据分页
   */
  static paginate(data, pageSize = 20) {
    const pages = [];
    for (let i = 0; i < data.length; i += pageSize) {
      pages.push(data.slice(i, i + pageSize));
    }
    return pages;
  }

  /**
   * 虚拟滚动数据计算
   */
  static calculateVirtualScroll(
    scrollTop,
    itemHeight,
    containerHeight,
    totalItems
  ) {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const endIndex = Math.min(startIndex + visibleCount + 1, totalItems);

    return {
      startIndex: Math.max(0, startIndex - 1),
      endIndex,
      visibleCount,
      offsetY: startIndex * itemHeight,
    };
  }
}

/**
 * 缓存管理器
 */
class CacheManager {
  constructor(maxSize = 100) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessTimes = new Map();
  }

  /**
   * 设置缓存
   */
  set(key, value, ttl = 0) {
    // 如果缓存已满，删除最久未访问的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    const item = {
      value,
      timestamp: Date.now(),
      ttl: ttl > 0 ? Date.now() + ttl : 0,
    };

    this.cache.set(key, item);
    this.accessTimes.set(key, Date.now());
  }

  /**
   * 获取缓存
   */
  get(key) {
    const item = this.cache.get(key);

    if (!item) return null;

    // 检查是否过期
    if (item.ttl > 0 && Date.now() > item.ttl) {
      this.delete(key);
      return null;
    }

    // 更新访问时间
    this.accessTimes.set(key, Date.now());
    return item.value;
  }

  /**
   * 删除缓存
   */
  delete(key) {
    this.cache.delete(key);
    this.accessTimes.delete(key);
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
    this.accessTimes.clear();
  }

  /**
   * LRU淘汰
   */
  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
    };
  }

  /**
   * 计算命中率
   */
  calculateHitRate() {
    // 这里可以实现更复杂的命中率计算
    return this.cache.size / this.maxSize;
  }
}

// 创建全局实例
const performanceManager = new PerformanceManager();
const cacheManager = new CacheManager();

// 自动启用性能监控
performanceManager.enable();

export {
  PerformanceManager,
  PerformanceOptimizer,
  CacheManager,
  performanceManager,
  cacheManager,
};
