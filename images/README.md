# 兼职工作管理小程序图标

## 概述

本目录包含微信小程序所需的所有图标文件和生成工具。

## 所需图标文件

根据 `app.json` 配置，需要以下 6 个图标文件：

### TabBar 图标

- `tab-home.png` - 首页图标（未选中状态）
- `tab-home-active.png` - 首页图标（选中状态）
- `tab-stats.png` - 统计图标（未选中状态）
- `tab-stats-active.png` - 统计图标（选中状态）
- `tab-profile.png` - 个人中心图标（未选中状态）
- `tab-profile-active.png` - 个人中心图标（选中状态）

## 图标规格

- **尺寸**: 81px × 81px
- **格式**: PNG
- **背景**: 透明
- **未选中颜色**: #7A7E83（中性灰）
- **选中颜色**: #007AFF（iOS 蓝）

## 生成方法

### 方法 1：使用 HTML 预览工具（推荐）

1. 在浏览器中打开 `icon-preview.html`
2. 预览所有图标设计
3. 点击"下载 PNG"按钮下载每个图标
4. 将文件重命名为对应的文件名

### 方法 2：使用 Node.js 脚本

```bash
# 进入images目录
cd images

# 安装依赖（可选，用于直接生成PNG）
npm install

# 生成SVG文件
npm run generate

# 如果安装了sharp，会自动生成PNG文件
# 否则需要手动转换SVG为PNG
```

### 方法 3：使用 ImageMagick 转换

```bash
# 安装ImageMagick（macOS）
brew install imagemagick

# 生成SVG文件
npm run generate

# 转换为PNG
npm run convert-icons
```

## 设计说明

### 首页图标

- 设计概念：房屋/工作台，代表工作管理的起点
- 视觉元素：简洁的房屋轮廓，带有工作区域

### 统计图标

- 设计概念：柱状图，代表数据分析和统计
- 视觉元素：四个不同高度的柱子，展现数据趋势

### 个人中心图标

- 设计概念：用户头像，代表个人信息和设置
- 视觉元素：圆形头像和身体轮廓

## 使用注意事项

1. 确保图标文件名与 `app.json` 中的路径完全一致
2. 图标应保持清晰的视觉效果，在不同设备上都能正常显示
3. 遵循微信小程序设计规范
4. 保持图标风格的一致性

## 文件结构

```
images/
├── README.md              # 说明文档
├── icon-designs.md        # 详细设计规范
├── icon-preview.html      # 图标预览工具
├── generate-icons.js      # 图标生成脚本
├── package.json          # Node.js配置
├── tab-home.png          # 首页图标（未选中）
├── tab-home-active.png   # 首页图标（选中）
├── tab-stats.png         # 统计图标（未选中）
├── tab-stats-active.png  # 统计图标（选中）
├── tab-profile.png       # 个人中心图标（未选中）
└── tab-profile-active.png # 个人中心图标（选中）
```
