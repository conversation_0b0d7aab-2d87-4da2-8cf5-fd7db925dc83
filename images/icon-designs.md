# 兼职工作管理小程序图标设计规范

## 设计概述
- **应用主题**：兼职工作管理
- **设计风格**：简洁、现代、专业
- **主色调**：#007AFF（iOS蓝）
- **辅助色**：#7A7E83（中性灰）
- **图标尺寸**：81px × 81px
- **格式**：PNG，透明背景

## 图标列表

### 1. 首页图标 (Home)
**文件名**：`tab-home.png` / `tab-home-active.png`
**设计概念**：房屋/工作台图标，代表工作的起点和管理中心

**未选中状态** (#7A7E83)：
```svg
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <path d="M40.5 15L20.25 30.375V60.75H33.75V45.5625H47.25V60.75H60.75V30.375L40.5 15Z" 
        fill="#7A7E83" stroke="#7A7E83" stroke-width="2" stroke-linejoin="round"/>
  <path d="M13.5 33.75L40.5 13.5L67.5 33.75" 
        stroke="#7A7E83" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
</svg>
```

**选中状态** (#007AFF)：
```svg
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <path d="M40.5 15L20.25 30.375V60.75H33.75V45.5625H47.25V60.75H60.75V30.375L40.5 15Z" 
        fill="#007AFF" stroke="#007AFF" stroke-width="2" stroke-linejoin="round"/>
  <path d="M13.5 33.75L40.5 13.5L67.5 33.75" 
        stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
</svg>
```

### 2. 统计图标 (Statistics)
**文件名**：`tab-stats.png` / `tab-stats-active.png`
**设计概念**：柱状图/趋势图，代表数据统计和分析

**未选中状态** (#7A7E83)：
```svg
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <rect x="20.25" y="40.5" width="8.1" height="27" fill="#7A7E83" rx="2"/>
  <rect x="33.75" y="27" width="8.1" height="40.5" fill="#7A7E83" rx="2"/>
  <rect x="47.25" y="33.75" width="8.1" height="33.75" fill="#7A7E83" rx="2"/>
  <rect x="60.75" y="20.25" width="8.1" height="47.25" fill="#7A7E83" rx="2"/>
  <path d="M13.5 67.5H67.5" stroke="#7A7E83" stroke-width="2" stroke-linecap="round"/>
</svg>
```

**选中状态** (#007AFF)：
```svg
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <rect x="20.25" y="40.5" width="8.1" height="27" fill="#007AFF" rx="2"/>
  <rect x="33.75" y="27" width="8.1" height="40.5" fill="#007AFF" rx="2"/>
  <rect x="47.25" y="33.75" width="8.1" height="33.75" fill="#007AFF" rx="2"/>
  <rect x="60.75" y="20.25" width="8.1" height="47.25" fill="#007AFF" rx="2"/>
  <path d="M13.5 67.5H67.5" stroke="#007AFF" stroke-width="2" stroke-linecap="round"/>
</svg>
```

### 3. 个人中心图标 (Profile)
**文件名**：`tab-profile.png` / `tab-profile-active.png`
**设计概念**：用户头像图标，代表个人信息和设置

**未选中状态** (#7A7E83)：
```svg
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="27" r="13.5" fill="none" stroke="#7A7E83" stroke-width="2"/>
  <path d="M20.25 60.75C20.25 52.5 28.5 45.5625 40.5 45.5625C52.5 45.5625 60.75 52.5 60.75 60.75" 
        fill="none" stroke="#7A7E83" stroke-width="2" stroke-linecap="round"/>
</svg>
```

**选中状态** (#007AFF)：
```svg
<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40.5" cy="27" r="13.5" fill="#007AFF"/>
  <path d="M20.25 60.75C20.25 52.5 28.5 45.5625 40.5 45.5625C52.5 45.5625 60.75 52.5 60.75 60.75" 
        fill="#007AFF"/>
</svg>
```

## 实现步骤

1. **使用在线SVG转PNG工具**：
   - 访问 https://convertio.co/svg-png/ 或类似工具
   - 将上述SVG代码保存为.svg文件
   - 转换为81x81像素的PNG格式

2. **使用设计软件**：
   - Figma、Sketch、Adobe Illustrator等
   - 创建81x81px画布
   - 按照上述设计绘制图标
   - 导出为PNG格式

3. **确保质量**：
   - 图标边缘清晰
   - 颜色准确（#007AFF和#7A7E83）
   - 透明背景
   - 适当的内边距（建议8-10px）

## 设计原则

1. **一致性**：所有图标使用相同的线条粗细和圆角半径
2. **可识别性**：图标含义清晰，符合用户认知习惯
3. **适配性**：在不同屏幕密度下都能清晰显示
4. **品牌一致性**：与应用整体设计风格保持一致
