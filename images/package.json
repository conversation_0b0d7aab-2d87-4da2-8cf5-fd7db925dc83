{"name": "wechat-miniprogram-icons", "version": "1.0.0", "description": "兼职工作管理小程序图标生成工具", "main": "generate-icons.js", "scripts": {"generate": "node generate-icons.js", "convert-icons": "for file in *.svg; do convert \"$file\" -resize 81x81 \"${file%.svg}.png\"; done", "install-deps": "npm install sharp", "clean": "rm -f *.svg *.png"}, "keywords": ["wechat", "miniprogram", "icons", "svg", "png"], "author": "UI Designer", "license": "MIT", "devDependencies": {"sharp": "^0.32.0"}}