// 微信小程序图标生成脚本
// 使用Node.js运行: node generate-icons.js

const fs = require("fs");
const path = require("path");

// 俏皮风格图标SVG定义 - 更多圆角和可爱元素
const icons = {
  "tab-home.png": `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <!-- 可爱的房子主体 -->
    <rect x="22" y="32" width="37" height="32" fill="none" stroke="#7A7E83" stroke-width="3" rx="8" stroke-linejoin="round"/>
    <!-- 圆润的屋顶 -->
    <path d="M18 35C18 35 40.5 18 40.5 18S63 35 63 35" stroke="#7A7E83" stroke-width="3" stroke-linecap="round" fill="none"/>
    <!-- 可爱的门 -->
    <rect x="35" y="48" width="11" height="16" fill="none" stroke="#7A7E83" stroke-width="2.5" rx="5.5"/>
    <!-- 小圆窗 -->
    <circle cx="28" cy="42" r="3" fill="none" stroke="#7A7E83" stroke-width="2"/>
    <circle cx="53" cy="42" r="3" fill="none" stroke="#7A7E83" stroke-width="2"/>
    <!-- 烟囱 -->
    <rect x="50" y="22" width="6" height="12" fill="none" stroke="#7A7E83" stroke-width="2" rx="3"/>
  </svg>`,

  "tab-home-active.png": `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <!-- 可爱的房子主体 - 填充版 -->
    <rect x="22" y="32" width="37" height="32" fill="#007AFF" stroke="#007AFF" stroke-width="3" rx="8" stroke-linejoin="round"/>
    <!-- 圆润的屋顶 -->
    <path d="M18 35C18 35 40.5 18 40.5 18S63 35 63 35" stroke="#007AFF" stroke-width="3" stroke-linecap="round" fill="none"/>
    <!-- 可爱的门 -->
    <rect x="35" y="48" width="11" height="16" fill="#ffffff" stroke="#007AFF" stroke-width="2.5" rx="5.5"/>
    <!-- 小圆窗 -->
    <circle cx="28" cy="42" r="3" fill="#ffffff"/>
    <circle cx="53" cy="42" r="3" fill="#ffffff"/>
    <!-- 烟囱 -->
    <rect x="50" y="22" width="6" height="12" fill="#007AFF" stroke="#007AFF" stroke-width="2" rx="3"/>
  </svg>`,

  "tab-stats.png": `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <!-- 俏皮的圆角柱状图 -->
    <rect x="18" y="45" width="10" height="20" fill="#7A7E83" rx="5"/>
    <rect x="30" y="35" width="10" height="30" fill="#7A7E83" rx="5"/>
    <rect x="42" y="40" width="10" height="25" fill="#7A7E83" rx="5"/>
    <rect x="54" y="25" width="10" height="40" fill="#7A7E83" rx="5"/>
    <!-- 可爱的底线 -->
    <path d="M12 67C12 67 69 67 69 67" stroke="#7A7E83" stroke-width="3" stroke-linecap="round"/>
    <!-- 小装饰点 -->
    <circle cx="23" cy="20" r="2" fill="#7A7E83"/>
    <circle cx="35" cy="15" r="2" fill="#7A7E83"/>
    <circle cx="47" cy="18" r="2" fill="#7A7E83"/>
    <circle cx="59" cy="12" r="2" fill="#7A7E83"/>
    <!-- 连接线 -->
    <path d="M23 20L35 15L47 18L59 12" stroke="#7A7E83" stroke-width="2" stroke-linecap="round" fill="none"/>
  </svg>`,

  "tab-stats-active.png": `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <!-- 俏皮的圆角柱状图 - 填充版 -->
    <rect x="18" y="45" width="10" height="20" fill="#007AFF" rx="5"/>
    <rect x="30" y="35" width="10" height="30" fill="#007AFF" rx="5"/>
    <rect x="42" y="40" width="10" height="25" fill="#007AFF" rx="5"/>
    <rect x="54" y="25" width="10" height="40" fill="#007AFF" rx="5"/>
    <!-- 可爱的底线 -->
    <path d="M12 67C12 67 69 67 69 67" stroke="#007AFF" stroke-width="3" stroke-linecap="round"/>
    <!-- 小装饰点 -->
    <circle cx="23" cy="20" r="2" fill="#007AFF"/>
    <circle cx="35" cy="15" r="2" fill="#007AFF"/>
    <circle cx="47" cy="18" r="2" fill="#007AFF"/>
    <circle cx="59" cy="12" r="2" fill="#007AFF"/>
    <!-- 连接线 -->
    <path d="M23 20L35 15L47 18L59 12" stroke="#007AFF" stroke-width="2" stroke-linecap="round" fill="none"/>
  </svg>`,

  "tab-profile.png": `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <!-- 可爱的头部 -->
    <circle cx="40.5" cy="28" r="12" fill="none" stroke="#7A7E83" stroke-width="3"/>
    <!-- 俏皮的身体轮廓 -->
    <path d="M22 62C22 52 30 47 40.5 47C51 47 59 52 59 62"
          fill="none" stroke="#7A7E83" stroke-width="3" stroke-linecap="round"/>
    <!-- 可爱的小装饰 -->
    <circle cx="35" cy="25" r="1.5" fill="#7A7E83"/>
    <circle cx="46" cy="25" r="1.5" fill="#7A7E83"/>
    <!-- 微笑 -->
    <path d="M35 32C35 32 38 35 40.5 35C43 35 46 32 46 32"
          stroke="#7A7E83" stroke-width="2" stroke-linecap="round" fill="none"/>
    <!-- 腮红 -->
    <circle cx="30" cy="30" r="2" fill="#7A7E83" opacity="0.3"/>
    <circle cx="51" cy="30" r="2" fill="#7A7E83" opacity="0.3"/>
  </svg>`,

  "tab-profile-active.png": `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <!-- 可爱的头部 - 填充版 -->
    <circle cx="40.5" cy="28" r="12" fill="#007AFF"/>
    <!-- 俏皮的身体轮廓 -->
    <path d="M22 62C22 52 30 47 40.5 47C51 47 59 52 59 62"
          fill="#007AFF"/>
    <!-- 可爱的小装饰 -->
    <circle cx="35" cy="25" r="1.5" fill="#ffffff"/>
    <circle cx="46" cy="25" r="1.5" fill="#ffffff"/>
    <!-- 微笑 -->
    <path d="M35 32C35 32 38 35 40.5 35C43 35 46 32 46 32"
          stroke="#ffffff" stroke-width="2" stroke-linecap="round" fill="none"/>
    <!-- 腮红 -->
    <circle cx="30" cy="30" r="2" fill="#ffffff" opacity="0.6"/>
    <circle cx="51" cy="30" r="2" fill="#ffffff" opacity="0.6"/>
  </svg>`,
};

// 生成SVG文件
function generateSVGFiles() {
  console.log("正在生成SVG文件...");

  Object.entries(icons).forEach(([filename, svgContent]) => {
    const svgFilename = filename.replace(".png", ".svg");
    const svgPath = path.join(__dirname, svgFilename);

    fs.writeFileSync(svgPath, svgContent);
    console.log(`✓ 已生成: ${svgFilename}`);
  });

  console.log("\n所有SVG文件已生成完成！");
  console.log("\n下一步：");
  console.log("1. 使用在线工具将SVG转换为PNG：");
  console.log("   - 访问 https://convertio.co/svg-png/");
  console.log("   - 上传SVG文件");
  console.log("   - 设置输出尺寸为 81x81 像素");
  console.log("   - 下载PNG文件");
  console.log("\n2. 或者使用命令行工具（需要安装ImageMagick）：");
  console.log("   brew install imagemagick  # macOS");
  console.log("   然后运行: npm run convert-icons");
}

// 如果安装了sharp包，可以直接转换为PNG
function convertToPNG() {
  try {
    const sharp = require("sharp");

    console.log("正在转换为PNG...");

    const promises = Object.entries(icons).map(
      async ([filename, svgContent]) => {
        const svgBuffer = Buffer.from(svgContent);
        const pngPath = path.join(__dirname, filename);

        await sharp(svgBuffer).resize(81, 81).png().toFile(pngPath);

        console.log(`✓ 已生成: ${filename}`);
      }
    );

    return Promise.all(promises);
  } catch (error) {
    console.log("Sharp包未安装，仅生成SVG文件。");
    console.log("如需直接生成PNG，请运行: npm install sharp");
    return Promise.resolve();
  }
}

// 主函数
async function main() {
  console.log("=== 微信小程序图标生成器 ===\n");

  // 生成SVG文件
  generateSVGFiles();

  // 尝试转换为PNG
  await convertToPNG();

  console.log("\n=== 生成完成 ===");
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { icons, generateSVGFiles, convertToPNG };
