// 微信小程序图标生成脚本
// 使用Node.js运行: node generate-icons.js

const fs = require('fs');
const path = require('path');

// 图标SVG定义
const icons = {
  'tab-home.png': `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <path d="M40.5 15L20.25 30.375V60.75H33.75V45.5625H47.25V60.75H60.75V30.375L40.5 15Z" 
          fill="#7A7E83" stroke="#7A7E83" stroke-width="2" stroke-linejoin="round"/>
    <path d="M13.5 33.75L40.5 13.5L67.5 33.75" 
          stroke="#7A7E83" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </svg>`,
  
  'tab-home-active.png': `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <path d="M40.5 15L20.25 30.375V60.75H33.75V45.5625H47.25V60.75H60.75V30.375L40.5 15Z" 
          fill="#007AFF" stroke="#007AFF" stroke-width="2" stroke-linejoin="round"/>
    <path d="M13.5 33.75L40.5 13.5L67.5 33.75" 
          stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </svg>`,
  
  'tab-stats.png': `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <rect x="20.25" y="40.5" width="8.1" height="27" fill="#7A7E83" rx="2"/>
    <rect x="33.75" y="27" width="8.1" height="40.5" fill="#7A7E83" rx="2"/>
    <rect x="47.25" y="33.75" width="8.1" height="33.75" fill="#7A7E83" rx="2"/>
    <rect x="60.75" y="20.25" width="8.1" height="47.25" fill="#7A7E83" rx="2"/>
    <path d="M13.5 67.5H67.5" stroke="#7A7E83" stroke-width="2" stroke-linecap="round"/>
  </svg>`,
  
  'tab-stats-active.png': `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <rect x="20.25" y="40.5" width="8.1" height="27" fill="#007AFF" rx="2"/>
    <rect x="33.75" y="27" width="8.1" height="40.5" fill="#007AFF" rx="2"/>
    <rect x="47.25" y="33.75" width="8.1" height="33.75" fill="#007AFF" rx="2"/>
    <rect x="60.75" y="20.25" width="8.1" height="47.25" fill="#007AFF" rx="2"/>
    <path d="M13.5 67.5H67.5" stroke="#007AFF" stroke-width="2" stroke-linecap="round"/>
  </svg>`,
  
  'tab-profile.png': `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <circle cx="40.5" cy="27" r="13.5" fill="none" stroke="#7A7E83" stroke-width="2"/>
    <path d="M20.25 60.75C20.25 52.5 28.5 45.5625 40.5 45.5625C52.5 45.5625 60.75 52.5 60.75 60.75" 
          fill="none" stroke="#7A7E83" stroke-width="2" stroke-linecap="round"/>
  </svg>`,
  
  'tab-profile-active.png': `<svg width="81" height="81" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
    <circle cx="40.5" cy="27" r="13.5" fill="#007AFF"/>
    <path d="M20.25 60.75C20.25 52.5 28.5 45.5625 40.5 45.5625C52.5 45.5625 60.75 52.5 60.75 60.75" 
          fill="#007AFF"/>
  </svg>`
};

// 生成SVG文件
function generateSVGFiles() {
  console.log('正在生成SVG文件...');
  
  Object.entries(icons).forEach(([filename, svgContent]) => {
    const svgFilename = filename.replace('.png', '.svg');
    const svgPath = path.join(__dirname, svgFilename);
    
    fs.writeFileSync(svgPath, svgContent);
    console.log(`✓ 已生成: ${svgFilename}`);
  });
  
  console.log('\n所有SVG文件已生成完成！');
  console.log('\n下一步：');
  console.log('1. 使用在线工具将SVG转换为PNG：');
  console.log('   - 访问 https://convertio.co/svg-png/');
  console.log('   - 上传SVG文件');
  console.log('   - 设置输出尺寸为 81x81 像素');
  console.log('   - 下载PNG文件');
  console.log('\n2. 或者使用命令行工具（需要安装ImageMagick）：');
  console.log('   brew install imagemagick  # macOS');
  console.log('   然后运行: npm run convert-icons');
}

// 如果安装了sharp包，可以直接转换为PNG
function convertToPNG() {
  try {
    const sharp = require('sharp');
    
    console.log('正在转换为PNG...');
    
    const promises = Object.entries(icons).map(async ([filename, svgContent]) => {
      const svgBuffer = Buffer.from(svgContent);
      const pngPath = path.join(__dirname, filename);
      
      await sharp(svgBuffer)
        .resize(81, 81)
        .png()
        .toFile(pngPath);
      
      console.log(`✓ 已生成: ${filename}`);
    });
    
    return Promise.all(promises);
  } catch (error) {
    console.log('Sharp包未安装，仅生成SVG文件。');
    console.log('如需直接生成PNG，请运行: npm install sharp');
    return Promise.resolve();
  }
}

// 主函数
async function main() {
  console.log('=== 微信小程序图标生成器 ===\n');
  
  // 生成SVG文件
  generateSVGFiles();
  
  // 尝试转换为PNG
  await convertToPNG();
  
  console.log('\n=== 生成完成 ===');
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { icons, generateSVGFiles, convertToPNG };
