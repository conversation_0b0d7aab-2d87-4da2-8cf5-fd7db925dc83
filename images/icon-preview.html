<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>兼职工作管理 - 图标预览</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f8f9fa;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #007aff;
        text-align: center;
        margin-bottom: 30px;
      }
      .icon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 30px;
      }
      .icon-card {
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
      }
      .icon-pair {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin: 20px 0;
      }
      .icon-item {
        text-align: center;
      }
      .icon-item h4 {
        margin: 10px 0 5px 0;
        font-size: 14px;
        color: #333;
      }
      .download-btn {
        background: #007aff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        margin-top: 10px;
      }
      .download-btn:hover {
        background: #0056cc;
      }
      .instructions {
        background: #f0f7ff;
        border: 1px solid #007aff;
        border-radius: 8px;
        padding: 20px;
        margin-top: 30px;
      }
      .instructions h3 {
        color: #007aff;
        margin-top: 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>兼职工作管理小程序图标</h1>

      <div class="icon-grid">
        <!-- 首页图标 -->
        <div class="icon-card">
          <h3>首页图标 (Home) - 俏皮风格</h3>
          <div class="icon-pair">
            <div class="icon-item">
              <svg
                width="81"
                height="81"
                viewBox="0 0 81 81"
                xmlns="http://www.w3.org/2000/svg"
              >
                <!-- 可爱的房子主体 -->
                <rect
                  x="22"
                  y="32"
                  width="37"
                  height="32"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="3"
                  rx="8"
                  stroke-linejoin="round"
                />
                <!-- 圆润的屋顶 -->
                <path
                  d="M18 35C18 35 40.5 18 40.5 18S63 35 63 35"
                  stroke="#7A7E83"
                  stroke-width="3"
                  stroke-linecap="round"
                  fill="none"
                />
                <!-- 可爱的门 -->
                <rect
                  x="35"
                  y="48"
                  width="11"
                  height="16"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="2.5"
                  rx="5.5"
                />
                <!-- 小圆窗 -->
                <circle
                  cx="28"
                  cy="42"
                  r="3"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="2"
                />
                <circle
                  cx="53"
                  cy="42"
                  r="3"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="2"
                />
                <!-- 烟囱 -->
                <rect
                  x="50"
                  y="22"
                  width="6"
                  height="12"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="2"
                  rx="3"
                />
              </svg>
              <h4>未选中状态</h4>
              <button
                class="download-btn"
                onclick="downloadSVG('tab-home', this.parentElement.querySelector('svg'))"
              >
                下载PNG
              </button>
            </div>
            <div class="icon-item">
              <svg
                width="81"
                height="81"
                viewBox="0 0 81 81"
                xmlns="http://www.w3.org/2000/svg"
              >
                <!-- 可爱的房子主体 - 填充版 -->
                <rect
                  x="22"
                  y="32"
                  width="37"
                  height="32"
                  fill="#007AFF"
                  stroke="#007AFF"
                  stroke-width="3"
                  rx="8"
                  stroke-linejoin="round"
                />
                <!-- 圆润的屋顶 -->
                <path
                  d="M18 35C18 35 40.5 18 40.5 18S63 35 63 35"
                  stroke="#007AFF"
                  stroke-width="3"
                  stroke-linecap="round"
                  fill="none"
                />
                <!-- 可爱的门 -->
                <rect
                  x="35"
                  y="48"
                  width="11"
                  height="16"
                  fill="#ffffff"
                  stroke="#007AFF"
                  stroke-width="2.5"
                  rx="5.5"
                />
                <!-- 小圆窗 -->
                <circle cx="28" cy="42" r="3" fill="#ffffff" />
                <circle cx="53" cy="42" r="3" fill="#ffffff" />
                <!-- 烟囱 -->
                <rect
                  x="50"
                  y="22"
                  width="6"
                  height="12"
                  fill="#007AFF"
                  stroke="#007AFF"
                  stroke-width="2"
                  rx="3"
                />
              </svg>
              <h4>选中状态</h4>
              <button
                class="download-btn"
                onclick="downloadSVG('tab-home-active', this.parentElement.querySelector('svg'))"
              >
                下载PNG
              </button>
            </div>
          </div>
        </div>

        <!-- 统计图标 -->
        <div class="icon-card">
          <h3>统计图标 (Statistics)</h3>
          <div class="icon-pair">
            <div class="icon-item">
              <svg
                width="81"
                height="81"
                viewBox="0 0 81 81"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="20.25"
                  y="40.5"
                  width="8.1"
                  height="27"
                  fill="#7A7E83"
                  rx="2"
                />
                <rect
                  x="33.75"
                  y="27"
                  width="8.1"
                  height="40.5"
                  fill="#7A7E83"
                  rx="2"
                />
                <rect
                  x="47.25"
                  y="33.75"
                  width="8.1"
                  height="33.75"
                  fill="#7A7E83"
                  rx="2"
                />
                <rect
                  x="60.75"
                  y="20.25"
                  width="8.1"
                  height="47.25"
                  fill="#7A7E83"
                  rx="2"
                />
                <path
                  d="M13.5 67.5H67.5"
                  stroke="#7A7E83"
                  stroke-width="2"
                  stroke-linecap="round"
                />
              </svg>
              <h4>未选中状态</h4>
              <button
                class="download-btn"
                onclick="downloadSVG('stats-normal', this.parentElement.querySelector('svg'))"
              >
                下载PNG
              </button>
            </div>
            <div class="icon-item">
              <svg
                width="81"
                height="81"
                viewBox="0 0 81 81"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="20.25"
                  y="40.5"
                  width="8.1"
                  height="27"
                  fill="#007AFF"
                  rx="2"
                />
                <rect
                  x="33.75"
                  y="27"
                  width="8.1"
                  height="40.5"
                  fill="#007AFF"
                  rx="2"
                />
                <rect
                  x="47.25"
                  y="33.75"
                  width="8.1"
                  height="33.75"
                  fill="#007AFF"
                  rx="2"
                />
                <rect
                  x="60.75"
                  y="20.25"
                  width="8.1"
                  height="47.25"
                  fill="#007AFF"
                  rx="2"
                />
                <path
                  d="M13.5 67.5H67.5"
                  stroke="#007AFF"
                  stroke-width="2"
                  stroke-linecap="round"
                />
              </svg>
              <h4>选中状态</h4>
              <button
                class="download-btn"
                onclick="downloadSVG('stats-active', this.parentElement.querySelector('svg'))"
              >
                下载PNG
              </button>
            </div>
          </div>
        </div>

        <!-- 个人中心图标 -->
        <div class="icon-card">
          <h3>个人中心图标 (Profile)</h3>
          <div class="icon-pair">
            <div class="icon-item">
              <svg
                width="81"
                height="81"
                viewBox="0 0 81 81"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="40.5"
                  cy="27"
                  r="13.5"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="2"
                />
                <path
                  d="M20.25 60.75C20.25 52.5 28.5 45.5625 40.5 45.5625C52.5 45.5625 60.75 52.5 60.75 60.75"
                  fill="none"
                  stroke="#7A7E83"
                  stroke-width="2"
                  stroke-linecap="round"
                />
              </svg>
              <h4>未选中状态</h4>
              <button
                class="download-btn"
                onclick="downloadSVG('profile-normal', this.parentElement.querySelector('svg'))"
              >
                下载PNG
              </button>
            </div>
            <div class="icon-item">
              <svg
                width="81"
                height="81"
                viewBox="0 0 81 81"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="40.5" cy="27" r="13.5" fill="#007AFF" />
                <path
                  d="M20.25 60.75C20.25 52.5 28.5 45.5625 40.5 45.5625C52.5 45.5625 60.75 52.5 60.75 60.75"
                  fill="#007AFF"
                />
              </svg>
              <h4>选中状态</h4>
              <button
                class="download-btn"
                onclick="downloadSVG('profile-active', this.parentElement.querySelector('svg'))"
              >
                下载PNG
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="instructions">
        <h3>使用说明</h3>
        <ol>
          <li>点击每个图标下方的"下载PNG"按钮，将自动下载81x81像素的PNG文件</li>
          <li>
            将下载的文件重命名为对应的文件名：
            <ul>
              <li>tab-home.png (首页-未选中)</li>
              <li>tab-home-active.png (首页-选中)</li>
              <li>tab-stats.png (统计-未选中)</li>
              <li>tab-stats-active.png (统计-选中)</li>
              <li>tab-profile.png (个人中心-未选中)</li>
              <li>tab-profile-active.png (个人中心-选中)</li>
            </ul>
          </li>
          <li>将重命名后的文件放入项目的 <code>images/</code> 目录中</li>
          <li>确保文件路径与 app.json 中的配置一致</li>
        </ol>
      </div>
    </div>

    <script>
      function downloadSVG(filename, svgElement) {
        // 创建canvas元素
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        canvas.width = 81;
        canvas.height = 81;

        // 将SVG转换为图片
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgData], {
          type: "image/svg+xml;charset=utf-8",
        });
        const svgUrl = URL.createObjectURL(svgBlob);

        const img = new Image();
        img.onload = function () {
          ctx.drawImage(img, 0, 0, 81, 81);

          // 转换为PNG并下载
          canvas.toBlob(function (blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = filename + ".png";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }, "image/png");

          URL.revokeObjectURL(svgUrl);
        };
        img.src = svgUrl;
      }
    </script>
  </body>
</html>
