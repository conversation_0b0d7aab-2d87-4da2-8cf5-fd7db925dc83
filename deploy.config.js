// deploy.config.js
// 部署配置文件

/**
 * 部署环境配置
 */
const deployConfig = {
  // 开发环境
  development: {
    appId: 'wx1234567890abcdef', // 开发环境AppID
    version: '1.0.0-dev',
    description: '开发版本',
    miniprogramRoot: './',
    setting: {
      urlCheck: false,
      es6: true,
      enhance: true,
      postcss: true,
      preloadBackgroundData: false,
      minified: false,
      newFeature: true,
      coverView: true,
      nodeModules: false,
      autoAudits: false,
      showShadowRootInWxmlPanel: true,
      scopeDataCheck: false,
      uglifyFileName: false,
      checkInvalidKey: true,
      checkSiteMap: true,
      uploadWithSourceMap: true,
      compileHotReLoad: false,
      lazyloadPlaceholderEnable: false,
      useMultiFrameRuntime: true,
      useApiHook: true,
      useApiHostProcess: true,
      babelSetting: {
        ignore: [],
        disablePlugins: [],
        outputPath: ''
      },
      enableEngineNative: false,
      useIsolateContext: true,
      userConfirmedBundleSwitch: false,
      packNpmManually: false,
      packNpmRelationList: [],
      minifyWXSS: true,
      disableUseStrict: false,
      minifyWXML: true
    },
    compileType: 'miniprogram',
    libVersion: '2.19.4',
    debugOptions: {
      hidedInDevtools: []
    }
  },

  // 测试环境
  testing: {
    appId: 'wx1234567890abcdef', // 测试环境AppID
    version: '1.0.0-beta',
    description: '测试版本',
    miniprogramRoot: './',
    setting: {
      urlCheck: true,
      es6: true,
      enhance: true,
      postcss: true,
      preloadBackgroundData: false,
      minified: true,
      newFeature: true,
      coverView: true,
      nodeModules: false,
      autoAudits: true,
      showShadowRootInWxmlPanel: false,
      scopeDataCheck: true,
      uglifyFileName: false,
      checkInvalidKey: true,
      checkSiteMap: true,
      uploadWithSourceMap: false,
      compileHotReLoad: false,
      lazyloadPlaceholderEnable: false,
      useMultiFrameRuntime: true,
      useApiHook: true,
      useApiHostProcess: true,
      babelSetting: {
        ignore: [],
        disablePlugins: [],
        outputPath: ''
      },
      enableEngineNative: false,
      useIsolateContext: true,
      userConfirmedBundleSwitch: false,
      packNpmManually: false,
      packNpmRelationList: [],
      minifyWXSS: true,
      disableUseStrict: false,
      minifyWXML: true
    },
    compileType: 'miniprogram',
    libVersion: '2.19.4'
  },

  // 生产环境
  production: {
    appId: 'wx1234567890abcdef', // 生产环境AppID
    version: '1.0.0',
    description: '正式版本',
    miniprogramRoot: './',
    setting: {
      urlCheck: true,
      es6: true,
      enhance: true,
      postcss: true,
      preloadBackgroundData: false,
      minified: true,
      newFeature: true,
      coverView: true,
      nodeModules: false,
      autoAudits: true,
      showShadowRootInWxmlPanel: false,
      scopeDataCheck: true,
      uglifyFileName: true,
      checkInvalidKey: true,
      checkSiteMap: true,
      uploadWithSourceMap: false,
      compileHotReLoad: false,
      lazyloadPlaceholderEnable: false,
      useMultiFrameRuntime: true,
      useApiHook: true,
      useApiHostProcess: true,
      babelSetting: {
        ignore: [],
        disablePlugins: [],
        outputPath: ''
      },
      enableEngineNative: false,
      useIsolateContext: true,
      userConfirmedBundleSwitch: false,
      packNpmManually: false,
      packNpmRelationList: [],
      minifyWXSS: true,
      disableUseStrict: false,
      minifyWXML: true
    },
    compileType: 'miniprogram',
    libVersion: '2.19.4'
  }
}

/**
 * 部署脚本配置
 */
const deployScripts = {
  // 预部署检查
  preCheck: {
    // 代码质量检查
    codeQuality: true,
    // 功能测试
    functionalTest: true,
    // 性能测试
    performanceTest: true,
    // 兼容性测试
    compatibilityTest: true,
    // 安全检查
    securityCheck: true
  },

  // 构建配置
  build: {
    // 代码压缩
    minify: true,
    // 资源优化
    optimize: true,
    // 图片压缩
    imageCompress: true,
    // 代码分割
    codeSplit: false,
    // Tree Shaking
    treeShaking: true
  },

  // 部署后验证
  postDeploy: {
    // 健康检查
    healthCheck: true,
    // 功能验证
    functionalVerify: true,
    // 性能监控
    performanceMonitor: true,
    // 错误监控
    errorMonitor: true
  }
}

/**
 * 发布流程配置
 */
const releaseProcess = {
  // 版本管理
  version: {
    // 版本号格式
    format: 'semantic', // semantic | timestamp | custom
    // 自动递增
    autoIncrement: true,
    // 版本前缀
    prefix: 'v',
    // 预发布标识
    prerelease: {
      dev: 'dev',
      beta: 'beta',
      rc: 'rc'
    }
  },

  // 发布阶段
  stages: [
    {
      name: 'development',
      description: '开发版本',
      auto: true,
      approval: false
    },
    {
      name: 'testing',
      description: '测试版本',
      auto: false,
      approval: true,
      approvers: ['developer', 'tester']
    },
    {
      name: 'production',
      description: '正式版本',
      auto: false,
      approval: true,
      approvers: ['developer', 'tester', 'product-manager']
    }
  ],

  // 回滚策略
  rollback: {
    // 自动回滚条件
    autoRollback: {
      errorRate: 0.05, // 错误率超过5%
      responseTime: 5000, // 响应时间超过5秒
      crashRate: 0.01 // 崩溃率超过1%
    },
    // 回滚步骤
    steps: [
      'stop-traffic',
      'restore-previous-version',
      'verify-rollback',
      'notify-team'
    ]
  }
}

/**
 * 监控配置
 */
const monitoringConfig = {
  // 性能监控
  performance: {
    // 页面加载时间
    pageLoadTime: {
      threshold: 3000, // 3秒
      alert: true
    },
    // API响应时间
    apiResponseTime: {
      threshold: 2000, // 2秒
      alert: true
    },
    // 内存使用
    memoryUsage: {
      threshold: 100, // 100MB
      alert: true
    }
  },

  // 错误监控
  error: {
    // JavaScript错误
    jsError: {
      alert: true,
      threshold: 10 // 每小时10个错误
    },
    // 网络错误
    networkError: {
      alert: true,
      threshold: 5 // 每小时5个错误
    },
    // 崩溃监控
    crash: {
      alert: true,
      threshold: 1 // 任何崩溃都报警
    }
  },

  // 用户行为监控
  userBehavior: {
    // 页面访问统计
    pageView: true,
    // 用户操作统计
    userAction: true,
    // 用户留存分析
    retention: true,
    // 转化率分析
    conversion: true
  }
}

/**
 * 通知配置
 */
const notificationConfig = {
  // 通知渠道
  channels: {
    email: {
      enabled: true,
      recipients: ['<EMAIL>']
    },
    webhook: {
      enabled: true,
      url: 'https://hooks.slack.com/services/xxx'
    },
    sms: {
      enabled: false,
      recipients: []
    }
  },

  // 通知事件
  events: {
    deployStart: true,
    deploySuccess: true,
    deployFailure: true,
    rollbackStart: true,
    rollbackSuccess: true,
    performanceAlert: true,
    errorAlert: true
  }
}

module.exports = {
  deployConfig,
  deployScripts,
  releaseProcess,
  monitoringConfig,
  notificationConfig
}
