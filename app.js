// app.js
// 应用入口文件

import { workTypeStorage } from "./utils/storage.js";
import { WorkType } from "./data/work-type.js";
import { performanceManager } from "./utils/performance.js";
import { logger, errorHandler } from "./utils/error-handler.js";
import { deviceCompatibility } from "./utils/compatibility.js";
import NotificationManager from "./utils/notification.js";

App({
  /**
   * 应用启动时触发
   */
  onLaunch(options) {
    // 开始性能监控
    performanceManager.startTimer("app_launch");

    logger.info("应用启动", options, "app");

    // 初始化应用
    this.initApp().finally(() => {
      performanceManager.endTimer("app_launch");
    });
  },

  /**
   * 应用显示时触发
   */
  onShow(options) {
    logger.info("应用显示", options, "app");

    // 检查待处理的通知
    NotificationManager.checkPendingNotifications();
  },

  /**
   * 应用隐藏时触发
   */
  onHide() {
    logger.info("应用隐藏", null, "app");

    // 清理性能监控
    performanceManager.clear();
  },

  /**
   * 应用发生错误时触发
   */
  onError(error) {
    errorHandler.handleError(error, "app_error");
  },

  /**
   * 页面不存在时触发
   */
  onPageNotFound(res) {
    logger.error("页面不存在", res, "navigation");

    // 重定向到首页
    wx.reLaunch({
      url: "/pages/home/<USER>",
    });
  },

  /**
   * 未处理的Promise拒绝
   */
  onUnhandledRejection(res) {
    errorHandler.handleError(res.reason, "unhandled_rejection");
  },

  /**
   * 初始化应用
   */
  async initApp() {
    try {
      performanceManager.startTimer("app_init");

      // 获取系统信息
      this.initSystemInfo();

      // 初始化错误处理
      this.initErrorHandling();

      // 检查设备兼容性
      this.checkCompatibility();

      // 检查并初始化默认工作类型
      await this.initDefaultWorkTypes();

      // 初始化用户设置
      this.initUserSettings();

      // 初始化通知系统
      this.initNotifications();

      // 检查应用更新
      this.checkForUpdates();

      // 预加载关键资源
      this.preloadResources();

      logger.info("应用初始化完成", null, "app");
    } catch (error) {
      logger.error("应用初始化失败", error, "app");
      errorHandler.handleError(error, "app_init");
    } finally {
      performanceManager.endTimer("app_init");
    }
  },

  /**
   * 初始化系统信息
   */
  initSystemInfo() {
    try {
      this.globalData.systemInfo = wx.getSystemInfoSync();
      this.globalData.deviceInfo = deviceCompatibility.getDeviceInfo();

      logger.info(
        "系统信息获取成功",
        {
          platform: this.globalData.systemInfo.platform,
          system: this.globalData.systemInfo.system,
          version: this.globalData.systemInfo.version,
        },
        "system"
      );
    } catch (error) {
      logger.error("获取系统信息失败", error, "system");
    }
  },

  /**
   * 初始化错误处理
   */
  initErrorHandling() {
    // 添加自定义错误处理回调
    errorHandler.addErrorCallback((error, category, context) => {
      // 记录错误到统计系统（如果有的话）
      console.log("错误统计:", { error, category, context });
    });

    logger.info("错误处理系统初始化完成", null, "error");
  },

  /**
   * 检查设备兼容性
   */
  checkCompatibility() {
    try {
      const report = deviceCompatibility.generateReport();

      // 记录兼容性信息
      logger.info(
        "设备兼容性检查完成",
        {
          performanceLevel: report.performanceLevel,
          isLowEndDevice: report.isLowEndDevice,
          platform: report.deviceInfo.platform,
        },
        "compatibility"
      );

      // 根据设备性能调整应用行为
      if (report.isLowEndDevice) {
        // 低端设备优化
        this.optimizeForLowEndDevice();
      }

      this.globalData.compatibilityReport = report;
    } catch (error) {
      logger.error("兼容性检查失败", error, "compatibility");
    }
  },

  /**
   * 低端设备优化
   */
  optimizeForLowEndDevice() {
    logger.info("启用低端设备优化", null, "performance");

    // 减少性能监控频率
    performanceManager.disable();

    // 减少日志级别
    logger.setLevel(2); // 只记录WARN及以上级别

    // 设置全局标志
    this.globalData.isLowEndDevice = true;
  },

  /**
   * 初始化默认工作类型
   */
  async initDefaultWorkTypes() {
    try {
      performanceManager.startTimer("init_work_types");

      const result = await workTypeStorage.getAll();

      if (!result.success || !result.data || result.data.length === 0) {
        logger.info("初始化默认工作类型", null, "data");

        const defaultTypes = [
          new WorkType({
            name: "兼职工作",
            icon: "💼",
            color: "#007AFF",
            description: "一般兼职工作",
            isDefault: true,
          }),
          new WorkType({
            name: "家教辅导",
            icon: "📚",
            color: "#34C759",
            description: "家教或辅导工作",
          }),
          new WorkType({
            name: "服务行业",
            icon: "🛎️",
            color: "#FF9500",
            description: "餐饮、零售等服务工作",
          }),
          new WorkType({
            name: "技术工作",
            icon: "💻",
            color: "#5856D6",
            description: "编程、设计等技术工作",
          }),
        ];

        for (const workType of defaultTypes) {
          await workTypeStorage.save(workType.toStorage());
        }

        logger.info(
          "默认工作类型初始化完成",
          { count: defaultTypes.length },
          "data"
        );
      }
    } catch (error) {
      logger.error("初始化默认工作类型失败", error, "data");
      throw error;
    } finally {
      performanceManager.endTimer("init_work_types");
    }
  },

  /**
   * 初始化用户设置
   */
  initUserSettings() {
    try {
      const defaultSettings = {
        theme: "auto",
        notifications: true,
        currency: "CNY",
        language: "zh-CN",
        firstLaunch: true,
        version: "1.0.0",
        installTime: Date.now(),
      };

      const existingSettings = wx.getStorageSync("appSettings");
      if (!existingSettings) {
        wx.setStorageSync("appSettings", defaultSettings);
        logger.info("用户设置初始化完成", defaultSettings, "settings");
      } else {
        // 合并新的默认设置
        const mergedSettings = { ...defaultSettings, ...existingSettings };
        wx.setStorageSync("appSettings", mergedSettings);
        logger.info("用户设置更新完成", null, "settings");
      }

      this.globalData.appSettings = wx.getStorageSync("appSettings");
    } catch (error) {
      logger.error("初始化用户设置失败", error, "settings");
    }
  },

  /**
   * 初始化通知系统
   */
  initNotifications() {
    try {
      NotificationManager.init();
      logger.info("通知系统初始化完成", null, "notification");
    } catch (error) {
      logger.error("通知系统初始化失败", error, "notification");
    }
  },

  /**
   * 检查应用更新
   */
  checkForUpdates() {
    if (wx.canIUse("getUpdateManager")) {
      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        logger.info("检查更新结果", { hasUpdate: res.hasUpdate }, "update");
      });

      updateManager.onUpdateReady(() => {
        logger.info("新版本准备就绪", null, "update");

        wx.showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用？",
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          },
        });
      });

      updateManager.onUpdateFailed(() => {
        logger.error("应用更新失败", null, "update");
      });
    }
  },

  /**
   * 预加载关键资源
   */
  preloadResources() {
    try {
      // 预加载关键页面
      const criticalPages = [
        "/pages/add-work/add-work",
        "/pages/statistics/statistics",
      ];

      // 这里可以添加预加载逻辑
      logger.info(
        "关键资源预加载完成",
        { pages: criticalPages.length },
        "preload"
      );
    } catch (error) {
      logger.error("预加载资源失败", error, "preload");
    }
  },

  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    systemInfo: null,
    deviceInfo: null,
    compatibilityReport: null,
    appSettings: null,
    isLowEndDevice: false,
  },
});
