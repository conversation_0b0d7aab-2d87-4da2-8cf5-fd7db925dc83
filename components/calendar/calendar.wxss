/* components/calendar/calendar.wxss */
/* 日历组件样式 */

.calendar-container {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 日历头部 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
}

.calendar-nav {
  display: flex;
  align-items: center;
  flex: 1;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}

.nav-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.calendar-title {
  flex: 1;
  text-align: center;
  margin: 0 32rpx;
}

.year {
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 16rpx;
}

.month {
  font-size: 36rpx;
  font-weight: bold;
}

.today-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  color: #ffffff;
  font-size: 24rpx;
}

.today-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 星期标题 */
.calendar-weekdays {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.weekday {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 24rpx;
  font-weight: 500;
  color: #6c757d;
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2rpx;
  background: #e9ecef;
}

.calendar-cell {
  position: relative;
  background: #ffffff;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 8rpx 4rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 120rpx;
}

.calendar-cell:active {
  background: #f0f9ff;
}

.calendar-cell.other-month {
  background: #f8f9fa;
  color: #adb5bd;
}

.calendar-cell.today {
  background: #e3f2fd;
  border: 2rpx solid #2196f3;
}

.calendar-cell.has-work {
  background: #fff3cd;
}

.calendar-cell.today.has-work {
  background: #e1f5fe;
}

/* 日期数字 */
.date-number {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.other-month .date-number {
  color: #adb5bd;
}

.today .date-number {
  color: #2196f3;
  font-weight: bold;
}

/* 工作记录指示器 */
.work-indicators {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4rpx;
  margin-bottom: 4rpx;
  max-width: 100%;
}

.work-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #007AFF;
}

/* 收入金额 */
.income-amount {
  margin-top: auto;
}

.amount {
  font-size: 20rpx;
  color: #28a745;
  font-weight: 500;
  background: rgba(40, 167, 69, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
}

/* 今天标记 */
.today-mark {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 12rpx;
  height: 12rpx;
  background: #ff4757;
  border-radius: 50%;
}

/* 工作类型图例 */
.calendar-legend {
  padding: 32rpx;
  background: #f8f9fa;
  border-top: 2rpx solid #e9ecef;
}

.legend-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
}

.legend-name {
  font-size: 24rpx;
  color: #666;
}

/* 加载和空状态 */
.calendar-loading,
.calendar-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #6c757d;
  font-size: 28rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .calendar-cell {
    min-height: 100rpx;
  }
  
  .date-number {
    font-size: 24rpx;
  }
  
  .amount {
    font-size: 18rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .calendar-container {
    background: #1a1a1a;
    color: #ffffff;
  }
  
  .calendar-cell {
    background: #2a2a2a;
    color: #ffffff;
  }
  
  .calendar-cell.other-month {
    background: #1a1a1a;
    color: #666;
  }
  
  .calendar-weekdays {
    background: #2a2a2a;
  }
  
  .calendar-legend {
    background: #2a2a2a;
  }
}
