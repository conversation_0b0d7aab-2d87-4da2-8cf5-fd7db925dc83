<!-- components/calendar/calendar.wxml -->
<!-- 日历组件模板 -->

<view class="calendar-container">
  <!-- 日历头部 -->
  <view class="calendar-header">
    <view class="calendar-nav">
      <button class="nav-btn" bindtap="onPrevMonth">
        <text class="iconfont icon-left">‹</text>
      </button>
      <view class="calendar-title">
        <text class="year">{{currentYear}}年</text>
        <text class="month">{{currentMonth}}月</text>
      </view>
      <button class="nav-btn" bindtap="onNextMonth">
        <text class="iconfont icon-right">›</text>
      </button>
    </view>
    
    <!-- 今天按钮 -->
    <button class="today-btn" bindtap="onToday" wx:if="{{!isCurrentMonth}}">
      今天
    </button>
  </view>

  <!-- 星期标题 -->
  <view class="calendar-weekdays">
    <view class="weekday" wx:for="{{weekdays}}" wx:key="index">
      {{item}}
    </view>
  </view>

  <!-- 日历网格 -->
  <view class="calendar-grid">
    <view 
      class="calendar-cell {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.hasWork ? 'has-work' : ''}}"
      wx:for="{{calendarDates}}" 
      wx:key="date"
      bindtap="onDateTap"
      data-date="{{item.date}}"
    >
      <!-- 日期数字 -->
      <view class="date-number">{{item.day}}</view>
      
      <!-- 工作记录指示器 -->
      <view class="work-indicators" wx:if="{{item.workRecords && item.workRecords.length > 0}}">
        <view 
          class="work-indicator"
          wx:for="{{item.workRecords}}" 
          wx:for-item="record"
          wx:key="id"
          style="background-color: {{record.workTypeColor}}"
        ></view>
      </view>
      
      <!-- 收入金额显示 -->
      <view class="income-amount" wx:if="{{item.totalIncome > 0}}">
        <text class="amount">¥{{item.totalIncome}}</text>
      </view>
      
      <!-- 今天标记 -->
      <view class="today-mark" wx:if="{{item.isToday}}"></view>
    </view>
  </view>

  <!-- 工作类型图例 -->
  <view class="calendar-legend" wx:if="{{showLegend && workTypes.length > 0}}">
    <view class="legend-title">工作类型</view>
    <view class="legend-items">
      <view 
        class="legend-item" 
        wx:for="{{workTypes}}" 
        wx:key="id"
      >
        <view 
          class="legend-color" 
          style="background-color: {{item.color}}"
        ></view>
        <text class="legend-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="calendar-loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="calendar-empty" wx:if="{{!loading && calendarDates.length === 0}}">
    <text>暂无数据</text>
  </view>
</view>
