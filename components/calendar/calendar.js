// components/calendar/calendar.js
// 日历组件逻辑

import DateUtils from '../../utils/date.js'
import { workRecordStorage, workTypeStorage } from '../../utils/storage.js'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前显示的年月
    year: {
      type: Number,
      value: new Date().getFullYear()
    },
    month: {
      type: Number,
      value: new Date().getMonth() + 1
    },
    // 是否显示工作类型图例
    showLegend: {
      type: Boolean,
      value: true
    },
    // 是否显示收入金额
    showIncome: {
      type: Boolean,
      value: true
    },
    // 最大显示的工作记录指示器数量
    maxIndicators: {
      type: Number,
      value: 3
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    calendarDates: [],
    workTypes: [],
    workRecords: [],
    weekdays: ['一', '二', '三', '四', '五', '六', '日'],
    loading: false,
    isCurrentMonth: true
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化日历
     */
    initCalendar() {
      this.setData({
        currentYear: this.properties.year,
        currentMonth: this.properties.month,
        loading: true
      })
      
      this.loadWorkTypes()
      this.loadWorkRecords()
      this.generateCalendar()
      this.checkIsCurrentMonth()
    },

    /**
     * 加载工作类型
     */
    async loadWorkTypes() {
      try {
        const result = await workTypeStorage.getAll()
        if (result.success) {
          const activeWorkTypes = result.data.filter(wt => wt.isActive)
          this.setData({
            workTypes: activeWorkTypes
          })
        }
      } catch (error) {
        console.error('加载工作类型失败:', error)
      }
    },

    /**
     * 加载工作记录
     */
    async loadWorkRecords() {
      try {
        const result = await workRecordStorage.getAll()
        if (result.success) {
          this.setData({
            workRecords: result.data || []
          })
        }
      } catch (error) {
        console.error('加载工作记录失败:', error)
      }
    },

    /**
     * 生成日历网格
     */
    generateCalendar() {
      const { currentYear, currentMonth } = this.data
      const calendarGrid = DateUtils.getCalendarGrid(currentYear, currentMonth)
      
      // 为每个日期添加工作记录信息
      const calendarDates = calendarGrid.map(dateInfo => {
        const dayRecords = this.getWorkRecordsForDate(dateInfo.date)
        const totalIncome = this.calculateDayIncome(dayRecords)
        
        return {
          ...dateInfo,
          workRecords: this.processWorkRecords(dayRecords),
          totalIncome: this.properties.showIncome ? totalIncome : 0,
          hasWork: dayRecords.length > 0
        }
      })

      this.setData({
        calendarDates,
        loading: false
      })
    },

    /**
     * 获取指定日期的工作记录
     */
    getWorkRecordsForDate(date) {
      return this.data.workRecords.filter(record => {
        if (record.isMultiDay && record.endDate) {
          // 多日工作记录
          return date >= record.date && date <= record.endDate
        } else {
          // 单日工作记录
          return record.date === date
        }
      })
    },

    /**
     * 处理工作记录（添加颜色信息，限制数量）
     */
    processWorkRecords(records) {
      const { workTypes, maxIndicators } = this.data
      const workTypeMap = new Map(workTypes.map(wt => [wt.id, wt]))
      
      const processedRecords = records.map(record => ({
        ...record,
        workTypeColor: workTypeMap.get(record.workTypeId)?.color || '#cccccc'
      }))

      // 限制显示数量
      return processedRecords.slice(0, maxIndicators)
    },

    /**
     * 计算单日收入
     */
    calculateDayIncome(records) {
      return records.reduce((total, record) => {
        return total + (record.totalSalary || 0)
      }, 0)
    },

    /**
     * 检查是否是当前月
     */
    checkIsCurrentMonth() {
      const now = new Date()
      const isCurrentMonth = this.data.currentYear === now.getFullYear() && 
                           this.data.currentMonth === now.getMonth() + 1
      this.setData({ isCurrentMonth })
    },

    /**
     * 上一月
     */
    onPrevMonth() {
      let { currentYear, currentMonth } = this.data
      
      currentMonth--
      if (currentMonth < 1) {
        currentMonth = 12
        currentYear--
      }

      this.setData({
        currentYear,
        currentMonth
      })

      this.generateCalendar()
      this.checkIsCurrentMonth()
      this.triggerEvent('monthchange', { year: currentYear, month: currentMonth })
    },

    /**
     * 下一月
     */
    onNextMonth() {
      let { currentYear, currentMonth } = this.data
      
      currentMonth++
      if (currentMonth > 12) {
        currentMonth = 1
        currentYear++
      }

      this.setData({
        currentYear,
        currentMonth
      })

      this.generateCalendar()
      this.checkIsCurrentMonth()
      this.triggerEvent('monthchange', { year: currentYear, month: currentMonth })
    },

    /**
     * 回到今天
     */
    onToday() {
      const now = new Date()
      const currentYear = now.getFullYear()
      const currentMonth = now.getMonth() + 1

      this.setData({
        currentYear,
        currentMonth
      })

      this.generateCalendar()
      this.checkIsCurrentMonth()
      this.triggerEvent('monthchange', { year: currentYear, month: currentMonth })
    },

    /**
     * 日期点击事件
     */
    onDateTap(e) {
      const { date } = e.currentTarget.dataset
      const dayRecords = this.getWorkRecordsForDate(date)
      
      this.triggerEvent('datetap', {
        date,
        workRecords: dayRecords,
        hasWork: dayRecords.length > 0
      })
    },

    /**
     * 刷新日历数据
     */
    refresh() {
      this.loadWorkTypes()
      this.loadWorkRecords()
      this.generateCalendar()
    },

    /**
     * 跳转到指定年月
     */
    goToMonth(year, month) {
      this.setData({
        currentYear: year,
        currentMonth: month
      })
      
      this.generateCalendar()
      this.checkIsCurrentMonth()
      this.triggerEvent('monthchange', { year, month })
    },

    /**
     * 获取当前显示的年月
     */
    getCurrentMonth() {
      return {
        year: this.data.currentYear,
        month: this.data.currentMonth
      }
    },

    /**
     * 获取指定日期的详细信息
     */
    getDateInfo(date) {
      const dateInfo = this.data.calendarDates.find(d => d.date === date)
      if (dateInfo) {
        return {
          ...dateInfo,
          workRecords: this.getWorkRecordsForDate(date)
        }
      }
      return null
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initCalendar()
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时刷新数据
      this.refresh()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'year, month': function(year, month) {
      if (year && month) {
        this.setData({
          currentYear: year,
          currentMonth: month
        })
        this.generateCalendar()
        this.checkIsCurrentMonth()
      }
    }
  }
})
