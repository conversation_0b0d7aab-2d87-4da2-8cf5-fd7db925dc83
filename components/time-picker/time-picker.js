// components/time-picker/time-picker.js
// 时间选择器组件逻辑

import DateUtils from '../../utils/date.js'
import SalaryCalculator from '../../utils/salary.js'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 副标题
    subtitle: {
      type: String,
      value: ''
    },
    // 默认开始时间
    defaultStartTime: {
      type: String,
      value: ''
    },
    // 默认结束时间
    defaultEndTime: {
      type: String,
      value: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      value: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否显示工作时长
    showDuration: {
      type: Boolean,
      value: true
    },
    // 是否显示详细信息
    showDetails: {
      type: Boolean,
      value: true
    },
    // 是否显示快速选择
    showQuickSelect: {
      type: Boolean,
      value: true
    },
    // 时薪（用于计算预计收入）
    hourlyRate: {
      type: Number,
      value: 0
    },
    // 最小工作时长（分钟）
    minDuration: {
      type: Number,
      value: 1
    },
    // 最大工作时长（分钟）
    maxDuration: {
      type: Number,
      value: 1440 // 24小时
    },
    // 提示信息
    hintMessage: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    startTime: '',
    endTime: '',
    duration: 0,
    durationHours: 0,
    formattedDuration: '',
    estimatedSalary: 0,
    errorMessage: '',
    isOvernight: false,
    quickOptions: [
      { label: '上午班', start: '09:00', end: '12:00' },
      { label: '下午班', start: '14:00', end: '18:00' },
      { label: '全天班', start: '09:00', end: '18:00' },
      { label: '夜班', start: '20:00', end: '23:00' }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      const { defaultStartTime, defaultEndTime } = this.properties
      
      this.setData({
        startTime: defaultStartTime,
        endTime: defaultEndTime
      })
      
      this.calculateDuration()
      this.validateTime()
    },

    /**
     * 开始时间变化事件
     */
    onStartTimeChange(e) {
      const startTime = e.detail.value
      this.setData({ startTime })
      this.calculateDuration()
      this.validateTime()
      this.triggerTimeChange()
    },

    /**
     * 结束时间变化事件
     */
    onEndTimeChange(e) {
      const endTime = e.detail.value
      this.setData({ endTime })
      this.calculateDuration()
      this.validateTime()
      this.triggerTimeChange()
    },

    /**
     * 快速选择事件
     */
    onQuickSelect(e) {
      const { start, end } = e.currentTarget.dataset
      
      this.setData({
        startTime: start,
        endTime: end
      })
      
      this.calculateDuration()
      this.validateTime()
      this.triggerTimeChange()
    },

    /**
     * 计算工作时长
     */
    calculateDuration() {
      const { startTime, endTime } = this.data
      
      if (!startTime || !endTime) {
        this.setData({
          duration: 0,
          durationHours: 0,
          formattedDuration: '',
          estimatedSalary: 0,
          isOvernight: false
        })
        return
      }

      const duration = DateUtils.getMinutesDiff(startTime, endTime)
      const durationHours = DateUtils.minutesToHours(duration)
      const formattedDuration = DateUtils.formatDuration(duration)
      const isOvernight = duration > 12 * 60 // 超过12小时认为是跨天
      
      // 计算预计收入
      const { hourlyRate } = this.properties
      const estimatedSalary = hourlyRate > 0 
        ? SalaryCalculator.calculateHourlySalary(hourlyRate, duration)
        : 0

      this.setData({
        duration,
        durationHours,
        formattedDuration,
        estimatedSalary,
        isOvernight
      })
    },

    /**
     * 验证时间
     */
    validateTime() {
      const { startTime, endTime, duration } = this.data
      const { required, minDuration, maxDuration } = this.properties
      let errorMessage = ''

      // 检查必填
      if (required && (!startTime || !endTime)) {
        errorMessage = '请选择开始时间和结束时间'
      }
      // 检查时间格式
      else if (startTime && !DateUtils.isValidTime(startTime)) {
        errorMessage = '开始时间格式不正确'
      }
      else if (endTime && !DateUtils.isValidTime(endTime)) {
        errorMessage = '结束时间格式不正确'
      }
      // 检查时间逻辑
      else if (startTime && endTime) {
        if (duration <= 0) {
          errorMessage = '结束时间必须晚于开始时间'
        }
        else if (duration < minDuration) {
          errorMessage = `工作时长不能少于${DateUtils.formatDuration(minDuration)}`
        }
        else if (duration > maxDuration) {
          errorMessage = `工作时长不能超过${DateUtils.formatDuration(maxDuration)}`
        }
      }

      this.setData({ errorMessage })
      return errorMessage === ''
    },

    /**
     * 触发时间变化事件
     */
    triggerTimeChange() {
      const { startTime, endTime, duration, durationHours, estimatedSalary } = this.data
      const isValid = this.validateTime()

      this.triggerEvent('timechange', {
        startTime,
        endTime,
        duration,
        durationHours,
        estimatedSalary,
        isValid,
        formattedDuration: this.data.formattedDuration
      })
    },

    /**
     * 获取时间数据
     */
    getTimeData() {
      const { startTime, endTime, duration, durationHours, estimatedSalary } = this.data
      const isValid = this.validateTime()

      return {
        startTime,
        endTime,
        duration,
        durationHours,
        estimatedSalary,
        isValid,
        formattedDuration: this.data.formattedDuration
      }
    },

    /**
     * 设置时间
     */
    setTime(startTime, endTime) {
      this.setData({
        startTime: startTime || '',
        endTime: endTime || ''
      })
      
      this.calculateDuration()
      this.validateTime()
    },

    /**
     * 清空时间
     */
    clearTime() {
      this.setTime('', '')
    },

    /**
     * 验证组件数据
     */
    validate() {
      return this.validateTime()
    },

    /**
     * 获取验证错误信息
     */
    getErrorMessage() {
      return this.data.errorMessage
    },

    /**
     * 设置错误信息
     */
    setErrorMessage(message) {
      this.setData({ errorMessage: message })
    },

    /**
     * 清空错误信息
     */
    clearErrorMessage() {
      this.setData({ errorMessage: '' })
    },

    /**
     * 是否有效
     */
    isValid() {
      return this.data.errorMessage === ''
    },

    /**
     * 获取工作时长（分钟）
     */
    getDuration() {
      return this.data.duration
    },

    /**
     * 获取工作时长（小时）
     */
    getDurationHours() {
      return this.data.durationHours
    },

    /**
     * 获取格式化的工作时长
     */
    getFormattedDuration() {
      return this.data.formattedDuration
    },

    /**
     * 获取预计收入
     */
    getEstimatedSalary() {
      return this.data.estimatedSalary
    },

    /**
     * 是否跨天工作
     */
    isOvernightWork() {
      return this.data.isOvernight
    },

    /**
     * 添加快速选择选项
     */
    addQuickOption(label, start, end) {
      const quickOptions = [...this.data.quickOptions]
      quickOptions.push({ label, start, end })
      this.setData({ quickOptions })
    },

    /**
     * 移除快速选择选项
     */
    removeQuickOption(index) {
      const quickOptions = [...this.data.quickOptions]
      quickOptions.splice(index, 1)
      this.setData({ quickOptions })
    },

    /**
     * 设置快速选择选项
     */
    setQuickOptions(options) {
      this.setData({ quickOptions: options || [] })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'defaultStartTime, defaultEndTime': function(startTime, endTime) {
      this.setTime(startTime, endTime)
    },
    
    'hourlyRate': function() {
      this.calculateDuration()
    },
    
    'minDuration, maxDuration': function() {
      this.validateTime()
    }
  }
})
