/* components/time-picker/time-picker.wxss */
/* 时间选择器组件样式 */

.time-picker {
  width: 100%;
}

/* 标题区域 */
.picker-header {
  margin-bottom: 32rpx;
}

.picker-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.picker-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 时间选择区域 */
.time-selection {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.time-item {
  flex: 1;
}

.time-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.required-mark {
  color: #dc3545;
  font-size: 28rpx;
  margin-left: 4rpx;
}

.time-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.time-display:active {
  border-color: #007AFF;
  background: #f0f9ff;
}

.time-display.placeholder {
  border-color: #dee2e6;
}

.time-display.placeholder .time-text {
  color: #adb5bd;
}

.time-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.time-icon {
  font-size: 32rpx;
  opacity: 0.6;
}

/* 时间箭头 */
.time-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #6c757d;
  font-weight: bold;
}

/* 工作时长显示 */
.duration-display {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.duration-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.duration-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
}

.duration-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #007AFF;
}

.duration-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-text {
  font-size: 24rpx;
  color: #6c757d;
}

/* 快速选择 */
.quick-select {
  margin-bottom: 32rpx;
}

.quick-label {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 16rpx;
}

.quick-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.quick-btn {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #495057;
  transition: all 0.2s ease;
}

.quick-btn:active {
  background: #007AFF;
  border-color: #007AFF;
  color: #ffffff;
  transform: scale(0.95);
}

.quick-btn[disabled] {
  opacity: 0.5;
  background: #f8f9fa;
  color: #adb5bd;
}

/* 错误信息 */
.error-message {
  background: #f8d7da;
  border: 2rpx solid #f5c6cb;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 24rpx;
  color: #721c24;
}

/* 提示信息 */
.hint-message {
  background: #d1ecf1;
  border: 2rpx solid #bee5eb;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}

.hint-text {
  font-size: 24rpx;
  color: #0c5460;
}

/* 跨天工作提示 */
.overnight-notice {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.notice-icon {
  font-size: 32rpx;
}

.notice-text {
  font-size: 26rpx;
  color: #856404;
  flex: 1;
}

/* 禁用状态 */
.time-picker[disabled] .time-display {
  background: #f8f9fa;
  border-color: #dee2e6;
  opacity: 0.6;
}

.time-picker[disabled] .time-text {
  color: #6c757d;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .time-selection {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .time-arrow {
    transform: rotate(90deg);
    margin: 16rpx 0;
  }
  
  .quick-buttons {
    justify-content: center;
  }
  
  .duration-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .time-display {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .time-text {
    color: #ffffff;
  }
  
  .duration-display {
    background: linear-gradient(135deg, #2a2a2a 0%, #404040 100%);
  }
  
  .duration-label {
    color: #ffffff;
  }
  
  .quick-btn {
    background: #2a2a2a;
    border-color: #404040;
    color: #ffffff;
  }
  
  .error-message {
    background: #2a2a2a;
    border-color: #dc3545;
  }
  
  .error-text {
    color: #ff6b6b;
  }
  
  .hint-message {
    background: #2a2a2a;
    border-color: #17a2b8;
  }
  
  .hint-text {
    color: #5bc0de;
  }
  
  .overnight-notice {
    background: #2a2a2a;
    border-color: #ffc107;
  }
  
  .notice-text {
    color: #ffc107;
  }
}
