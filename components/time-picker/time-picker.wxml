<!-- components/time-picker/time-picker.wxml -->
<!-- 时间选择器组件模板 -->

<view class="time-picker">
  <!-- 标题 -->
  <view class="picker-header" wx:if="{{title}}">
    <text class="picker-title">{{title}}</text>
    <text class="picker-subtitle" wx:if="{{subtitle}}">{{subtitle}}</text>
  </view>

  <!-- 时间选择区域 -->
  <view class="time-selection">
    <!-- 开始时间 -->
    <view class="time-item">
      <view class="time-label">
        <text class="label-text">开始时间</text>
        <text class="required-mark" wx:if="{{required}}">*</text>
      </view>
      <picker 
        mode="time" 
        value="{{startTime}}" 
        bindchange="onStartTimeChange"
        disabled="{{disabled}}"
      >
        <view class="time-display {{!startTime ? 'placeholder' : ''}}">
          <text class="time-text">{{startTime || '请选择开始时间'}}</text>
          <text class="time-icon">🕐</text>
        </view>
      </picker>
    </view>

    <!-- 时间箭头 -->
    <view class="time-arrow">
      <text class="arrow-icon">→</text>
    </view>

    <!-- 结束时间 -->
    <view class="time-item">
      <view class="time-label">
        <text class="label-text">结束时间</text>
        <text class="required-mark" wx:if="{{required}}">*</text>
      </view>
      <picker 
        mode="time" 
        value="{{endTime}}" 
        bindchange="onEndTimeChange"
        disabled="{{disabled}}"
      >
        <view class="time-display {{!endTime ? 'placeholder' : ''}}">
          <text class="time-text">{{endTime || '请选择结束时间'}}</text>
          <text class="time-icon">🕐</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 工作时长显示 -->
  <view class="duration-display" wx:if="{{showDuration && duration > 0}}">
    <view class="duration-info">
      <text class="duration-label">工作时长</text>
      <text class="duration-value">{{formattedDuration}}</text>
    </view>
    <view class="duration-details" wx:if="{{showDetails}}">
      <text class="detail-text">共 {{durationHours}} 小时</text>
      <text class="detail-text" wx:if="{{hourlyRate > 0}}">预计收入 ¥{{estimatedSalary}}</text>
    </view>
  </view>

  <!-- 快速选择按钮 -->
  <view class="quick-select" wx:if="{{showQuickSelect && quickOptions.length > 0}}">
    <text class="quick-label">快速选择</text>
    <view class="quick-buttons">
      <button 
        class="quick-btn"
        wx:for="{{quickOptions}}" 
        wx:key="label"
        bindtap="onQuickSelect"
        data-start="{{item.start}}"
        data-end="{{item.end}}"
        disabled="{{disabled}}"
      >
        {{item.label}}
      </button>
    </view>
  </view>

  <!-- 验证错误信息 -->
  <view class="error-message" wx:if="{{errorMessage}}">
    <text class="error-text">{{errorMessage}}</text>
  </view>

  <!-- 提示信息 -->
  <view class="hint-message" wx:if="{{hintMessage && !errorMessage}}">
    <text class="hint-text">{{hintMessage}}</text>
  </view>

  <!-- 跨天工作提示 -->
  <view class="overnight-notice" wx:if="{{isOvernight}}">
    <view class="notice-icon">🌙</view>
    <text class="notice-text">检测到跨天工作，请确认时间是否正确</text>
  </view>
</view>
