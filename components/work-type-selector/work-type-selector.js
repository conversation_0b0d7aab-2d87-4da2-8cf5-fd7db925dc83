// components/work-type-selector/work-type-selector.js
// 工作类型选择组件逻辑

import { workTypeStorage } from '../../utils/storage.js'
import { WorkType } from '../../data/work-type.js'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 副标题
    subtitle: {
      type: String,
      value: ''
    },
    // 是否多选
    multiple: {
      type: Boolean,
      value: false
    },
    // 最大选择数量（多选时有效）
    maxSelection: {
      type: Number,
      value: 0
    },
    // 默认选中的工作类型ID
    defaultSelected: {
      type: Array,
      value: []
    },
    // 是否显示薪资
    showRate: {
      type: Boolean,
      value: true
    },
    // 是否显示统计信息
    showStats: {
      type: Boolean,
      value: false
    },
    // 是否显示选择统计
    showSummary: {
      type: Boolean,
      value: true
    },
    // 是否显示筛选器
    showFilters: {
      type: Boolean,
      value: false
    },
    // 是否允许添加新工作类型
    allowAdd: {
      type: Boolean,
      value: true
    },
    // 禁用的工作类型ID列表
    disabledIds: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    workTypes: [],
    selectedIds: [],
    selectedCount: 0,
    loading: false,
    activeFilter: 'all'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      this.setData({
        selectedIds: [...this.properties.defaultSelected],
        selectedCount: this.properties.defaultSelected.length
      })
      this.loadWorkTypes()
    },

    /**
     * 加载工作类型
     */
    async loadWorkTypes() {
      this.setData({ loading: true })
      
      try {
        const result = await workTypeStorage.getAll()
        if (result.success) {
          let workTypes = result.data || []
          
          // 只显示活跃的工作类型
          workTypes = workTypes.filter(wt => wt.isActive)
          
          // 应用筛选
          workTypes = this.applyFilter(workTypes)
          
          // 处理工作类型数据
          workTypes = this.processWorkTypes(workTypes)
          
          this.setData({
            workTypes,
            loading: false
          })
        }
      } catch (error) {
        console.error('加载工作类型失败:', error)
        this.setData({ loading: false })
      }
    },

    /**
     * 处理工作类型数据
     */
    processWorkTypes(workTypes) {
      const { selectedIds, disabledIds } = this.data
      
      return workTypes.map(workType => ({
        ...workType,
        selected: selectedIds.includes(workType.id),
        disabled: disabledIds.includes(workType.id)
      }))
    },

    /**
     * 应用筛选条件
     */
    applyFilter(workTypes) {
      const { activeFilter } = this.data
      
      switch (activeFilter) {
        case 'recent':
          // 按最近使用排序（这里简化为按更新时间）
          return workTypes.sort((a, b) => b.updatedAt - a.updatedAt)
        case 'favorite':
          // 按使用频率排序（这里简化为按记录数量）
          return workTypes.sort((a, b) => (b.totalRecords || 0) - (a.totalRecords || 0))
        default:
          // 默认排序：默认类型在前，然后按创建时间
          return workTypes.sort((a, b) => {
            if (a.isDefault && !b.isDefault) return -1
            if (!a.isDefault && b.isDefault) return 1
            return a.createdAt - b.createdAt
          })
      }
    },

    /**
     * 工作类型选择事件
     */
    onWorkTypeSelect(e) {
      const { id } = e.currentTarget.dataset
      const workType = this.data.workTypes.find(wt => wt.id === id)
      
      if (!workType || workType.disabled) return
      
      let { selectedIds } = this.data
      const { multiple, maxSelection } = this.properties
      
      if (multiple) {
        // 多选模式
        const index = selectedIds.indexOf(id)
        if (index > -1) {
          // 取消选择
          selectedIds.splice(index, 1)
        } else {
          // 添加选择
          if (maxSelection > 0 && selectedIds.length >= maxSelection) {
            wx.showToast({
              title: `最多只能选择${maxSelection}个`,
              icon: 'none'
            })
            return
          }
          selectedIds.push(id)
        }
      } else {
        // 单选模式
        selectedIds = selectedIds.includes(id) ? [] : [id]
      }
      
      this.updateSelection(selectedIds)
    },

    /**
     * 更新选择状态
     */
    updateSelection(selectedIds) {
      const workTypes = this.data.workTypes.map(wt => ({
        ...wt,
        selected: selectedIds.includes(wt.id)
      }))
      
      this.setData({
        selectedIds,
        selectedCount: selectedIds.length,
        workTypes
      })
      
      // 触发选择变化事件
      const selectedWorkTypes = workTypes.filter(wt => wt.selected)
      this.triggerEvent('selectionchange', {
        selectedIds,
        selectedWorkTypes,
        selectedCount: selectedIds.length
      })
    },

    /**
     * 筛选器变化事件
     */
    onFilterChange(e) {
      const { filter } = e.currentTarget.dataset
      this.setData({ activeFilter: filter })
      this.loadWorkTypes()
    },

    /**
     * 添加工作类型事件
     */
    onAddWorkType() {
      this.triggerEvent('addworktype')
    },

    /**
     * 获取选中的工作类型
     */
    getSelectedWorkTypes() {
      return this.data.workTypes.filter(wt => wt.selected)
    },

    /**
     * 获取选中的工作类型ID
     */
    getSelectedIds() {
      return [...this.data.selectedIds]
    },

    /**
     * 设置选中的工作类型
     */
    setSelectedIds(ids) {
      this.updateSelection(ids)
    },

    /**
     * 清空选择
     */
    clearSelection() {
      this.updateSelection([])
    },

    /**
     * 全选（多选模式）
     */
    selectAll() {
      if (!this.properties.multiple) return
      
      const availableIds = this.data.workTypes
        .filter(wt => !wt.disabled)
        .map(wt => wt.id)
      
      const { maxSelection } = this.properties
      const selectedIds = maxSelection > 0 
        ? availableIds.slice(0, maxSelection)
        : availableIds
      
      this.updateSelection(selectedIds)
    },

    /**
     * 刷新工作类型列表
     */
    refresh() {
      this.loadWorkTypes()
    },

    /**
     * 验证选择
     */
    validateSelection() {
      const { selectedCount } = this.data
      const { multiple, maxSelection } = this.properties
      
      if (selectedCount === 0) {
        return {
          valid: false,
          message: '请选择至少一个工作类型'
        }
      }
      
      if (multiple && maxSelection > 0 && selectedCount > maxSelection) {
        return {
          valid: false,
          message: `最多只能选择${maxSelection}个工作类型`
        }
      }
      
      return {
        valid: true,
        message: ''
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init()
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时刷新数据
      this.refresh()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'defaultSelected': function(newVal) {
      if (newVal && Array.isArray(newVal)) {
        this.setData({
          selectedIds: [...newVal],
          selectedCount: newVal.length
        })
        this.loadWorkTypes()
      }
    },
    
    'disabledIds': function() {
      this.loadWorkTypes()
    }
  }
})
