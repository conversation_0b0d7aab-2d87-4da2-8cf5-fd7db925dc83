/* components/work-type-selector/work-type-selector.wxss */
/* 工作类型选择组件样式 */

.work-type-selector {
  width: 100%;
}

/* 标题区域 */
.selector-header {
  margin-bottom: 32rpx;
}

.selector-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.selector-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 工作类型网格 */
.work-type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.work-type-card {
  position: relative;
  background: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.work-type-card:active {
  transform: scale(0.98);
}

.work-type-card.selected {
  border-color: #007AFF;
  background: #f0f9ff;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.work-type-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.work-type-card.disabled:active {
  transform: none;
}

/* 选中状态指示器 */
.selection-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 40rpx;
  height: 40rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.check-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 工作类型图标 */
.work-type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  background: #007AFF;
}

.icon {
  color: #ffffff;
  font-size: 36rpx;
}

/* 工作类型信息 */
.work-type-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.work-type-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.work-type-rate {
  font-size: 24rpx;
  color: #28a745;
  font-weight: 500;
}

.work-type-stats {
  font-size: 22rpx;
  color: #6c757d;
}

/* 禁用遮罩 */
.disabled-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.disabled-text {
  font-size: 24rpx;
  color: #dc3545;
  font-weight: 500;
}

/* 添加新工作类型卡片 */
.add-card {
  border: 2rpx dashed #adb5bd;
  background: #f8f9fa;
  color: #6c757d;
}

.add-card:active {
  background: #e9ecef;
  border-color: #6c757d;
}

.add-icon {
  width: 80rpx;
  height: 80rpx;
  border: 2rpx dashed #adb5bd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.plus-icon {
  font-size: 48rpx;
  color: #adb5bd;
  font-weight: 300;
}

.add-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.add-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #6c757d;
}

.add-subtitle {
  font-size: 22rpx;
  color: #adb5bd;
}

/* 选择统计 */
.selection-summary {
  margin-top: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  text-align: center;
}

.summary-text {
  font-size: 26rpx;
  color: #495057;
}

/* 快速筛选 */
.quick-filters {
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
}

.filter-buttons {
  display: flex;
  gap: 12rpx;
}

.filter-btn {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #495057;
  transition: all 0.2s ease;
}

.filter-btn.active {
  background: #007AFF;
  border-color: #007AFF;
  color: #ffffff;
}

.filter-btn:active {
  transform: scale(0.95);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 32rpx;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.empty-action:active {
  background: #0056CC;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .work-type-grid {
    grid-template-columns: 1fr;
  }
  
  .work-type-card {
    flex-direction: row;
    text-align: left;
    padding: 24rpx;
  }
  
  .work-type-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 24rpx;
    margin-bottom: 0;
  }
  
  .work-type-info {
    align-items: flex-start;
    flex: 1;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .work-type-card {
    background: #2a2a2a;
    border-color: #404040;
    color: #ffffff;
  }
  
  .work-type-card.selected {
    background: #1a3a5c;
    border-color: #007AFF;
  }
  
  .work-type-name {
    color: #ffffff;
  }
  
  .selection-summary {
    background: #2a2a2a;
  }
  
  .filter-btn {
    background: #2a2a2a;
    border-color: #404040;
    color: #ffffff;
  }
}
