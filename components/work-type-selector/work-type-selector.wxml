<!-- components/work-type-selector/work-type-selector.wxml -->
<!-- 工作类型选择组件模板 -->

<view class="work-type-selector">
  <!-- 标题 -->
  <view class="selector-header" wx:if="{{title}}">
    <text class="selector-title">{{title}}</text>
    <text class="selector-subtitle" wx:if="{{subtitle}}">{{subtitle}}</text>
  </view>

  <!-- 工作类型网格 -->
  <view class="work-type-grid">
    <view 
      class="work-type-card {{item.selected ? 'selected' : ''}} {{item.disabled ? 'disabled' : ''}}"
      wx:for="{{workTypes}}" 
      wx:key="id"
      bindtap="onWorkTypeSelect"
      data-id="{{item.id}}"
      data-index="{{index}}"
    >
      <!-- 选中状态指示器 -->
      <view class="selection-indicator" wx:if="{{item.selected}}">
        <text class="check-icon">✓</text>
      </view>

      <!-- 工作类型图标 -->
      <view class="work-type-icon" style="background-color: {{item.color}}">
        <text class="icon {{item.icon}}"></text>
      </view>

      <!-- 工作类型信息 -->
      <view class="work-type-info">
        <text class="work-type-name">{{item.name}}</text>
        <text class="work-type-rate" wx:if="{{showRate && item.defaultHourlyRate > 0}}">
          ¥{{item.defaultHourlyRate}}/小时
        </text>
        <text class="work-type-stats" wx:if="{{showStats && item.totalRecords > 0}}">
          {{item.totalRecords}}次记录
        </text>
      </view>

      <!-- 禁用遮罩 -->
      <view class="disabled-mask" wx:if="{{item.disabled}}">
        <text class="disabled-text">不可选</text>
      </view>
    </view>

    <!-- 添加新工作类型卡片 -->
    <view 
      class="work-type-card add-card" 
      wx:if="{{allowAdd}}"
      bindtap="onAddWorkType"
    >
      <view class="add-icon">
        <text class="plus-icon">+</text>
      </view>
      <view class="add-text">
        <text class="add-title">添加类型</text>
        <text class="add-subtitle">自定义工作类型</text>
      </view>
    </view>
  </view>

  <!-- 选择统计 -->
  <view class="selection-summary" wx:if="{{showSummary && selectedCount > 0}}">
    <text class="summary-text">
      已选择 {{selectedCount}} 个工作类型
      <text wx:if="{{multiple && maxSelection > 0}}">（最多{{maxSelection}}个）</text>
    </text>
  </view>

  <!-- 快速筛选 -->
  <view class="quick-filters" wx:if="{{showFilters}}">
    <text class="filter-label">快速筛选：</text>
    <view class="filter-buttons">
      <button 
        class="filter-btn {{activeFilter === 'all' ? 'active' : ''}}"
        bindtap="onFilterChange"
        data-filter="all"
      >
        全部
      </button>
      <button 
        class="filter-btn {{activeFilter === 'recent' ? 'active' : ''}}"
        bindtap="onFilterChange"
        data-filter="recent"
      >
        最近使用
      </button>
      <button 
        class="filter-btn {{activeFilter === 'favorite' ? 'active' : ''}}"
        bindtap="onFilterChange"
        data-filter="favorite"
      >
        常用
      </button>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{workTypes.length === 0 && !loading}}">
    <view class="empty-icon">📋</view>
    <text class="empty-text">暂无工作类型</text>
    <button class="empty-action" wx:if="{{allowAdd}}" bindtap="onAddWorkType">
      添加第一个工作类型
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>
