// components/stat-card/stat-card.js
// 统计卡片组件逻辑

import SalaryCalculator from '../../utils/salary.js'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 卡片类型：number, progress, list, chart, custom
    cardType: {
      type: String,
      value: 'number'
    },
    // 卡片尺寸：small, normal, large
    size: {
      type: String,
      value: 'normal'
    },
    // 卡片主题：default, primary, success, warning, danger
    theme: {
      type: String,
      value: 'default'
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 副标题
    subtitle: {
      type: String,
      value: ''
    },
    // 数值（number类型）
    value: {
      type: Number,
      value: 0
    },
    // 单位（number类型）
    unit: {
      type: String,
      value: ''
    },
    // 是否显示趋势（number类型）
    showTrend: {
      type: Boolean,
      value: false
    },
    // 趋势数据（number类型）
    trendData: {
      type: Object,
      value: null
    },
    // 标签（progress类型）
    label: {
      type: String,
      value: ''
    },
    // 当前值（progress类型）
    current: {
      type: Number,
      value: 0
    },
    // 总值（progress类型）
    total: {
      type: Number,
      value: 100
    },
    // 列表数据（list类型）
    listData: {
      type: Array,
      value: []
    },
    // 列表项的键字段
    keyField: {
      type: String,
      value: 'id'
    },
    // 图表类型：bar, pie, line
    chartType: {
      type: String,
      value: 'bar'
    },
    // 图表标题
    chartTitle: {
      type: String,
      value: ''
    },
    // 图表数据
    chartData: {
      type: Array,
      value: []
    },
    // 是否显示更多按钮
    showMore: {
      type: Boolean,
      value: false
    },
    // 更多按钮文本
    moreText: {
      type: String,
      value: '查看更多'
    },
    // 底部文本
    footerText: {
      type: String,
      value: ''
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      value: false
    },
    // 操作按钮文本
    actionText: {
      type: String,
      value: '操作'
    },
    // 空状态文本
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    formattedValue: '',
    progressPercent: 0,
    trend: null,
    isEmpty: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      this.formatValue()
      this.calculateProgress()
      this.processTrend()
      this.processChartData()
      this.checkEmpty()
    },

    /**
     * 格式化数值
     */
    formatValue() {
      const { value, cardType } = this.properties
      
      if (cardType !== 'number') return
      
      let formattedValue = ''
      
      if (typeof value === 'number') {
        if (value >= 10000) {
          formattedValue = (value / 10000).toFixed(1) + '万'
        } else if (value >= 1000) {
          formattedValue = (value / 1000).toFixed(1) + 'k'
        } else {
          formattedValue = value.toString()
        }
      } else {
        formattedValue = value.toString()
      }
      
      this.setData({ formattedValue })
    },

    /**
     * 计算进度百分比
     */
    calculateProgress() {
      const { current, total, cardType } = this.properties
      
      if (cardType !== 'progress') return
      
      const progressPercent = total > 0 ? Math.round((current / total) * 100) : 0
      this.setData({ progressPercent })
    },

    /**
     * 处理趋势数据
     */
    processTrend() {
      const { trendData, showTrend, cardType } = this.properties
      
      if (cardType !== 'number' || !showTrend || !trendData) return
      
      let trend = null
      
      if (trendData.change !== undefined) {
        const change = trendData.change
        
        if (change > 0) {
          trend = {
            type: 'up',
            icon: '↗',
            text: `+${change}%`
          }
        } else if (change < 0) {
          trend = {
            type: 'down',
            icon: '↘',
            text: `${change}%`
          }
        } else {
          trend = {
            type: 'flat',
            icon: '→',
            text: '0%'
          }
        }
      }
      
      this.setData({ trend })
    },

    /**
     * 处理图表数据
     */
    processChartData() {
      const { chartData, chartType, cardType } = this.properties
      
      if (cardType !== 'chart' || !chartData || chartData.length === 0) return
      
      if (chartType === 'bar') {
        // 计算柱状图百分比
        const maxValue = Math.max(...chartData.map(item => item.value || 0))
        const processedData = chartData.map(item => ({
          ...item,
          percent: maxValue > 0 ? (item.value / maxValue) * 100 : 0
        }))
        
        this.setData({
          chartData: processedData
        })
      }
    },

    /**
     * 检查是否为空
     */
    checkEmpty() {
      const { cardType, value, listData, chartData, current, total } = this.properties
      let isEmpty = false
      
      switch (cardType) {
        case 'number':
          isEmpty = value === 0 || value === null || value === undefined
          break
        case 'progress':
          isEmpty = current === 0 && total === 0
          break
        case 'list':
          isEmpty = !listData || listData.length === 0
          break
        case 'chart':
          isEmpty = !chartData || chartData.length === 0
          break
        default:
          isEmpty = false
      }
      
      this.setData({ isEmpty })
    },

    /**
     * 卡片点击事件
     */
    onCardTap(e) {
      this.triggerEvent('cardtap', {
        cardType: this.properties.cardType,
        value: this.properties.value,
        data: this.data
      })
    },

    /**
     * 更多按钮点击事件
     */
    onMoreTap(e) {
      e.stopPropagation()
      this.triggerEvent('moretap', {
        cardType: this.properties.cardType
      })
    },

    /**
     * 操作按钮点击事件
     */
    onActionTap(e) {
      e.stopPropagation()
      this.triggerEvent('actiontap', {
        cardType: this.properties.cardType
      })
    },

    /**
     * 更新数据
     */
    updateData(newData) {
      const updateFields = {}
      
      Object.keys(newData).forEach(key => {
        if (this.properties.hasOwnProperty(key)) {
          updateFields[key] = newData[key]
        }
      })
      
      this.setData(updateFields)
      this.init()
    },

    /**
     * 设置加载状态
     */
    setLoading(loading) {
      this.setData({ loading })
    },

    /**
     * 获取卡片数据
     */
    getCardData() {
      return {
        cardType: this.properties.cardType,
        value: this.properties.value,
        formattedValue: this.data.formattedValue,
        progressPercent: this.data.progressPercent,
        trend: this.data.trend,
        isEmpty: this.data.isEmpty
      }
    },

    /**
     * 设置趋势数据
     */
    setTrend(change, period = '') {
      const trendData = { change, period }
      this.setData({ trendData })
      this.processTrend()
    },

    /**
     * 添加列表项
     */
    addListItem(item) {
      const listData = [...this.properties.listData]
      listData.push(item)
      this.setData({ listData })
      this.checkEmpty()
    },

    /**
     * 移除列表项
     */
    removeListItem(index) {
      const listData = [...this.properties.listData]
      listData.splice(index, 1)
      this.setData({ listData })
      this.checkEmpty()
    },

    /**
     * 更新列表项
     */
    updateListItem(index, item) {
      const listData = [...this.properties.listData]
      if (index >= 0 && index < listData.length) {
        listData[index] = { ...listData[index], ...item }
        this.setData({ listData })
      }
    },

    /**
     * 设置图表数据
     */
    setChartData(data) {
      this.setData({ chartData: data })
      this.processChartData()
      this.checkEmpty()
    },

    /**
     * 格式化金额
     */
    formatMoney(amount) {
      return SalaryCalculator.formatMoney(amount)
    },

    /**
     * 计算增长率
     */
    calculateGrowthRate(current, previous) {
      return SalaryCalculator.calculateGrowthRate(current, previous)
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'value, current, total, listData, chartData': function() {
      this.init()
    },
    
    'trendData': function() {
      this.processTrend()
    },
    
    'loading': function(loading) {
      this.setData({ loading })
    }
  }
})
