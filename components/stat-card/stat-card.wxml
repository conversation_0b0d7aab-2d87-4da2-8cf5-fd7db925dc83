<!-- components/stat-card/stat-card.wxml -->
<!-- 统计卡片组件模板 -->

<view class="stat-card {{cardType}} {{size}} {{theme}}" bindtap="onCardTap">
  <!-- 卡片头部 -->
  <view class="card-header" wx:if="{{title || showMore}}">
    <view class="header-left">
      <text class="card-title" wx:if="{{title}}">{{title}}</text>
      <text class="card-subtitle" wx:if="{{subtitle}}">{{subtitle}}</text>
    </view>
    <view class="header-right" wx:if="{{showMore}}">
      <text class="more-text" bindtap="onMoreTap">{{moreText}}</text>
      <text class="more-icon">›</text>
    </view>
  </view>

  <!-- 卡片内容 -->
  <view class="card-content">
    <!-- 数值类型 -->
    <view class="stat-number" wx:if="{{cardType === 'number'}}">
      <view class="number-main">
        <text class="number-value">{{formattedValue}}</text>
        <text class="number-unit" wx:if="{{unit}}">{{unit}}</text>
      </view>
      <view class="number-trend" wx:if="{{showTrend && trend}}">
        <text class="trend-icon {{trend.type}}">{{trend.icon}}</text>
        <text class="trend-text {{trend.type}}">{{trend.text}}</text>
      </view>
    </view>

    <!-- 进度类型 -->
    <view class="stat-progress" wx:if="{{cardType === 'progress'}}">
      <view class="progress-header">
        <text class="progress-label">{{label}}</text>
        <text class="progress-value">{{current}}/{{total}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progressPercent}}%"></view>
      </view>
      <text class="progress-percent">{{progressPercent}}%</text>
    </view>

    <!-- 列表类型 -->
    <view class="stat-list" wx:if="{{cardType === 'list'}}">
      <view class="list-item" wx:for="{{listData}}" wx:key="{{keyField || 'id'}}">
        <view class="item-left">
          <view class="item-icon" wx:if="{{item.icon}}" style="background-color: {{item.color}}">
            <text class="icon {{item.icon}}"></text>
          </view>
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-desc" wx:if="{{item.description}}">{{item.description}}</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-value">{{item.value}}</text>
          <text class="item-unit" wx:if="{{item.unit}}">{{item.unit}}</text>
        </view>
      </view>
    </view>

    <!-- 图表类型 -->
    <view class="stat-chart" wx:if="{{cardType === 'chart'}}">
      <view class="chart-header" wx:if="{{chartTitle}}">
        <text class="chart-title">{{chartTitle}}</text>
      </view>
      <view class="chart-container">
        <!-- 简单柱状图 -->
        <view class="bar-chart" wx:if="{{chartType === 'bar'}}">
          <view class="bar-item" wx:for="{{chartData}}" wx:key="label">
            <view class="bar-column">
              <view 
                class="bar-fill" 
                style="height: {{item.percent}}%; background-color: {{item.color || '#007AFF'}}"
              ></view>
            </view>
            <text class="bar-label">{{item.label}}</text>
          </view>
        </view>
        
        <!-- 简单饼图 -->
        <view class="pie-chart" wx:if="{{chartType === 'pie'}}">
          <view class="pie-legend">
            <view class="legend-item" wx:for="{{chartData}}" wx:key="label">
              <view class="legend-color" style="background-color: {{item.color}}"></view>
              <text class="legend-label">{{item.label}}</text>
              <text class="legend-value">{{item.value}}</text>
            </view>
          </view>
        </view>
        
        <!-- 折线图占位 -->
        <view class="line-chart" wx:if="{{chartType === 'line'}}">
          <text class="chart-placeholder">图表功能开发中...</text>
        </view>
      </view>
    </view>

    <!-- 自定义内容 -->
    <view class="stat-custom" wx:if="{{cardType === 'custom'}}">
      <slot></slot>
    </view>
  </view>

  <!-- 卡片底部 -->
  <view class="card-footer" wx:if="{{footerText || showAction}}">
    <text class="footer-text" wx:if="{{footerText}}">{{footerText}}</text>
    <button class="footer-action" wx:if="{{showAction}}" bindtap="onActionTap">
      {{actionText}}
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="card-loading" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="card-empty" wx:if="{{!loading && isEmpty}}">
    <view class="empty-icon">📊</view>
    <text class="empty-text">{{emptyText || '暂无数据'}}</text>
  </view>
</view>
