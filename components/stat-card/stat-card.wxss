/* components/stat-card/stat-card.wxss */
/* 统计卡片组件样式 */

.stat-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 卡片尺寸 */
.stat-card.small {
  padding: 24rpx;
}

.stat-card.large {
  padding: 40rpx;
}

/* 卡片主题 */
.stat-card.primary {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  color: #ffffff;
}

.stat-card.success {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
  color: #ffffff;
}

.stat-card.warning {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
  color: #ffffff;
}

.stat-card.danger {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: #ffffff;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.header-left {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.card-subtitle {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.primary .card-title,
.success .card-title,
.warning .card-title,
.danger .card-title {
  color: #ffffff;
}

.primary .card-subtitle,
.success .card-subtitle,
.warning .card-subtitle,
.danger .card-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.more-text {
  font-size: 24rpx;
  color: #007AFF;
}

.more-icon {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: bold;
}

/* 数值类型 */
.stat-number {
  text-align: center;
}

.number-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.number-value {
  font-size: 64rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.number-unit {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.primary .number-value,
.success .number-value,
.warning .number-value,
.danger .number-value {
  color: #ffffff;
}

.primary .number-unit,
.success .number-unit,
.warning .number-unit,
.danger .number-unit {
  color: rgba(255, 255, 255, 0.8);
}

.number-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.trend-icon {
  font-size: 24rpx;
  font-weight: bold;
}

.trend-text {
  font-size: 24rpx;
  font-weight: 500;
}

.trend-icon.up,
.trend-text.up {
  color: #34C759;
}

.trend-icon.down,
.trend-text.down {
  color: #FF3B30;
}

.trend-icon.flat,
.trend-text.flat {
  color: #8E8E93;
}

/* 进度类型 */
.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.progress-value {
  font-size: 24rpx;
  color: #666;
}

.progress-bar {
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background: #007AFF;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-percent {
  text-align: center;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 列表类型 */
.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007AFF;
}

.icon {
  color: #ffffff;
  font-size: 24rpx;
}

.item-info {
  flex: 1;
}

.item-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.item-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.item-right {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.item-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.item-unit {
  font-size: 22rpx;
  color: #666;
}

/* 图表类型 */
.chart-header {
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.chart-container {
  min-height: 200rpx;
}

/* 柱状图 */
.bar-chart {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200rpx;
  padding: 0 16rpx;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80rpx;
}

.bar-column {
  width: 32rpx;
  height: 160rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  position: relative;
  margin-bottom: 12rpx;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #007AFF;
  border-radius: 4rpx;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

/* 饼图图例 */
.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
}

.legend-label {
  flex: 1;
  font-size: 24rpx;
  color: #333;
}

.legend-value {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
}

/* 图表占位 */
.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  font-size: 24rpx;
  color: #adb5bd;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 2rpx solid #f0f0f0;
}

.footer-text {
  font-size: 22rpx;
  color: #666;
}

.footer-action {
  padding: 12rpx 24rpx;
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.footer-action:active {
  background: #0056CC;
}

/* 加载和空状态 */
.card-loading,
.card-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.loading-text {
  font-size: 24rpx;
  color: #adb5bd;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 24rpx;
  color: #adb5bd;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .number-value {
    font-size: 48rpx;
  }
  
  .bar-chart {
    height: 160rpx;
  }
  
  .bar-column {
    height: 120rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .stat-card {
    background: #2a2a2a;
    color: #ffffff;
  }
  
  .card-title {
    color: #ffffff;
  }
  
  .card-subtitle {
    color: #adb5bd;
  }
  
  .number-value {
    color: #ffffff;
  }
  
  .number-unit {
    color: #adb5bd;
  }
  
  .progress-bar {
    background: #404040;
  }
  
  .list-item {
    border-bottom-color: #404040;
  }
  
  .item-name {
    color: #ffffff;
  }
  
  .item-desc {
    color: #adb5bd;
  }
  
  .item-value {
    color: #ffffff;
  }
  
  .card-footer {
    border-top-color: #404040;
  }
}
