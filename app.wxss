/**app.wxss**/
/* 全局样式和主题系统 */

/* CSS变量定义 */
page {
  /* 主色调 */
  --primary-color: #007aff;
  --primary-light: #5ac8fa;
  --primary-dark: #0056cc;

  /* 辅助色 */
  --secondary-color: #34c759;
  --warning-color: #ff9500;
  --error-color: #ff3b30;
  --info-color: #5856d6;

  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #cccccc;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f0f0f0;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 边框色 */
  --border-light: #e9ecef;
  --border-medium: #dee2e6;
  --border-dark: #adb5bd;

  /* 阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --radius-small: 8rpx;
  --radius-medium: 12rpx;
  --radius-large: 16rpx;
  --radius-xl: 24rpx;

  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;

  /* 字体大小 */
  --font-xs: 20rpx;
  --font-sm: 24rpx;
  --font-md: 28rpx;
  --font-lg: 32rpx;
  --font-xl: 36rpx;
  --font-2xl: 40rpx;

  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* 基础样式 */
  background-color: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  font-size: var(--font-md);
  line-height: 1.6;
  color: var(--text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
  page {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --text-disabled: #666666;

    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;

    --border-light: #404040;
    --border-medium: #555555;
    --border-dark: #666666;
  }
}

/* 基础重置 */
* {
  box-sizing: border-box;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007aff;
  color: #ffffff;
}

.btn-primary:active {
  background: #0056cc;
}

.btn-secondary {
  background: #f1f3f4;
  color: #333;
}

.btn-secondary:active {
  background: #e8eaed;
}

.btn-success {
  background: #34c759;
  color: #ffffff;
}

.btn-success:active {
  background: #28a745;
}

.btn-danger {
  background: #ff3b30;
  color: #ffffff;
}

.btn-danger:active {
  background: #dc3545;
}

/* 表单样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007aff;
  outline: none;
}

/* 文本样式 */
.text-primary {
  color: #007aff;
}

.text-success {
  color: #34c759;
}

.text-warning {
  color: #ff9500;
}

.text-danger {
  color: #ff3b30;
}

.text-muted {
  color: #6c757d;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-1 {
  margin-top: 16rpx;
}
.mt-2 {
  margin-top: 32rpx;
}
.mt-3 {
  margin-top: 48rpx;
}
.mb-1 {
  margin-bottom: 16rpx;
}
.mb-2 {
  margin-bottom: 32rpx;
}
.mb-3 {
  margin-bottom: 48rpx;
}
.ml-1 {
  margin-left: 16rpx;
}
.ml-2 {
  margin-left: 32rpx;
}
.mr-1 {
  margin-right: 16rpx;
}
.mr-2 {
  margin-right: 32rpx;
}

.p-1 {
  padding: 16rpx;
}
.p-2 {
  padding: 32rpx;
}
.p-3 {
  padding: 48rpx;
}

/* 工作类型色彩 */
.work-type-live {
  background: #ff3b30;
}

.work-type-flyer {
  background: #ff9500;
}

.work-type-tutor {
  background: #34c759;
}

.work-type-delivery {
  background: #007aff;
}

.work-type-other {
  background: #af52de;
}

/* 状态指示器 */
.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  display: inline-block;
  margin-right: 12rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #6c757d;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  color: #6c757d;
  text-align: center;
}

.empty-state .icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-state .text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10rpx);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(10rpx);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) ease-out;
}

.animate-slide-in-up {
  animation: slideInUp var(--duration-normal) ease-out;
}

.animate-slide-in-down {
  animation: slideInDown var(--duration-normal) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) ease-out;
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 过渡效果 */
.transition-all {
  transition: all var(--duration-normal) ease;
}

.transition-colors {
  transition: color var(--duration-normal) ease,
    background-color var(--duration-normal) ease,
    border-color var(--duration-normal) ease;
}

.transition-opacity {
  transition: opacity var(--duration-normal) ease;
}

.transition-transform {
  transition: transform var(--duration-normal) ease;
}

/* 交互状态 */
.interactive {
  transition: all var(--duration-fast) ease;
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-medium);
}

.interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-light);
}

/* 触觉反馈 */
.haptic-light:active {
  transform: scale(0.98);
}

.haptic-medium:active {
  transform: scale(0.95);
}

.haptic-heavy:active {
  transform: scale(0.92);
}

/* 加载状态增强 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 8rpx;
}

.loading-dots::after {
  content: "";
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--primary-color);
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-dots::before {
  content: "";
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--primary-color);
  animation: pulse 1.5s ease-in-out infinite 0.5s;
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-tertiary) 25%,
    var(--border-light) 50%,
    var(--bg-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1em;
  border-radius: 4rpx;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.skeleton-button {
  height: 80rpx;
  border-radius: var(--radius-medium);
}

/* 工具类 */
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 响应式工具 */
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-100 {
  opacity: 1;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}

/* 无障碍访问 */
.focus-visible:focus {
  outline: 2rpx solid var(--primary-color);
  outline-offset: 2rpx;
}

.focus-visible:focus:not(:focus-visible) {
  outline: none;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .btn {
    border: 2rpx solid currentColor;
  }

  .card {
    border: 2rpx solid var(--border-dark);
  }
}
