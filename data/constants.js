// data/constants.js
// 数据层常量定义

// 存储键名常量
export const STORAGE_KEYS = {
  WORK_RECORDS: 'work_records',
  WORK_TYPES: 'work_types', 
  USER_SETTINGS: 'user_settings',
  APP_VERSION: 'app_version'
}

// 工作类型常量
export const WORK_TYPE_IDS = {
  LIVE_STREAMING: 1,
  FLYER_DISTRIBUTION: 2,
  TUTORING: 3,
  DELIVERY: 4,
  OTHER: 5
}

// 工作类型颜色映射
export const WORK_TYPE_COLORS = {
  [WORK_TYPE_IDS.LIVE_STREAMING]: '#FF3B30',
  [WORK_TYPE_IDS.FLYER_DISTRIBUTION]: '#FF9500',
  [WORK_TYPE_IDS.TUTORING]: '#34C759',
  [WORK_TYPE_IDS.DELIVERY]: '#007AFF',
  [WORK_TYPE_IDS.OTHER]: '#AF52DE'
}

// 薪资计算类型
export const SALARY_TYPES = {
  HOURLY: 'hourly',      // 时薪
  DAILY: 'daily',        // 日薪
  PROJECT: 'project',    // 项目薪资
  PIECE: 'piece'         // 计件薪资
}

// 工作记录状态
export const WORK_RECORD_STATUS = {
  PLANNED: 'planned',     // 计划中
  IN_PROGRESS: 'in_progress', // 进行中
  COMPLETED: 'completed', // 已完成
  CANCELLED: 'cancelled'  // 已取消
}

// 统计时间范围
export const STAT_TIME_RANGE = {
  WEEK: 'week',
  MONTH: 'month',
  QUARTER: 'quarter',
  YEAR: 'year'
}

// 默认工作类型配置
export const DEFAULT_WORK_TYPES = [
  {
    id: WORK_TYPE_IDS.LIVE_STREAMING,
    name: '兼职直播',
    icon: 'fas fa-video',
    color: WORK_TYPE_COLORS[WORK_TYPE_IDS.LIVE_STREAMING],
    defaultHourlyRate: 50,
    defaultSalaryType: SALARY_TYPES.HOURLY,
    isDefault: true,
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now()
  },
  {
    id: WORK_TYPE_IDS.FLYER_DISTRIBUTION,
    name: '超市发传单',
    icon: 'fas fa-bullhorn',
    color: WORK_TYPE_COLORS[WORK_TYPE_IDS.FLYER_DISTRIBUTION],
    defaultHourlyRate: 20,
    defaultSalaryType: SALARY_TYPES.HOURLY,
    isDefault: true,
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now()
  },
  {
    id: WORK_TYPE_IDS.TUTORING,
    name: '家教辅导',
    icon: 'fas fa-graduation-cap',
    color: WORK_TYPE_COLORS[WORK_TYPE_IDS.TUTORING],
    defaultHourlyRate: 80,
    defaultSalaryType: SALARY_TYPES.HOURLY,
    isDefault: true,
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now()
  },
  {
    id: WORK_TYPE_IDS.DELIVERY,
    name: '外卖配送',
    icon: 'fas fa-motorcycle',
    color: WORK_TYPE_COLORS[WORK_TYPE_IDS.DELIVERY],
    defaultHourlyRate: 25,
    defaultSalaryType: SALARY_TYPES.HOURLY,
    isDefault: true,
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now()
  },
  {
    id: WORK_TYPE_IDS.OTHER,
    name: '其他工作',
    icon: 'fas fa-briefcase',
    color: WORK_TYPE_COLORS[WORK_TYPE_IDS.OTHER],
    defaultHourlyRate: 30,
    defaultSalaryType: SALARY_TYPES.HOURLY,
    isDefault: true,
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now()
  }
]

// 数据验证规则
export const VALIDATION_RULES = {
  WORK_RECORD: {
    TITLE_MAX_LENGTH: 50,
    DESCRIPTION_MAX_LENGTH: 200,
    MIN_DURATION_MINUTES: 1,
    MAX_DURATION_HOURS: 24,
    MIN_SALARY: 0,
    MAX_SALARY: 99999
  },
  WORK_TYPE: {
    NAME_MAX_LENGTH: 20,
    NAME_MIN_LENGTH: 1,
    MIN_HOURLY_RATE: 0,
    MAX_HOURLY_RATE: 9999
  }
}

// 错误码定义
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',
  DUPLICATE_DATA: 'DUPLICATE_DATA',
  PERMISSION_DENIED: 'PERMISSION_DENIED'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  WORK_RECORD_CREATED: '工作记录创建成功',
  WORK_RECORD_UPDATED: '工作记录更新成功',
  WORK_RECORD_DELETED: '工作记录删除成功',
  WORK_TYPE_CREATED: '工作类型创建成功',
  WORK_TYPE_UPDATED: '工作类型更新成功',
  WORK_TYPE_DELETED: '工作类型删除成功'
}

// 错误消息
export const ERROR_MESSAGES = {
  INVALID_DATE: '日期格式不正确',
  INVALID_TIME: '时间格式不正确',
  INVALID_SALARY: '薪资金额不正确',
  WORK_TYPE_NOT_FOUND: '工作类型不存在',
  WORK_RECORD_NOT_FOUND: '工作记录不存在',
  STORAGE_FAILED: '数据存储失败',
  NETWORK_ERROR: '网络连接失败'
}
