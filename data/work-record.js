// data/work-record.js
// 工作记录数据模型

import { SALARY_TYPES, WORK_RECORD_STATUS, VALIDATION_RULES } from './constants.js'

/**
 * 工作记录数据模型类
 */
export class WorkRecord {
  constructor(data = {}) {
    this.id = data.id || this.generateId()
    this.workTypeId = data.workTypeId || null
    this.title = data.title || ''
    this.description = data.description || ''
    this.date = data.date || new Date().toISOString().split('T')[0] // YYYY-MM-DD
    this.startTime = data.startTime || '09:00'
    this.endTime = data.endTime || '17:00'
    this.duration = data.duration || 0 // 分钟
    this.salaryType = data.salaryType || SALARY_TYPES.HOURLY
    this.hourlyRate = data.hourlyRate || 0
    this.totalSalary = data.totalSalary || 0
    this.actualSalary = data.actualSalary || null // 实际收到的薪资
    this.status = data.status || WORK_RECORD_STATUS.PLANNED
    this.location = data.location || ''
    this.notes = data.notes || ''
    this.tags = data.tags || []
    this.isMultiDay = data.isMultiDay || false
    this.endDate = data.endDate || null // 多日工作的结束日期
    this.createdAt = data.createdAt || Date.now()
    this.updatedAt = data.updatedAt || Date.now()
    
    // 计算字段
    this.calculateDuration()
    this.calculateSalary()
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 计算工作时长（分钟）
   */
  calculateDuration() {
    if (this.startTime && this.endTime) {
      const start = this.parseTime(this.startTime)
      const end = this.parseTime(this.endTime)
      
      if (start && end) {
        let duration = (end.hours * 60 + end.minutes) - (start.hours * 60 + start.minutes)
        if (duration < 0) {
          duration += 24 * 60 // 跨天处理
        }
        this.duration = duration
      }
    }
    return this.duration
  }

  /**
   * 解析时间字符串
   */
  parseTime(timeStr) {
    const match = timeStr.match(/^(\d{1,2}):(\d{2})$/)
    if (match) {
      return {
        hours: parseInt(match[1]),
        minutes: parseInt(match[2])
      }
    }
    return null
  }

  /**
   * 计算薪资
   */
  calculateSalary() {
    switch (this.salaryType) {
      case SALARY_TYPES.HOURLY:
        this.totalSalary = (this.duration / 60) * this.hourlyRate
        break
      case SALARY_TYPES.DAILY:
        this.totalSalary = this.hourlyRate // 这里hourlyRate实际是日薪
        break
      case SALARY_TYPES.PROJECT:
        this.totalSalary = this.hourlyRate // 这里hourlyRate实际是项目薪资
        break
      case SALARY_TYPES.PIECE:
        this.totalSalary = this.hourlyRate // 这里hourlyRate实际是计件单价
        break
      default:
        this.totalSalary = 0
    }
    
    // 保留两位小数
    this.totalSalary = Math.round(this.totalSalary * 100) / 100
    return this.totalSalary
  }

  /**
   * 验证工作记录数据
   */
  validate() {
    const errors = []

    // 验证工作类型
    if (!this.workTypeId) {
      errors.push('请选择工作类型')
    }

    // 验证日期
    if (!this.date || !this.isValidDate(this.date)) {
      errors.push('请选择正确的日期')
    }

    // 验证时间
    if (!this.startTime || !this.endTime) {
      errors.push('请设置开始和结束时间')
    }

    if (this.startTime && this.endTime) {
      const start = this.parseTime(this.startTime)
      const end = this.parseTime(this.endTime)
      
      if (!start || !end) {
        errors.push('时间格式不正确')
      } else if (start.hours === end.hours && start.minutes === end.minutes) {
        errors.push('开始时间和结束时间不能相同')
      }
    }

    // 验证工作时长
    if (this.duration < VALIDATION_RULES.WORK_RECORD.MIN_DURATION_MINUTES) {
      errors.push('工作时长不能少于1分钟')
    }
    if (this.duration > VALIDATION_RULES.WORK_RECORD.MAX_DURATION_HOURS * 60) {
      errors.push('工作时长不能超过24小时')
    }

    // 验证薪资
    if (this.hourlyRate < VALIDATION_RULES.WORK_RECORD.MIN_SALARY) {
      errors.push('薪资不能小于0')
    }
    if (this.hourlyRate > VALIDATION_RULES.WORK_RECORD.MAX_SALARY) {
      errors.push('薪资金额过大')
    }

    // 验证标题长度
    if (this.title && this.title.length > VALIDATION_RULES.WORK_RECORD.TITLE_MAX_LENGTH) {
      errors.push(`标题不能超过${VALIDATION_RULES.WORK_RECORD.TITLE_MAX_LENGTH}个字符`)
    }

    // 验证描述长度
    if (this.description && this.description.length > VALIDATION_RULES.WORK_RECORD.DESCRIPTION_MAX_LENGTH) {
      errors.push(`描述不能超过${VALIDATION_RULES.WORK_RECORD.DESCRIPTION_MAX_LENGTH}个字符`)
    }

    // 验证多日工作
    if (this.isMultiDay && (!this.endDate || !this.isValidDate(this.endDate))) {
      errors.push('多日工作请设置结束日期')
    }

    if (this.isMultiDay && this.endDate && this.date >= this.endDate) {
      errors.push('结束日期必须晚于开始日期')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证日期格式
   */
  isValidDate(dateStr) {
    const regex = /^\d{4}-\d{2}-\d{2}$/
    if (!regex.test(dateStr)) return false
    
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date)
  }

  /**
   * 转换为存储格式
   */
  toStorage() {
    return {
      id: this.id,
      workTypeId: this.workTypeId,
      title: this.title,
      description: this.description,
      date: this.date,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.duration,
      salaryType: this.salaryType,
      hourlyRate: this.hourlyRate,
      totalSalary: this.totalSalary,
      actualSalary: this.actualSalary,
      status: this.status,
      location: this.location,
      notes: this.notes,
      tags: this.tags,
      isMultiDay: this.isMultiDay,
      endDate: this.endDate,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }

  /**
   * 从存储格式创建实例
   */
  static fromStorage(data) {
    return new WorkRecord(data)
  }

  /**
   * 更新工作记录
   */
  update(data) {
    const updatableFields = [
      'workTypeId', 'title', 'description', 'date', 'startTime', 'endTime',
      'salaryType', 'hourlyRate', 'actualSalary', 'status', 'location',
      'notes', 'tags', 'isMultiDay', 'endDate'
    ]

    updatableFields.forEach(field => {
      if (data[field] !== undefined) {
        this[field] = data[field]
      }
    })

    this.updatedAt = Date.now()
    this.calculateDuration()
    this.calculateSalary()
    return this
  }

  /**
   * 标记为完成
   */
  markCompleted() {
    this.status = WORK_RECORD_STATUS.COMPLETED
    this.updatedAt = Date.now()
    return this
  }

  /**
   * 标记为取消
   */
  markCancelled() {
    this.status = WORK_RECORD_STATUS.CANCELLED
    this.updatedAt = Date.now()
    return this
  }

  /**
   * 获取工作时长（小时）
   */
  getDurationHours() {
    return Math.round((this.duration / 60) * 100) / 100
  }

  /**
   * 获取格式化的工作时长
   */
  getFormattedDuration() {
    const hours = Math.floor(this.duration / 60)
    const minutes = this.duration % 60
    
    if (hours === 0) {
      return `${minutes}分钟`
    } else if (minutes === 0) {
      return `${hours}小时`
    } else {
      return `${hours}小时${minutes}分钟`
    }
  }

  /**
   * 获取日期范围（用于多日工作）
   */
  getDateRange() {
    if (!this.isMultiDay || !this.endDate) {
      return [this.date]
    }

    const dates = []
    const start = new Date(this.date)
    const end = new Date(this.endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0])
    }
    
    return dates
  }

  /**
   * 克隆工作记录
   */
  clone() {
    const cloned = new WorkRecord(this.toStorage())
    cloned.id = cloned.generateId()
    cloned.status = WORK_RECORD_STATUS.PLANNED
    cloned.actualSalary = null
    cloned.createdAt = Date.now()
    cloned.updatedAt = Date.now()
    return cloned
  }

  /**
   * 是否是今天的工作
   */
  isToday() {
    const today = new Date().toISOString().split('T')[0]
    return this.date === today
  }

  /**
   * 是否是本周的工作
   */
  isThisWeek() {
    const today = new Date()
    const recordDate = new Date(this.date)
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6))
    
    return recordDate >= startOfWeek && recordDate <= endOfWeek
  }

  /**
   * 是否是本月的工作
   */
  isThisMonth() {
    const today = new Date()
    const recordDate = new Date(this.date)
    
    return recordDate.getFullYear() === today.getFullYear() &&
           recordDate.getMonth() === today.getMonth()
  }
}
