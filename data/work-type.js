// data/work-type.js
// 工作类型数据模型

import { SALARY_TYPES, VALIDATION_RULES, ERROR_CODES } from './constants.js'

/**
 * 工作类型数据模型类
 */
export class WorkType {
  constructor(data = {}) {
    this.id = data.id || this.generateId()
    this.name = data.name || ''
    this.icon = data.icon || 'fas fa-briefcase'
    this.color = data.color || '#AF52DE'
    this.defaultHourlyRate = data.defaultHourlyRate || 0
    this.defaultSalaryType = data.defaultSalaryType || SALARY_TYPES.HOURLY
    this.description = data.description || ''
    this.isDefault = data.isDefault || false
    this.isActive = data.isActive !== undefined ? data.isActive : true
    this.createdAt = data.createdAt || Date.now()
    this.updatedAt = data.updatedAt || Date.now()
    
    // 统计数据（运行时计算）
    this.totalRecords = data.totalRecords || 0
    this.totalHours = data.totalHours || 0
    this.totalEarnings = data.totalEarnings || 0
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 验证工作类型数据
   */
  validate() {
    const errors = []

    // 验证名称
    if (!this.name || this.name.trim().length === 0) {
      errors.push('工作类型名称不能为空')
    }
    if (this.name.length > VALIDATION_RULES.WORK_TYPE.NAME_MAX_LENGTH) {
      errors.push(`工作类型名称不能超过${VALIDATION_RULES.WORK_TYPE.NAME_MAX_LENGTH}个字符`)
    }

    // 验证时薪
    if (this.defaultHourlyRate < VALIDATION_RULES.WORK_TYPE.MIN_HOURLY_RATE) {
      errors.push('默认时薪不能小于0')
    }
    if (this.defaultHourlyRate > VALIDATION_RULES.WORK_TYPE.MAX_HOURLY_RATE) {
      errors.push(`默认时薪不能超过${VALIDATION_RULES.WORK_TYPE.MAX_HOURLY_RATE}`)
    }

    // 验证颜色格式
    if (!/^#[0-9A-F]{6}$/i.test(this.color)) {
      errors.push('颜色格式不正确')
    }

    // 验证薪资类型
    if (!Object.values(SALARY_TYPES).includes(this.defaultSalaryType)) {
      errors.push('薪资类型不正确')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 转换为存储格式
   */
  toStorage() {
    return {
      id: this.id,
      name: this.name,
      icon: this.icon,
      color: this.color,
      defaultHourlyRate: this.defaultHourlyRate,
      defaultSalaryType: this.defaultSalaryType,
      description: this.description,
      isDefault: this.isDefault,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }

  /**
   * 从存储格式创建实例
   */
  static fromStorage(data) {
    return new WorkType(data)
  }

  /**
   * 更新工作类型
   */
  update(data) {
    if (data.name !== undefined) this.name = data.name
    if (data.icon !== undefined) this.icon = data.icon
    if (data.color !== undefined) this.color = data.color
    if (data.defaultHourlyRate !== undefined) this.defaultHourlyRate = data.defaultHourlyRate
    if (data.defaultSalaryType !== undefined) this.defaultSalaryType = data.defaultSalaryType
    if (data.description !== undefined) this.description = data.description
    if (data.isActive !== undefined) this.isActive = data.isActive
    
    this.updatedAt = Date.now()
    return this
  }

  /**
   * 软删除（设置为不活跃）
   */
  softDelete() {
    this.isActive = false
    this.updatedAt = Date.now()
    return this
  }

  /**
   * 恢复（设置为活跃）
   */
  restore() {
    this.isActive = true
    this.updatedAt = Date.now()
    return this
  }

  /**
   * 更新统计数据
   */
  updateStats(totalRecords, totalHours, totalEarnings) {
    this.totalRecords = totalRecords || 0
    this.totalHours = totalHours || 0
    this.totalEarnings = totalEarnings || 0
    return this
  }

  /**
   * 获取显示名称
   */
  getDisplayName() {
    return this.name
  }

  /**
   * 获取显示颜色
   */
  getDisplayColor() {
    return this.isActive ? this.color : '#cccccc'
  }

  /**
   * 是否可以删除
   */
  canDelete() {
    return !this.isDefault && this.totalRecords === 0
  }

  /**
   * 克隆工作类型
   */
  clone() {
    const cloned = new WorkType(this.toStorage())
    cloned.id = cloned.generateId()
    cloned.name = `${this.name} 副本`
    cloned.isDefault = false
    cloned.createdAt = Date.now()
    cloned.updatedAt = Date.now()
    return cloned
  }
}

/**
 * 工作类型管理器
 */
export class WorkTypeManager {
  constructor() {
    this.workTypes = new Map()
  }

  /**
   * 添加工作类型
   */
  add(workType) {
    if (!(workType instanceof WorkType)) {
      throw new Error('参数必须是WorkType实例')
    }

    const validation = workType.validate()
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '))
    }

    this.workTypes.set(workType.id, workType)
    return workType
  }

  /**
   * 获取工作类型
   */
  get(id) {
    return this.workTypes.get(id)
  }

  /**
   * 获取所有活跃的工作类型
   */
  getActive() {
    return Array.from(this.workTypes.values()).filter(wt => wt.isActive)
  }

  /**
   * 获取所有工作类型
   */
  getAll() {
    return Array.from(this.workTypes.values())
  }

  /**
   * 删除工作类型
   */
  delete(id) {
    const workType = this.workTypes.get(id)
    if (!workType) {
      throw new Error('工作类型不存在')
    }

    if (!workType.canDelete()) {
      throw new Error('该工作类型不能删除')
    }

    this.workTypes.delete(id)
    return true
  }

  /**
   * 按名称查找工作类型
   */
  findByName(name) {
    return Array.from(this.workTypes.values()).find(wt => wt.name === name)
  }

  /**
   * 获取默认工作类型
   */
  getDefaults() {
    return Array.from(this.workTypes.values()).filter(wt => wt.isDefault)
  }

  /**
   * 清空所有工作类型
   */
  clear() {
    this.workTypes.clear()
  }

  /**
   * 获取工作类型数量
   */
  count() {
    return this.workTypes.size
  }

  /**
   * 获取活跃工作类型数量
   */
  countActive() {
    return this.getActive().length
  }
}
